using JieNor.Framework.Watcher;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 业务配置文件监控服务接口定义
    /// </summary>
    public interface IBizConfigFileMonitor
    {
        /// <summary>
        /// 注册监控者
        /// </summary>
        /// <param name="container">服务容器</param>
        /// <param name="monitorDir">监控的目录，默认为站点根目录</param>
        void RegisterMonitorClient(IServiceContainer container, string monitorDir);
    }
}