using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormMeta;
using System.Collections.Concurrent;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.SQL.Exception;

namespace JieNor.Framework.AppService
{
    /// <summary>
    /// 过滤方案服务实现
    /// </summary>
    [InjectService]
    public class FilterSchemeService : IFilterSchemeService
    {
        /// <summary>
        /// 加载过滤方案
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        //[PerfMonitor]
        public List<FilterSchemeObject> LoadFilterScheme(UserContext ctx, string formId)
        {
            List<FilterSchemeObject> lstFilterSchemes = new List<FilterSchemeObject>();
            var metaService = ctx.Container.GetService<IMetaModelService>();
            var schemeMetadata = metaService?.LoadFormModel(ctx, "bas_filterscheme");

            if (schemeMetadata != null)
            {
                var _dmFilterScheme = ctx.GetDefaultDataManager(schemeMetadata);
                var pkIdSqlReader = ctx.GetPkIdDataReader(schemeMetadata,
                    $@"fbillformid=@billFormId and (fmainorgid='' or fmainorgid is null or fmainorgid=@currentCompanyId) 
                    and (fuserid=@currentUserId or fmainorgid='')",
                    new SqlParam[]
                    {
                        new SqlParam("billFormId", System.Data.DbType.String,formId),
                        new SqlParam("currentCompanyId", System.Data.DbType.String,ctx.Company),
                        new SqlParam("currentUserId", System.Data.DbType.String,ctx.UserId),
                    });
                var allFilterSchemes = _dmFilterScheme.SelectBy(pkIdSqlReader)
                    .OfType<DynamicObject>();
                lstFilterSchemes.AddRange(allFilterSchemes.Select(o =>
                {
                    var f = Convert.ToString(o["ffilterdata"]).FromJson<FilterSchemeObject>();
                    f.Id = Convert.ToString(o["Id"]);
                    f.UserId = Convert.ToString(o["fuserid"]);
                    f.MainOrgId = Convert.ToString(o["fmainorgid"]);
                    f.IsPreset = Convert.ToBoolean(o["fispreset"]);
                    f.IsShare = false;
                    f.ShareUser = "";
                    f.ShareUser_txt = "";
                    f.Order = Convert.ToInt32(o["forder"]);
                    f.SumExpression = Convert.ToString(o["fsumexpr"]);
                    var strFilter = Convert.ToString(o["ffilterstring"]);
                    if (!strFilter.IsNullOrEmptyOrWhiteSpace())
                    {
                        f.FilterString = strFilter;
                    }
                    return f;
                }).OrderBy(a => a.Order).ToList());
            }

            //如果都没有预置，则所有方案也不添加
            if (lstFilterSchemes.Any())
            {
                var _defFilterScheme = lstFilterSchemes.FirstOrDefault(o => o.Id.EndsWithIgnoreCase("__default"));

                if (_defFilterScheme == null)
                {
                    var formMeta = metaService.LoadFormModel(ctx, formId);

                    var seqService = ctx.Container.GetService<ISequenceService>();
                    var strFilterSchemeId = seqService.GetSequence<string>();

                    if (formMeta?.NoFilterSchemeAll == false)
                    {
                        //强制添加一个叫所有的方案
                        lstFilterSchemes.Insert(0, new FilterSchemeObject()
                        {
                            Id = strFilterSchemeId + "__default",
                            BillFormId = formId,
                            FilterData = new List<FilterRowObject>(),
                            ColVisible = new List<QueryColVisible>(),
                            Name = "所有",
                            IsShare = false,
                            IsPreset = true,
                        });
                    }
                }
                else
                {
                    //确保默认方案在首位
                    lstFilterSchemes.Remove(_defFilterScheme);
                    lstFilterSchemes.Insert(0, _defFilterScheme);
                }
            }
            return lstFilterSchemes;
        }

        /// <summary>
        /// 保存过滤方案
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="scheme"></param>
        //[PerfMonitor]
        public void SaveFilterScheme(UserContext ctx, string formId, FilterSchemeObject scheme)
        {
            var session = ctx;

            var metaService = ctx.Container.GetService<IMetaModelService>();
            var schemeMetadata = metaService?.LoadFormModel(ctx, "bas_filterscheme");
            if (schemeMetadata == null || session == null)
            {
                throw new InvalidOperationException("当前会话不可用！");
            }
            // 保存时使用原数据
            foreach (var filterRowObject in scheme.FilterData)
            {
                filterRowObject.IsRawData = true;
            }

            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, schemeMetadata.GetDynamicObjectType(ctx));
            DynamicObject existFilterScheme = null;

            if (!scheme.Id.IsEmptyPrimaryKey())
            {
                existFilterScheme = dm.Select(scheme.Id) as DynamicObject;
            }
            if (existFilterScheme == null)
            {
                var seqService = ctx.Container.GetService<ISequenceService>();
                existFilterScheme = dm.DataEntityType.CreateInstance() as DynamicObject;
                existFilterScheme["Id"] = seqService.GetSequence<string>();
                if (!scheme.Id.IsEmptyPrimaryKey())
                {
                    existFilterScheme["Id"] = scheme.Id;
                }
                existFilterScheme["fformid"] = "bas_filterscheme";
                existFilterScheme["fuserid"] = session.UserId;
                existFilterScheme["fbillformid"] = scheme.BillFormId.IsNullOrEmptyOrWhiteSpace() ? formId : scheme.BillFormId;
                existFilterScheme["fmainorgid"] = ctx.Company;
            }
            existFilterScheme["fname"] = scheme.Name;
            existFilterScheme["fispreset"] = scheme.IsPreset;
            existFilterScheme["forder"] = scheme.Order;
            existFilterScheme["ffilterdata"] = scheme.ToJson();
            existFilterScheme["ffilterstring"] = scheme.FilterString;
            existFilterScheme["fsumexpr"] = scheme.SumExpression;

            this.CheckPermission(ctx, formId, existFilterScheme);

            dm.Save(existFilterScheme);

            scheme.UserId = session.UserId;
            scheme.MainOrgId = session.Company;
            scheme.Id = Convert.ToString(existFilterScheme["Id"]);
        }

        /// <summary>
        /// 分配过滤方案
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="scheme"></param>
        /// <returns></returns>
        public string AllotFilterScheme(UserContext ctx, string formId, FilterSchemeObject scheme)
        {
            var message = string.Empty;

            // 分配时使用原数据
            foreach (var filterRowObject in scheme.FilterData)
            {
                filterRowObject.IsRawData = true;
            }

            var allotUserIds = scheme?.ShareUser?.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries) ?? new string[] { };
            if (allotUserIds.Length <= 0) return message;

            var allotUserNames = scheme?.ShareUser_txt?.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries) ?? new string[] { };
            var billFormId = scheme.BillFormId.IsNullOrEmptyOrWhiteSpace() ? formId : scheme.BillFormId;

            var paramList = new List<SqlParam>
            {
                new SqlParam("@fname", System.Data.DbType.String, scheme.Name),
                new SqlParam("@fbillformid", System.Data.DbType.String, billFormId)
            };
            var where = "fname=@fname and fbillformid=@fbillformid and ";
            if (allotUserIds.Length == 1)
            {
                where += "fuserid=@fuserid";
                paramList.Add(new SqlParam("@fuserid", System.Data.DbType.String, allotUserIds[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < allotUserIds.Length; i++)
                {
                    paramNames.Add($"@uid{i}");
                    paramList.Add(new SqlParam($"@uid{i}", System.Data.DbType.String, allotUserIds[i]));
                }
                where += $"fuserid in({string.Join(",", paramNames)})";
            }

            //需要分配的用户Id
            var userIds = new List<string>();

            var metaService = ctx.Container.GetService<IMetaModelService>();
            var schemeForm = metaService?.LoadFormModel(ctx, "bas_filterscheme");
            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, schemeForm.GetDynamicObjectType(ctx));

            var reader = ctx.GetPkIdDataReader(schemeForm, where, paramList);
            var schemeObjs = dm.SelectBy(reader).OfType<DynamicObject>().ToList();
            if (schemeObjs != null && schemeObjs.Count > 0)
            {
                var userNames = new List<string>();
                foreach (var item in schemeObjs)
                {
                    var exists = false;
                    var userId = item["fuserid"] as string;
                    for (int i = 0; i < allotUserIds.Length; i++)
                    {
                        if (allotUserIds[i].EqualsIgnoreCase(userId))
                        {
                            //根据用户Id找到用户名
                            if (allotUserNames.Length > i)
                            {
                                userNames.Add(allotUserNames[i]);
                            }
                            exists = true;
                            break;
                        }
                    }
                    if (!exists)
                    {
                        userIds.Add(userId);
                    }
                }
                message = $"过滤方案【{scheme.Name}】共享给用户【{string.Join(",", userNames)}】失败：对方已存在同名过滤方案！";
            }
            else
            {
                userIds = allotUserIds.ToList();
            }

            if (userIds.Count > 0)
            {
                var seqService = ctx.Container.GetService<ISequenceService>();
                //需要分配的过滤方案
                var allotSchemeObjs = new List<DynamicObject>();
                foreach (var userId in userIds)
                {
                    var dynObj = dm.DataEntityType.CreateInstance() as DynamicObject;
                    dynObj["id"] = seqService.GetSequence<string>();
                    dynObj["fformid"] = "bas_filterscheme";
                    dynObj["fuserid"] = userId;
                    dynObj["fbillformid"] = billFormId;
                    dynObj["fmainorgid"] = ctx.Company;
                    dynObj["fname"] = scheme.Name;
                    dynObj["fispreset"] = scheme.IsPreset;
                    dynObj["forder"] = scheme.Order;
                    dynObj["ffilterdata"] = scheme.ToJson();
                    dynObj["ffilterstring"] = scheme.FilterString;
                    dynObj["fsumexpr"] = scheme.SumExpression;
                    allotSchemeObjs.Add(dynObj);
                }
                dm.Save(allotSchemeObjs);
            }

            return message;
        }

        /// <summary>
        /// 删除过滤方案
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="id"></param>
        public void DeleteFilterScheme(UserContext ctx, string formId, string id)
        {
            var session = ctx;
            var metaService = ctx.Container.GetService<IMetaModelService>();
            var schemeMetadata = metaService?.LoadFormModel(ctx, "bas_filterscheme");
            if (schemeMetadata == null || session == null)
            {
                throw new InvalidOperationException("当前会话不可用！");
            }
            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, schemeMetadata.GetDynamicObjectType(ctx));
            var filter = dm.Select(id) as DynamicObject;
            if (filter == null)
            {
                throw new BusinessException("过滤方案不存在，无法删除！");
            }
            this.CheckPermission(ctx, formId, filter);

            dm.Delete((object)id);
        }

        /// <summary>
        /// 检查是否有预置过滤方案的编辑权限
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        private void CheckPermission(UserContext ctx, string formId, DynamicObject filter)
        {
            var mainOrgId = Convert.ToString(filter["fmainorgid"]);
            if ((mainOrgId.IsNullOrEmptyOrWhiteSpace() || mainOrgId.EqualsIgnoreCase("0")) && Convert.ToBoolean(filter["fispreset"]))
            {
                var permissionService = ctx.Container.GetService<IPermissionService>();
                permissionService?.CheckPermission(ctx, new MetaCore.PermData.PermAuth(ctx)
                {
                    FormId = formId,
                    PermId = PermConst.PermssionItem_PresetFilterEdit,
                });
            }
        }

        /// <summary>
        /// 检查过滤方案是否合法（主要是检查生成后的 where 条件表达式是否符合 ksql 语法）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formId"></param>
        /// <param name="filterList"></param>
        /// <returns></returns>
        public bool CheckFilterScheme(UserContext userCtx, string formId, List<FilterRowObject> filterList)
        {
            if (formId.IsNullOrEmptyOrWhiteSpace() || filterList == null) return true;

            var bizForm = userCtx.Container.GetService<MetaModelService>().LoadFormModel(userCtx, formId);
            if (bizForm == null) return true;

            var sbFilter = new StringBuilder();
            var sqlParams = new List<SqlParam>();
            var queryInfo = QueryService.GetHtmlFormQueryMetaInfo(userCtx, bizForm);
            foreach (var filter in filterList)
            {
                var selField = queryInfo.GetSelectField(filter.Id);
                if (selField == null) continue;

                var filterStr = filter.ToFilterString(userCtx, selField, sqlParams, bizForm);
                if (filterStr.IsNullOrEmptyOrWhiteSpace()) continue;

                //第一个不需要逻辑符，类似于：fnumber=xxx and fname=xxxx
                var logic = "";
                if (sbFilter.Length > 0)
                {
                    logic = filter.Logic.EqualsIgnoreCase("or") ? "or" : "and";
                }

                sbFilter.Append($"{logic} {filterStr}");
            }
            if (sbFilter.Length <= 0) return true;

            try
            {
                //翻译sql条件
                List<string> fldKeys = null;
                var sqlParser = userCtx.Container.GetService<ISqlStringParser>();
                var whereStr = sqlParser.GetRealSqlFilter(userCtx, bizForm, sbFilter.ToString(), out fldKeys);

                return true;
            }
            catch (ParserException)
            {
                return false;
            }
        }
    }









    /// <summary>
    /// 动态构建过滤方案
    /// 比如基础资料：动态构建首字母过滤方案
    /// 比如单据：动态构建日期类的过滤方案（今天、昨天、本周、上周、本月、上月、本年、上一年等等）
    /// </summary>
    public class DynamicFilterSchemeHelper
    {



        public static List<FilterSchemeObject> BuildDynamicFilterScheme(UserContext ctx, HtmlForm htmlForm)
        {
            var filters = new List<FilterSchemeObject>();
            var dyBuilder = ctx.Container.GetAllServiceByMeta<IDynamicFilterSchemeService>("formid", htmlForm.Id);
            foreach (var item in dyBuilder)
            {
                filters.AddRange(item.BuildDynamicFilterScheme(ctx, htmlForm));
            }

            return filters;
        }



        public static List<FilterSchemeObject> BuildAlphaAndNumberSchemes(HtmlForm htmlForm, string filterViewStyle)
        {
            List<FilterSchemeObject> filterSchemes = new List<FilterSchemeObject>();
            var nameField = htmlForm.GetNameField();
            if (nameField == null)
            {
                return filterSchemes;
            }

            //默认增加一个按钮代表所有数据
            filterSchemes.Add(new FilterSchemeObject()
            {
                Id = "#",
                Name = "#",
                BillFormId = htmlForm.Id,
                FilterString = "",
                IsPreset = true
            });

            if (filterViewStyle.EqualsIgnoreCase(Enum.GetName(typeof(Enu_ListFilterViewStyle), Enu_ListFilterViewStyle.ByAlphabeticAndNumber)))
            {
                var numberFilterObj = new FilterSchemeObject()
                {
                    Id = "123",
                    Name = "123",
                    BillFormId = htmlForm.Id,
                    FilterString = "",
                    IsPreset = true
                };

                string tmpFilter = "";
                for (var chr = '0'; chr <= '9'; chr++)
                {
                    tmpFilter += $" {(tmpFilter.IsNullOrEmptyOrWhiteSpace() ? "" : " or ")} {nameField?.Id}_py = '{chr.ToString()}' ";
                }

                numberFilterObj.FilterString = $" ({tmpFilter}) ";
                filterSchemes.Add(numberFilterObj);
            }

            //按26个字母及10个数字来生成过滤方案
            for (var chr = 'A'; chr <= 'Z'; chr++)
            {
                filterSchemes.Add(new FilterSchemeObject()
                {
                    Id = chr.ToString(),
                    Name = chr.ToString(),
                    BillFormId = htmlForm.Id,
                    FilterString = $"{nameField?.Id}_py = '{chr.ToString()}'",
                    IsPreset = true
                });
            }

            return filterSchemes;
        }



    }








}







