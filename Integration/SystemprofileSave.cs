using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using static JieNor.Framework.AppService.SystemProfileService;
using JieNor.Framework;

namespace JieNor.Framework.AppService.Integration
{
    /// <summary>
    /// 问卷调查反馈明细--查看
    /// </summary>
    [InjectService]
    [FormId("sys_systemprofile")]
    [OperationNo("scrm_save")]
    public class SystemprofileSave: AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            string pkid = this.GetQueryOrSimpleParam<string>("pkid");
            string fcategory = this.GetQueryOrSimpleParam<string>("fcategory");
            string fkey = this.GetQueryOrSimpleParam<string>("fkey");
            string fvalue = this.GetQueryOrSimpleParam<string>("fvalue");
            string fdesc = this.GetQueryOrSimpleParam<string>("fdesc");
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;
            if(fcategory.IsNullOrEmptyOrWhiteSpace()
                || fkey.IsNullOrEmptyOrWhiteSpace()
                || fvalue.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = @"当前单据字段【参数类别】、【参数标识】、【参数值】为必录字段！";
                return;
            }
            if(pkid.IsNullOrEmptyOrWhiteSpace())
            {
                var dm = this.Container?.GetService<IDataManager>();
                dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
                var lstSys = dm.SelectBy("{0}.fcategory=N'{1}' and {0}.fkey='{2}' "
                    .Fmt(this.HtmlForm.BillHeadTableName, fcategory, fkey)).OfType<DynamicObject>().ToList();
                if(lstSys.Count>0)
                {
                    this.Result.IsSuccess = false;
                    this.Result.SimpleMessage = @"当前单据【参数类别】+【参数标识】唯一！";
                    return;
                }

            }

            //保存参数设置信息
            var spService = this.Container.GetService<ISystemProfile>();
            spService.CreateOrUpdateProfile(this.Context, fcategory, fkey, fvalue, fdesc);
            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "保存成功！";
        }

    }
}
