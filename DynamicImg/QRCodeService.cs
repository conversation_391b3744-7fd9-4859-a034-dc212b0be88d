using JieNor.Framework.DataTransferObject;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using ZXing;
using ZXing.Common;
using ZXing.QrCode;
using ZXing.Rendering;

namespace JieNor.Framework.Interface
{


    /// <summary>
    /// 二维码生成接口
    /// </summary>
    [InjectService]
    public class QRCodeService : IQRCodeService
    { 
        EncodingOptions qrCodeOptions = new QrCodeEncodingOptions
        {
            DisableECI = true,
            CharacterSet = "UTF-8",
            Width = 200,
            Height = 200,
        };
        EncodingOptions barCodeOptions = new EncodingOptions()
        {
            Width = 500,
            Height = 150,

        };
         
        /// <summary>
        /// 生成二维码
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="content"></param> 
        /// <returns></returns>
        public string CreateQRCode(UserContext ctx, string content, bool retLocalDir = false)
        {
            PathUtils.DeleteTempFile("prints");
             
            BarcodeWriter writer = new BarcodeWriter();
            writer.Options = qrCodeOptions;
            qrCodeOptions.Margin = 1;
            writer.Format = BarcodeFormat.QR_CODE; 
            Bitmap img = writer.Write(content);

            var path = PathUtils.GetStartupPath();
            var dir = Path.Combine(path, @"prints");
            var fileName = @"{0}.Bmp".Fmt(Guid.NewGuid());
            var file = Path.Combine(dir, fileName);
            img.Save(file);
            if (retLocalDir)
            {
                return file;
            }

            var url = "{0}prints/{1}".Fmt(ctx.AppServer.ToString(), fileName);

            return url;
        }

        /// <summary>
        /// 生成二维码
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="content"></param> 
        /// <returns></returns>
        public Stream CreateQRCodeStream(UserContext ctx, string content)
        { 
            BarcodeWriter writer = new BarcodeWriter();
            writer.Options = qrCodeOptions;
            qrCodeOptions.Margin = 1;
            writer.Format = BarcodeFormat.QR_CODE;  
            Bitmap img = writer.Write(content);
            MemoryStream stream = new MemoryStream();
            img.Save(stream, ImageFormat.Bmp);
            return stream;
        }


        /// <summary>
        /// 动态生成条码
        /// 应用场景：打印时的条码字段的条码生成 
        /// </summary>
        /// <param name="ctx"></param> 
        /// <returns></returns>
        public string CreateBarCode(UserContext ctx, QRCodeBuildPara para)
        {
            PathUtils.DeleteTempFile("prints", 2);

            BarcodeWriter writer = new BarcodeWriter();
            writer.Options = barCodeOptions;
            writer.Format = BarcodeFormat.CODE_128;
            //writer.Options.Width = 500;// para.Width ;
            //writer.Options.Height = (int)500.0 * para.Height / para.Width;
            writer.Options.Width = para.Width;
            writer.Options.Height = para.Height;
            BitmapRenderer render = new BitmapRenderer();
            if (para.FontBold)
            {
                render.TextFont = new Font("Microsoft YaHei", para.FontSize < 0 ? 6 : para.FontSize, FontStyle.Bold);
            }
            else
            {
                render.TextFont = new Font("Microsoft YaHei", para.FontSize < 0 ? 6 : para.FontSize);
            }
            writer.Renderer = render;
            //是否纯条码
            //writer.Options.PureBarcode = true;
            //writer.Options.Margin = 1;
            //Bitmap img = writer.Write(para.Content);
            //var xx = new ZXing.OneD.Code128Writer();
            //var result = xx.encode(para.Content, BarcodeFormat.CODE_128, 0, 0);
            var img = writer.Write(para.Content);
            if (para.Rotate90Flip)
            {
                img.RotateFlip(RotateFlipType.Rotate90FlipXY);
            }
            var path = PathUtils.GetStartupPath();
            var dir = Path.Combine(path, @"prints");
            var fileName = @"{0}.Bmp".Fmt(Guid.NewGuid().ToString().Replace("-", ""));
            var file = Path.Combine(dir, fileName);
            img.Save(file);
            if (para.RetLocalDir)
            {
                return file;
            }
            var url = "{0}prints/{1}".Fmt(ctx.AppServer.ToString(), fileName);

            return url;
        }


        /// <summary>
        /// 动态生成条码
        /// 应用场景：打印时的条码字段的条码生成 
        /// </summary>
        /// <param name="ctx"></param> 
        /// <returns></returns>
        public Stream CreateBarCodeStream(UserContext ctx, QRCodeBuildPara para)
        {
            PathUtils.DeleteTempFile("prints", 2);

            BarcodeWriter writer = new BarcodeWriter();
            writer.Options = barCodeOptions;
            writer.Format = BarcodeFormat.CODE_128;
            writer.Options.Width = para.Width;
            writer.Options.Height = para.Height;
            writer.Options.PureBarcode = para.PureBarcode;
            BitmapRenderer render = new BitmapRenderer();
            if (para.FontBold)
            {
                render.TextFont = new Font("Microsoft YaHei", para.FontSize < 0 ? 6 : para.FontSize, FontStyle.Bold);
            }
            else
            {
                render.TextFont = new Font("Microsoft YaHei", para.FontSize < 0 ? 6 : para.FontSize);
            }
            writer.Renderer = render;

            var img = writer.Write(para.Content);
            if (para.Rotate90Flip)
            {
                img.RotateFlip(RotateFlipType.Rotate90FlipXY);
            }

            MemoryStream stream = new MemoryStream();
            img.Save(stream, ImageFormat.Bmp);

            return stream;
        }



        /// <summary>
        /// 动态生成条码
        /// 应用场景：打印时的条码字段的条码生成 
        /// </summary>
        /// <param name="ctx"></param> 
        /// <returns></returns>
        public string CreateBarCodeEx(UserContext ctx, QRCodeBuildPara para)
        {
            PathUtils.DeleteTempFile("prints", 2);
            var b = new BarcodeLib.Barcode();
            b.Alignment = BarcodeLib.AlignmentPositions.CENTER;

            //是否打印码文
            b.IncludeLabel = true;
            //可以设置显示的文本信息与码文不一致 
            //b.AlternateLabel = para.Content; 
            if (para.FontBold)
            {
                b.LabelFont = new Font("宋体", para.FontSize < 11 ? 14 : para.FontSize, FontStyle.Bold);
            }
            else
            {
                b.LabelFont = new Font("宋体", para.FontSize < 11 ? 14 : para.FontSize);
            }
            //b.Width = para.Width;
            //b.Height = para.Height;
            if (para.Content.Length >= 20)
            {
                b.BarWidth = 2;
            }
            else
            {
                b.BarWidth = 3;
            }
            //码文显示位置
            b.LabelPosition = BarcodeLib.LabelPositions.BOTTOMCENTER;
            if (para.Rotate90Flip)
            {
                b.RotateFlipType = RotateFlipType.Rotate90FlipXY;
            }
            b.ImageFormat = System.Drawing.Imaging.ImageFormat.Jpeg;
            var img = b.Encode(BarcodeLib.TYPE.CODE128, para.Content);

            b.BarWidth = null;
            b.Width += 30;
            img = b.Encode(BarcodeLib.TYPE.CODE128, para.Content);
            var path = PathUtils.GetStartupPath();
            var dir = Path.Combine(path, @"prints");
            var fileName = @"{0}.JPG".Fmt(Guid.NewGuid().ToString().Replace("-", ""));
            var file = Path.Combine(dir, fileName);
            b.SaveImage(file, BarcodeLib.SaveTypes.JPG);

            img.Save(file);
            if (para.RetLocalDir)
            {
                return file;
            }
            var url = "{0}prints/{1}".Fmt(ctx.AppServer.ToString(), fileName);

            return url;
        }

    }
}
