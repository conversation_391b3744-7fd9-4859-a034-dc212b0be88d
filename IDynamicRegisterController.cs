using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 动态注册控制器
    /// </summary>
    public interface IDynamicRegisterController
    {
        /// <summary>
        /// 获取控制器所属程序集
        /// </summary>
        /// <returns></returns>
        Assembly[] GetControllerAssembly();
    }
}
