using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn.SystemIntegration;

namespace JieNor.Framework.Interface.SystemIntegration
{
    /// <summary>
    /// 同步数据到第三方系统插件
    /// </summary>
    public interface ISyncDataTo3SysPlugIn
    {
        /// <summary>
        /// 同步操作前事件
        /// </summary>
        /// <param name="e"></param>
        void BeforeSendDataToTarget(BeforeSendDataToTargetEventArgs e);

        /// <summary>
        /// 打包前事件
        /// </summary>
        /// <param name="e"></param>
        void BeforePackTargetBill(BeforePackTargetBillEventArgs e);

        /// <summary>
        /// 打包后事件
        /// </summary>
        /// <param name="e"></param>
        void AfterPackTargetBill(AfterPackTargetBillEventArgs e);

        /// <summary>
        /// 同步操作后事件
        /// </summary>
        /// <param name="e"></param>
        void AfterSendDataToTarget(AfterSendDataToTargetEventArgs e);

        /// <summary>
        /// 构建映射信息后事件
        /// </summary>
        /// <param name="e"></param>
        void AfterBuildBillMapping(AfterBuildBillMappingEventArgs e);
    }
}
