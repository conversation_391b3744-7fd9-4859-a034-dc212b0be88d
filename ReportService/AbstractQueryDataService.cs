//using JieNor.Framework.Consts;
//using JieNor.Framework.DataTransferObject.Report;
//using JieNor.Framework.Interface;
//using JieNor.Framework.Interface.QueryBuilder;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormMeta;
//using JieNor.Framework.MetaCore.FormModel.List;
//using JieNor.Framework.MetaCore.FormOp;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.SuperOrm.DataManager;
//using Newtonsoft.Json;
//using System;
//using System.Collections.Generic;
//using System.Data; 
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;


//namespace JieNor.Framework.AppService.ReportService
//{

//    /// <summary>
//    /// 数据查询服务
//    /// </summary> 
//    public abstract class AbstractQueryDataService : AbstractOperationService  //, IQueryDataService
//    {
//        protected override string OperationName
//        {
//            get
//            {
//                return "数据查询";
//            }
//        }


//        protected override string PermItem
//        {
//            get
//            {
//                return PermConst.PermssionItem_View;
//            }
//        }



//        /// <summary>
//        /// 是否office端查询
//        /// </summary>
//        protected virtual  bool IsOffice
//        {
//            get
//            {
//                return this.GetQueryOrSimpleParam("office", "false").EqualsIgnoreCase("true");
//            }
//        }

//        /// <summary>
//        /// 列表操作上下文
//        /// </summary>
//        protected ListOperationContext ListOperationContext
//        {
//            get
//            {
//                return this.OperationContext as ListOperationContext;
//            }
//        }

//        FilterSchemeObject filter = null;
//        /// <summary>
//        /// 报表过滤条件（过滤条件、排序、显示隐藏列等）：由客户端传过来
//        /// </summary>
//        protected virtual  FilterSchemeObject FilterObject
//        {
//            get
//            {
//                if (filter == null)
//                {
//                    var filterString = this.GetQueryOrSimpleParam<string>("filter", "");
//                    if (filterString.IsNullOrEmptyOrWhiteSpace())
//                    {
//                        return new FilterSchemeObject();
//                    }

//                    filter = JsonConvert.DeserializeObject<FilterSchemeObject>(filterString);
//                }
//                if (filter == null)
//                {
//                    filter = new FilterSchemeObject();
//                }
//                filter.DataTitle = this.DataTitle;
//                filter.FilterTitle = "";
//                foreach (var item in this.FilterTitle)
//                {
//                    filter.FilterTitle += item.Value + Environment.NewLine;
//                }

//                if (this.ListOperationContext != null)
//                {
//                    filter.PageIndex = this.ListOperationContext.PageIndex;
//                    filter.PageCount = this.ListOperationContext.PageCount;
//                }
//                else
//                {
//                    filter.PageIndex = 1;
//                    filter.PageCount = this.GetQueryOrSimpleParam<int>("count", 1000000); ;
//                }
//                return filter;
//            }
//        }








//        /// <summary>
//        /// 设置数据的标题：报表数据 -- 设置为报表名称，列表数据--设置为列表名称
//        /// </summary>
//        protected virtual string DataTitle
//        {
//            get
//            {
//                return "数据查询";
//            }
//            set
//            {
//            }
//        }


//        /// <summary>
//        /// 设置返回的过滤条件信息：一般来说，报表查询时，在报表查询出来的列表上方，显示一些相应的过滤条件信息
//        /// </summary>
//        public virtual  Dictionary<string, string> FilterTitle
//        {
//            get
//            {
//                return new  Dictionary<string, string>();
//            }
//        }




//        /// <summary>
//        /// 相关一些选项信息，如字段信息及其他相关系统选项、打印选项等等
//        /// </summary>
//        protected virtual Dictionary<string, string> Options
//        {
//            get
//            {
//                return new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
//            }
//        }


//        protected ColumnHeadObject  ColumnHead
//        {
//            get;
//            set;
//        }


//        /// <summary>
//        /// 从数据库获取报表模型里面定义的字段列表
//        /// </summary>
//        /// <returns></returns>
//        protected List<ColumnObject> GetRptColObjectFromModel()
//        {
//            List<ColumnObject> cols = new List<ColumnObject>();
//            string sql = string.Format(@"select FID,FName,foption 
//                                        from t_bas_officetmpl 
//                                        where fid='{0}' ", FilterObject.BillFormId);
//            using (var reader = this.DBService.ExecuteReader(this.OperationContext.UserContext, sql))
//            {
//                while (reader.Read())
//                {
//                    DataTitle = reader["FName"].ToString();
//                    if (!reader["foption"].IsNullOrEmptyOrWhiteSpace())
//                    {
//                        Dictionary<string, string> dic = JsonConvert.DeserializeObject<Dictionary<string, string>>(reader.GetValue<string>("foption"));
//                        if (dic.ContainsKey("col") && !dic["col"].IsNullOrEmptyOrWhiteSpace())
//                        {
//                            cols = JsonConvert.DeserializeObject<List<ColumnObject>>(dic["col"]);
//                        }
//                    }
//                    break;
//                }
//            }
//            cols.ForEach(f => f.DBFieldName = f.Id);

//            return cols;
//        }







//        //protected object[,] SetCaptionRow(List<ColumnObject> cols, long rowCount)
//        //{
//        //    object[,] values = new object[rowCount, cols.Count];
//        //    for (int i = 0; i < cols.Count; i++)
//        //    {
//        //        values[0, i] = cols[i].Caption;
//        //    }

//        //    return values;
//        //}




//        //protected List<ColumnObject> GetColumnInfo(List<SelectField> selectFlds)
//        //{
//        //    var allCols = this.ColumnHead.AllColumns;
//        //    var cols = new List<ColumnObject>();
//        //    foreach (var item in selectFlds)
//        //    {
//        //        var x = allCols.FirstOrDefault(f => f.Id.EqualsIgnoreCase(item.Id));
//        //        if (x != null)
//        //        {
//        //            var y = FilterObject.ColVisible.FirstOrDefault(f => f.Id.EqualsIgnoreCase(item.Id));
//        //            if (y == null || y.Hidden)
//        //            {
//        //                x.ListShowType = QueryColVisibleEnum.Hide;
//        //            }
//        //            else if (y.Visible)
//        //            {
//        //                x.ListShowType = QueryColVisibleEnum.Visible;
//        //            }
//        //            else
//        //            {
//        //                x.ListShowType = QueryColVisibleEnum.UnVisible;
//        //            }
//        //            cols.Add(x);
//        //        }
//        //    }
//        //    return cols;
//        //}


//        ///// <summary>
//        ///// 设置返回的字段：只返回显示及隐藏的字段，不显示的字段，不返回
//        ///// </summary>
//        //protected List<ColumnObject> GetColumnInfo(IDataReader reader,List<ColumnObject > columns )
//        //{
//        //    var cols = new List<ColumnObject>();
//        //    foreach (var item in columns)
//        //    {
//        //        if (FilterObject.ColVisible.Any(f => f.UnVisible && f.Id.EqualsIgnoreCase(item.Id)))
//        //        {
//        //            continue;
//        //        }

//        //        DataTable dt = reader.GetSchemaTable();
//        //        foreach (DataRow  row in dt.Rows )
//        //        {
//        //            if (row["ColumnName"].ToString ().EqualsIgnoreCase(item.DBFieldName))
//        //            {
//        //                var x = FilterObject.ColVisible.FirstOrDefault(f => f.Id.EqualsIgnoreCase(item.Id));
//        //                if(x==null || x.Visible)
//        //                {
//        //                    item.Visible = true;
//        //                    item.ListShowType = QueryColVisibleEnum.Visible;
//        //                } 
//        //                else
//        //                {
//        //                    item.ListShowType = QueryColVisibleEnum.Hide;
//        //                } 
//        //                cols.Add(item);
//        //                break;
//        //            }
//        //        }
//        //    }

//        //    return cols;
//        //}






//        ///// <summary>
//        ///// 返回查询数据：
//        ///// 注意：1、只返回显示及隐藏的字段，不显示的字段，不返回
//        /////       2、一般web页面或winform页面数据，以  List 形式返回数据
//        /////       3、office数据，以二维数组的形式返回报表数据，数组的填充，与字段的显示顺序一致，这样在excel页签就可以批量填充
//        ///// </summary>
//        ///// <param name="ctx"></param>
//        ///// <param name="filter"></param>
//        ///// <returns></returns>
//        //public virtual    QueryDataInfo QueryData(UserContext ctx, FilterSchemeObject filter, bool isOffice)
//        //{
//        //    this.filter = filter;
//        //    QueryDataInfo data =new QueryDataInfo();
//        //    data.DatasDesc    =new ListDesc();
//        //    var cols = ColumnHead.AllColumns;
//        //    if (cols == null || cols.Count ==0)
//        //    {
//        //        return data;
//        //    }
//        //    ListDesc desc;
//        //    using (var reader = BuildQueryData(out desc))
//        //    {
//        //        data = PackQueryData(reader ,desc , cols);
//        //    }

//        //    return data;
//        //}



//        /// <summary>
//        /// 返回查询数据 
//        /// </summary>
//        /// <param name="dataEntities"></param>
//        protected override void DoExecute(ref DynamicObject[] dataEntities)
//        {
//            ColumnHead = BuildColumnHeads();
//            var svc = this.Container.GetService<IQueryDataService>();
//            var datas = svc.QueryData(this.OperationContext.UserContext, FilterObject,IsOffice);
//            datas.ColHeads = this.ColumnHead;

//            //设置报表查询返回的数据
//            this.OperationContext.Result.SrvData = datas.ToJson();
//            this.OperationContext.Result.IsSuccess = true;
//        }









//        protected abstract ColumnHeadObject BuildColumnHeads();




//        ///// <summary>
//        ///// 构建查询数据 ：
//        ///// 1、出于性能考虑，要求返回IDataReader类型的数据包
//        ///// 2、返回的desc数据包，office端查询，强制要求要返回本次查询返回的记录数，
//        /////    即 ListDesc.CurrentRows 字段要赋值 ，以便构建二维数组做批量填充
//        ///// </summary>
//        //protected abstract IDataReader BuildQueryData(out ListDesc desc);


//        ///// <summary>
//        ///// 数据打包：返回查询数据
//        ///// </summary>
//        ///// <returns></returns>
//        //protected abstract QueryDataInfo PackQueryData(IDataReader reader, ListDesc desc, List<ColumnObject> cols);






//    }









//}
