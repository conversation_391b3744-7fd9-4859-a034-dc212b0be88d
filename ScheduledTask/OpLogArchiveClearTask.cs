using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Log;

namespace JieNor.Framework.AppService.ScheduledTask
{
    /// <summary>
    /// 操作日志归档清理任务
    /// </summary>
    [InjectService(AliasName = "Task")]
    [TaskSvrId("OpLogArchiveClearTask")]
    [Caption("操作日志归档清理")]
    [TaskMultiInstance()]
    [Browsable(false)]
    public class OpLogArchiveClearTask : AbstractScheduleWorker
    {
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        protected override async Task DoExecute()
        {
            await Task.Run(() =>
            {
                // 暂未实现...

                var logServiceEx = this.UserContext.Container.GetService<ILogServiceEx>();
                logServiceEx.Info("操作日志归档清理任务。");
            });
        }
    }
}