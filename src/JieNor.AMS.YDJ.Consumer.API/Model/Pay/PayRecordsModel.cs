using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.Model.Pay
{
    public class PayRecordsModel
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string billNo { get; set; }

        /// <summary>
        /// 订单用途  bizpurpose_01账户充值 bizpurpose_02订单付款  bizpurpose_03其他扣款 bizpurpose_04红冲  bizpurpose_06退款 bizpurpose_07协同账户初始化  bizpurpose_08账户转账
        /// </summary>
        public ComboDataModel purpose { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal amount { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string createDate { get; set; }
    }
}
