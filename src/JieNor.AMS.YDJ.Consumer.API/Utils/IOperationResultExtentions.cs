using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Consumer.API.Utils
{
    /// <summary>
    /// 操作结果接口扩展类
    /// </summary>
    public static class IOperationResultExtentions
    {
        public static T ToDataModel<T>(this IOperationResult result) where T : class, new()
        {
            T model = new T();

            if (result == null || !result.IsSuccess)
            {
                return model;
            }

            //将麦浩接口返回的数据结构转换为小程序接口要求的数据结构 
            var srvDataStr = result.SrvData as string;
            if (!srvDataStr.IsNullOrEmptyOrWhiteSpace())
            {
                model = JsonConvert.DeserializeObject<List<T>>(srvDataStr).FirstOrDefault();
            }

            return model;
        }

        /// <summary>
        /// 转响应数据模型
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="result">操作结果</param>
        /// <param name="isParseData">是否解析数据</param>
        /// <returns></returns>
        public static BaseResponse<T> ToResponseModel<T>(this IOperationResult result, bool isParseData = true) where T : class, new()
        {
            var resp = new BaseResponse<T>();
            if (result == null)
            {
                return resp;
            }

            if (!result.IsSuccess)
            {
                resp.Success = false;
                resp.Message = result.GetErrorMessage();
                return resp;
            }

            resp.Success = true;
            resp.Message = "操作成功！";
            if (isParseData)
            {
                resp.Data = result.ToDataModel<T>();
            }

            return resp;
        }

        /// <summary>
        /// 获取错误消息
        /// </summary> 
        /// <param name="result">操作结果</param> 
        /// <param name="defaultMsg">默认消息</param> 
        /// <returns></returns>
        public static string GetErrorMessage(this IOperationResult result, string defaultMsg = "操作失败！")
        {
            //if (result == null)
            //{
            //    return defaultVal;
            //}

            //if (result.ComplexMessage.HasMessage)
            //{
            //    return string.Join("\r\n", result.ComplexMessage.ErrorMessages);
            //}

            //if (!result.SimpleMessage.IsNullOrEmptyOrWhiteSpace())
            //{
            //    return result.SimpleMessage;
            //}

            //return defaultVal;

            return result?.ToString() ?? defaultMsg;
        }
    }
}
