using JieNor.Framework;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.AMS.YDJ.Consumer.API.Model.STE.Order;
using JieNor.Framework.Enums;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.STE.Order
{
    public class BaseOrderController : BaseController
    {
        public static SqlBuilder BuildSqlBuilder(UserContext userCtx, int pageSize, int pageIndex, string type, string keyword = null)
        {
            SqlBuilder sqlBuilder = new SqlBuilder();

            string orderBy = "a.fcreatedate desc", sqlWhere = " where a.fmainorgid=@fmainorgid and a.fcustomerid = @fcustomerid ";

            //获取订单默认取数时间
            var fapporderdate = "1900-01-01";
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            //var fapporderdateParam = profileService.GetSystemParameter(userCtx, "bas_appletparameter", "fapporderdate", "1900-01-01");
            var fapporderdateParam = profileService.GetSystemParameter(userCtx, "bas_appletparameter");
            if (!string.IsNullOrEmpty(Convert.ToString(fapporderdateParam["fapporderdate"])))
            {
                fapporderdate = Convert.ToString(fapporderdateParam["fapporderdate"]);
            }

            //添加 订单默认取数时间过滤
            sqlWhere += $" and a.forderdate >= '{fapporderdate}' ";

            sqlBuilder.SqlParams.Add(new SqlParam("@fmainorgid", DbType.String, userCtx.Company));
            sqlBuilder.SqlParams.Add(new SqlParam("@fcustomerid", DbType.String, userCtx.UserId));
            if (type.IsNullOrEmptyOrWhiteSpace()) type = "all";

            switch (type?.ToLower())
            {
                // 待付款：审核通过，但是未付全款的合同（未收款>0）。
                case "paying":
                    {
                        sqlWhere += $@"
and a.fstatus='E' 
and a.funreceived>0";
                    }
                    break;
                // 待发货：合同已审核，且总商品出库数量小于总销售数量。
                case "stocking":
                    {
                        //                        sqlWhere += $@"
                        //and a.fstatus='E' 
                        //and a.fsumoutqty<a.fsumqty";
                        sqlWhere += $@" and  a.fstatus='E' 
and  exists(select 1 from T_YDJ_ORDERENTRY with(nolock) where T_YDJ_ORDERENTRY.fbizqty>T_YDJ_ORDERENTRY.fbizoutqty and T_YDJ_ORDERENTRY.fid=a.fid) ";
                    }
                    break;
                // 待收货：合同已审核，且总商品出库数量大于0，且不包含整单关闭的合同。
                /*
                   1)  消费者小程序 - 合同列表：待收货 , 要增加不包含"手工关闭"的合同; 也就是从 待收货：合同已审核，且总商品出库数量大于0，且不包含整单关闭的合同。变成 待收货：合同已审核，且总商品出库数量大于0，且不包含整单关闭 或 手工关闭 的合同。
                    2) 消费者小程序 - 合同列表：已关闭 , 要增加过滤出"手工关闭"的合同
                */
                case "receiving":
                    {
                        sqlWhere += $@"
                        and a.fstatus='E' 
                        and a.fsumoutqty>0
                        and a.fclosestatus<>'{(int)CloseStatus.Whole}'
                        and a.fclosestatus<>'{(int)CloseStatus.Manual}'";
                    }
                    break;
                case "closed":
                    {
                        sqlWhere += $@" and a.fstatus='E'
                                        and a.fclosestatus in('{(int)CloseStatus.Whole}','{(int)CloseStatus.Manual}')";
                    }
                    break;
                default:
                    sqlWhere += $@" and a.fstatus='E' ";
                    break;
            }

            // 搜索需支持：商品名称+门店名称+订单号模糊匹配
            if (keyword.IsNullOrEmptyOrWhiteSpace() == false)
            {
                sqlWhere += @" and 
(
    exists (select 1 from t_ydj_orderentry entry with(nolock) inner join t_bd_material product with(nolock) on entry.fproductid=product.fid where entry.fid=a.fid and product.fname like @keyword)
    or 
    a.fdeptname like @keyword
    or
    a.fbillno like @keyword
)";
                sqlBuilder.SqlParams.Add(new SqlParam("@keyword", DbType.String, $"%{keyword.Trim()}%"));
            }

            string table = $@"
(
    select
        o.fid,
        o.fcreatedate,
        o.fbillno,
        o.fdeptid,
        dept.fname as fdeptname,    
        o.fsumamount,
        o.funreceived,
        o.fstatus,
        o.fclosestatus,
        o.fmainorgid,
        o.forderdate,
        o.fcustomerid,
        (select ISNULL(SUM(fbizqty),0) from t_ydj_orderentry entry with(nolock) where entry.fid=o.fid) as fsumqty,  
        (select ISNULL(SUM(fbizoutqty),0) from t_ydj_orderentry entry with(nolock) where entry.fid=o.fid) as fsumoutqty
    from t_ydj_order o with(nolock) 
    left join t_bd_department dept with(nolock) on o.fdeptid=dept.fid
) as a
{sqlWhere}";

            sqlBuilder.DataSql = $@"select top {pageSize} * from (
select row_number() over(order by {orderBy}) rownum, a.* from {table}
) as t where rownum > {pageSize * (pageIndex - 1)}";

            sqlBuilder.CountSql = $@"select COUNT(1) total_count from {table}";

            return sqlBuilder;
        }
        public static SqlBuilder BuildSqlBuilderByPhone(UserContext userCtx, int pageSize, int pageIndex, string type, string keyword = null)
        {
            SqlBuilder sqlBuilder = new SqlBuilder();

            string orderBy = "a.fcreatedate desc", sqlWhere = " where a.customernumber = @customernumber ";

            //获取订单默认取数时间
            var fapporderdate = "1900-01-01";
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            //var fapporderdateParam = profileService.GetSystemParameter(userCtx, "bas_appletparameter", "fapporderdate", "1900-01-01");
            var fapporderdateParam = profileService.GetSystemParameter(userCtx, "bas_appletparameter");
            if (!string.IsNullOrEmpty(Convert.ToString(fapporderdateParam["fapporderdate"])))
            {
                fapporderdate = Convert.ToString(fapporderdateParam["fapporderdate"]);
            }

            //添加 订单默认取数时间过滤
            sqlWhere += $" and a.forderdate >= '{fapporderdate}' ";

            sqlBuilder.SqlParams.Add(new SqlParam("@customernumber", DbType.String, userCtx.UserSession.UserName));
            if (type.IsNullOrEmptyOrWhiteSpace()) type = "all";

            switch (type?.ToLower())
            {
                // 待付款：审核通过，但是未付全款的合同（未收款>0）。
                case "paying":
                    {
                        sqlWhere += $@"
and a.fstatus='E' 
and a.funreceived>0";
                    }
                    break;
                // 待发货：合同已审核，且总商品出库数量小于总销售数量。
                case "stocking":
                    {
                        //                        sqlWhere += $@"
                        //and a.fstatus='E' 
                        //and a.fsumoutqty<a.fsumqty";
                        sqlWhere += $@" and  a.fstatus='E' 
and  exists(select 1 from T_YDJ_ORDERENTRY with(nolock) where T_YDJ_ORDERENTRY.fbizqty>T_YDJ_ORDERENTRY.fbizoutqty and T_YDJ_ORDERENTRY.fid=a.fid) ";
                    }
                    break;
                // 待收货：合同已审核，且总商品出库数量大于0，且不包含整单关闭的合同。
                /*
                   1)  消费者小程序 - 合同列表：待收货 , 要增加不包含"手工关闭"的合同; 也就是从 待收货：合同已审核，且总商品出库数量大于0，且不包含整单关闭的合同。变成 待收货：合同已审核，且总商品出库数量大于0，且不包含整单关闭 或 手工关闭 的合同。
                    2) 消费者小程序 - 合同列表：已关闭 , 要增加过滤出"手工关闭"的合同
                */
                case "receiving":
                    {
                        sqlWhere += $@"
                        and a.fstatus='E' 
                        and a.fsumoutqty>0
                        and a.fclosestatus<>'{(int)CloseStatus.Whole}'
                        and a.fclosestatus<>'{(int)CloseStatus.Manual}'";
                    }
                    break;
                case "closed":
                    {
                        sqlWhere += $@" and a.fstatus='E'
                                        and a.fclosestatus in('{(int)CloseStatus.Whole}','{(int)CloseStatus.Manual}')";
                    }
                    break;
                default:
                    sqlWhere += $@" and a.fstatus='E' ";
                    break;
            }

            // 搜索需支持：商品名称+门店名称+订单号模糊匹配
            if (keyword.IsNullOrEmptyOrWhiteSpace() == false)
            {
                sqlWhere += @" and 
(
    exists (select 1 from t_ydj_orderentry entry with(nolock) inner join t_bd_material product with(nolock) on entry.fproductid=product.fid where entry.fid=a.fid and product.fname like @keyword)
    or 
    a.fdeptname like @keyword
    or
    a.fbillno like @keyword
)";
                sqlBuilder.SqlParams.Add(new SqlParam("@keyword", DbType.String, $"%{keyword.Trim()}%"));
            }

            string table = $@"
(
    select
        o.fid,
        o.fcreatedate,
        o.fbillno,
        o.fdeptid,
        dept.fname as fdeptname,    
        o.fsumamount,
        o.funreceived,
        o.fstatus,
        o.fclosestatus,
        o.fmainorgid,
        o.forderdate,
        o.fcustomerid,
        c.fnumber customernumber,
        (select ISNULL(SUM(fbizqty),0) from t_ydj_orderentry entry with(nolock) where entry.fid=o.fid) as fsumqty,  
        (select ISNULL(SUM(fbizoutqty),0) from t_ydj_orderentry entry with(nolock) where entry.fid=o.fid) as fsumoutqty
    from t_ydj_order o with(nolock) 
    join t_ydj_customer c on c.fid=o.fcustomerid
    left join t_bd_department dept with(nolock) on o.fdeptid=dept.fid
) as a
{sqlWhere}";

            sqlBuilder.DataSql = $@"select top {pageSize} * from (
select row_number() over(order by {orderBy}) rownum, a.* from {table}
) as t where rownum > {pageSize * (pageIndex - 1)}";

            sqlBuilder.CountSql = $@"select COUNT(1) total_count from {table}";

            return sqlBuilder;
        }
        public static int GetTotalRecord(UserContext userCtx, string type)
        {
            var sqlBuilder = BuildSqlBuilderByPhone(userCtx, 1, 1, type);
            return GetTotalRecord(userCtx, sqlBuilder.CountSql, sqlBuilder.SqlParams);
        }

        public static int GetTotalRecord(UserContext userCtx, string sql, List<SqlParam> sqlParams)
        {
            int totalRecord = 0;

            using (var reader = userCtx.Container.GetService<IDBService>().ExecuteReader(userCtx, sql, sqlParams))
            {
                if (reader.Read())
                {
                    totalRecord = Convert.ToInt32(reader["total_count"]);
                }
            }

            return totalRecord;
        }

        /// <summary>
        /// 解析合同状态文字
        /// </summary>
        /// <returns></returns>
        protected string ParseOrderStatusTxt(OrderListModel model, string tab)
        {
            return ParseOrderStatusTxt(model.Status, model.UnreceivedAmount, model.SumQty, model.SumOutQty, model.CloseStatus, tab: tab);

            ////待付款：审核通过，但是未付全款的合同。
            //if (model.StatusId.EqualsIgnoreCase("E") && !model.ReceiptStatus.EqualsIgnoreCase("receiptstatus_type_03"))
            //{
            //    return "待付款";
            //}

            ////待发货：合同已审核，且所有商品出库数量小于销售数量。
            //if (model.StatusId.EqualsIgnoreCase("E") && model.ProductCount == model.StockingCount)
            //{
            //    return "待发货";
            //}

            ////待收货：合同已审核，且所有商品出库数量大于0。
            //if (model.StatusId.EqualsIgnoreCase("E") && model.ProductCount == model.ReceivingCount)
            //{
            //    return "待收货";
            //}

            ////已取消：先不做，未来做了消费者下单再考虑。


            ////已完成：取订单已审核，且整单关闭的即可。
            //if (model.StatusId.EqualsIgnoreCase("E") && model.CloseStatus == (int)CloseStatus.Whole)
            //{
            //    return "已完成";
            //}

            //return "";
        }

        /// <summary>
        /// 固定状态页签
        /// 除了全部和退款售后，订单页签返回的状态可直接跟页签名称保持一致。例如：请求的待发货，返回的都固定待发货的状态；
        /// </summary>
        private static readonly string[] fixedStatusTabs = new[] { "paying", "stocking", "receiving" };

        /// <summary>
        /// 解析合同状态文字
        /// </summary>
        /// <param name="status">状态</param>
        /// <param name="unreceivedAmount">未收款金额</param>
        /// <param name="sumQty">总商品数量</param>
        /// <param name="sumOutQty">总出库数量</param>
        /// <param name="closeStatus">关闭状态</param>
        /// <param name="isDetail">是否详情页状态</param>
        /// <returns>合同状态文字</returns>
        protected string ParseOrderStatusTxt(string status, decimal unreceivedAmount, int sumQty, int sumOutQty, int closeStatus, bool isDetail = false, string tab = null)
        {
            if (!tab.IsNullOrEmptyOrWhiteSpace() && fixedStatusTabs.Contains(tab?.ToLower()))
            {
                switch (tab?.ToLower())
                {
                    case "stocking": return "待发货";
                    case "receiving": return "待收货";
                    default: return "待付款";
                }
            }

            //待付款：审核通过，但是未付全款的合同（即未收款>0）。
            if (status.EqualsIgnoreCase("E") && unreceivedAmount > 0)
            {
                return isDetail ? "等待付款" : "待付款";
            }

            //已完成：取订单已审核，且整单关闭的即可。
            if (status.EqualsIgnoreCase("E") && closeStatus == (int)CloseStatus.Whole)
            {
                return "已完成";
            }

            //待发货：合同已审核，且总商品出库数量小于总销售数量。
            if (status.EqualsIgnoreCase("E") && sumOutQty < sumQty)
            {
                return isDetail ? "等待发货" : "待发货";
            }

            //待收货：合同已审核，且总商品出库数量大于0。
            if (status.EqualsIgnoreCase("E") && sumOutQty > 0)
            {
                return isDetail ? "等待收货" : "待收货";
            }

            //已取消：先不做，未来做了消费者下单再考虑。

            return "";
        }

        /// <summary>
        /// 解析商品状态文字
        /// </summary>
        /// <returns></returns>
        protected string ParseProductStatusTxt(string orderStatus, int outQty, int qty)
        {
            if (orderStatus.Contains("待付款"))
            {
                return "待付款";
            }

            if (orderStatus.Contains("已完成"))
            {
                return "已完成";
            }

            if (outQty < qty)
            {
                return "待发货";
            }

            if (outQty == qty)
            {
                return "待收货";
            }


            return "";
        }
    }
}
