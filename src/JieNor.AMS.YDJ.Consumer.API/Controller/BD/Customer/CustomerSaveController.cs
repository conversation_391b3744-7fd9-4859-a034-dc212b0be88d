using JieNor.AMS.YDJ.Consumer.API.DTO.BD.Customer;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.Framework;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.BD.Customer
{
    /// <summary>
    /// 消费者小程序：会员信息保存接口
    /// </summary>
    public class CustomerSaveController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerSaveDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            if (dto.Name.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"姓名不能为空！";
                return resp;
            }
            if (dto.Phone.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"手机号不能为空！";
                return resp;
            }
            if (dto.Birthdate.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"生日不能为空！";
                return resp;
            }

            var customerForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");

            var customer = customerForm.GetBizDataById(this.Context, this.Context.UserId, true);
            if (customer == null)
            {
                resp.Success = false;
                resp.Message = "保存失败，当前用户不存在，请检查！";
                return resp;
            }

            customer["fphone"] = dto.Phone;
            customer["fname"] = dto.Name;
            customer["fprovince"] = dto.Province;
            customer["fcity"] = dto.City;
            customer["fregion"] = dto.Region;
            customer["faddress"] = dto.Address;
            customer["fgender"] = dto.Gender;
            customer["femail"] = dto.Email;
            customer["fbirthdate"] = dto.Birthdate;
            customer["fcontacts"] = dto.Name;
            customer["fheadimgurl"] = dto.Images;
            customer["fgeneratesource"] = "会员信息保存接口修改";
            //customer["fheadimgurl_txt"] = ImageFieldUtil.ConvertImageTxt(dto.Images);
            var dt = customerForm.GetDynamicObjectType(this.Context);
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, dt);
            dm.Save(customer);

            resp.Success = true;
            resp.Message = "保存成功！";

            return resp;
        }
    }
}
