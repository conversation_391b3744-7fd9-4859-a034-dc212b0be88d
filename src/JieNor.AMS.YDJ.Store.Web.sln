
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.32413.511
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JieNor.AMS.YDJ.Store.Web", "JieNor.AMS.YDJ.Store.Web\JieNor.AMS.YDJ.Store.Web.csproj", "{D371C781-16BA-4FB4-9DB4-F31838D727D2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JieNor.AMS.YDJ.Store.AppService", "JieNor.AMS.YDJ.Store.AppService\JieNor.AMS.YDJ.Store.AppService.csproj", "{3640F504-8B8C-4836-A6D1-6C10861DFFE6}"
	ProjectSection(ProjectDependencies) = postProject
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3} = {83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}
		{CB351565-D30C-4CE3-B866-41EA29C66EC4} = {CB351565-D30C-4CE3-B866-41EA29C66EC4}
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A} = {B81417A8-30F1-4AF1-97EE-6509B726FA1A}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{C7053058-3BFE-4897-8AB8-9F34355F70E7}"
	ProjectSection(SolutionItems) = preProject
		..\build\build.bat = ..\build\build.bat
		..\build\build.proj = ..\build\build.proj
		..\build\build.tasks = ..\build\build.tasks
		..\build\copy.bat = ..\build\copy.bat
		..\build\copy2.bat = ..\build\copy2.bat
		..\build\copybranch.bat = ..\build\copybranch.bat
		..\build\copybranch2.bat = ..\build\copybranch2.bat
		..\build\fwexclude.txt = ..\build\fwexclude.txt
		..\build\syncfw.bat = ..\build\syncfw.bat
		..\build\syncfw2.bat = ..\build\syncfw2.bat
		..\build\syncfwbranch.bat = ..\build\syncfwbranch.bat
		..\build\syncfwbranch2.bat = ..\build\syncfwbranch2.bat
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "fw", "fw", "{C2F0A24D-61AE-4FB5-988D-3EEC833ED8D2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JieNor.AMS.YDJ.Core", "JieNor.AMS.YDJ.Core\JieNor.AMS.YDJ.Core.csproj", "{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}"
	ProjectSection(ProjectDependencies) = postProject
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A} = {B81417A8-30F1-4AF1-97EE-6509B726FA1A}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JieNor.AMS.YDJ.DataTransferObject", "JieNor.AMS.YDJ.DataTransferObject\JieNor.AMS.YDJ.DataTransferObject.csproj", "{B81417A8-30F1-4AF1-97EE-6509B726FA1A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JieNor.AMS.YDJ.Stock.AppService", "JieNor.AMS.YDJ.Stock.AppService\JieNor.AMS.YDJ.Stock.AppService.csproj", "{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}"
	ProjectSection(ProjectDependencies) = postProject
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3} = {83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A} = {B81417A8-30F1-4AF1-97EE-6509B726FA1A}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JieNor.AMS.YDJ.MP.API", "JieNor.AMS.YDJ.MP.API\JieNor.AMS.YDJ.MP.API.csproj", "{18E7268B-D122-4398-A796-270764724832}"
	ProjectSection(ProjectDependencies) = postProject
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3} = {83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A} = {B81417A8-30F1-4AF1-97EE-6509B726FA1A}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JieNor.AMS.YDJ.Consumer.API", "JieNor.AMS.YDJ.Consumer.API\JieNor.AMS.YDJ.Consumer.API.csproj", "{961979B0-84E2-443D-880E-34F715A1E1F4}"
	ProjectSection(ProjectDependencies) = postProject
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3} = {83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A} = {B81417A8-30F1-4AF1-97EE-6509B726FA1A}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JieNor.AMS.YDJ.MS.API", "JieNor.AMS.YDJ.MS.API\JieNor.AMS.YDJ.MS.API.csproj", "{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}"
	ProjectSection(ProjectDependencies) = postProject
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3} = {83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A} = {B81417A8-30F1-4AF1-97EE-6509B726FA1A}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JieNor.AMS.YDJ.WeiXin.AppService", "JieNor.AMS.YDJ.WeiXin.AppService\JieNor.AMS.YDJ.WeiXin.AppService.csproj", "{CB351565-D30C-4CE3-B866-41EA29C66EC4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JieNor.AMS.YDJ.SWJ.API", "JieNor.AMS.YDJ.SWJ.API\JieNor.AMS.YDJ.SWJ.API.csproj", "{065761D1-29F7-491D-ABF0-30A8D7872226}"
	ProjectSection(ProjectDependencies) = postProject
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3} = {83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A} = {B81417A8-30F1-4AF1-97EE-6509B726FA1A}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JieNor.Framework.AppService", "..\..\fw\Test01\src2\JieNor.Framework.AppService\JieNor.Framework.AppService.csproj", "{131B44E4-4C40-4502-B836-3DF9F73671CE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "JieNor.Framework.Interface", "..\..\fw\Test01\src2\JieNor.Framework.Interface\JieNor.Framework.Interface.csproj", "{D5E41FEB-5FBA-4353-8F77-27897B458DC2}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		MonoTouch|Any CPU = MonoTouch|Any CPU
		MonoTouch|x64 = MonoTouch|x64
		MonoTouch|x86 = MonoTouch|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
		Signed|Any CPU = Signed|Any CPU
		Signed|x64 = Signed|x64
		Signed|x86 = Signed|x86
		STATIC_ONLY NO_EXPRESSIONS|Any CPU = STATIC_ONLY NO_EXPRESSIONS|Any CPU
		STATIC_ONLY NO_EXPRESSIONS|x64 = STATIC_ONLY NO_EXPRESSIONS|x64
		STATIC_ONLY NO_EXPRESSIONS|x86 = STATIC_ONLY NO_EXPRESSIONS|x86
		TYPE_SAFE|Any CPU = TYPE_SAFE|Any CPU
		TYPE_SAFE|x64 = TYPE_SAFE|x64
		TYPE_SAFE|x86 = TYPE_SAFE|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Debug|x64.Build.0 = Debug|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Debug|x86.Build.0 = Debug|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.MonoTouch|Any CPU.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.MonoTouch|Any CPU.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.MonoTouch|x64.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.MonoTouch|x64.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.MonoTouch|x86.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.MonoTouch|x86.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Release|Any CPU.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Release|x64.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Release|x64.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Release|x86.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Release|x86.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Signed|Any CPU.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Signed|Any CPU.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Signed|x64.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Signed|x64.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Signed|x86.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.Signed|x86.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.STATIC_ONLY NO_EXPRESSIONS|x64.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.STATIC_ONLY NO_EXPRESSIONS|x64.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.STATIC_ONLY NO_EXPRESSIONS|x86.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.STATIC_ONLY NO_EXPRESSIONS|x86.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.TYPE_SAFE|Any CPU.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.TYPE_SAFE|Any CPU.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.TYPE_SAFE|x64.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.TYPE_SAFE|x64.Build.0 = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.TYPE_SAFE|x86.ActiveCfg = Release|Any CPU
		{D371C781-16BA-4FB4-9DB4-F31838D727D2}.TYPE_SAFE|x86.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Debug|x64.Build.0 = Debug|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Debug|x86.ActiveCfg = Debug|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Debug|x86.Build.0 = Debug|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.MonoTouch|Any CPU.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.MonoTouch|Any CPU.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.MonoTouch|x64.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.MonoTouch|x64.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.MonoTouch|x86.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.MonoTouch|x86.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Release|Any CPU.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Release|x64.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Release|x64.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Release|x86.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Release|x86.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Signed|Any CPU.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Signed|Any CPU.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Signed|x64.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Signed|x64.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Signed|x86.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.Signed|x86.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.STATIC_ONLY NO_EXPRESSIONS|x64.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.STATIC_ONLY NO_EXPRESSIONS|x64.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.STATIC_ONLY NO_EXPRESSIONS|x86.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.STATIC_ONLY NO_EXPRESSIONS|x86.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.TYPE_SAFE|Any CPU.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.TYPE_SAFE|Any CPU.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.TYPE_SAFE|x64.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.TYPE_SAFE|x64.Build.0 = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.TYPE_SAFE|x86.ActiveCfg = Release|Any CPU
		{3640F504-8B8C-4836-A6D1-6C10861DFFE6}.TYPE_SAFE|x86.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Debug|x64.Build.0 = Debug|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Debug|x86.Build.0 = Debug|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.MonoTouch|Any CPU.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.MonoTouch|Any CPU.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.MonoTouch|x64.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.MonoTouch|x64.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.MonoTouch|x86.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.MonoTouch|x86.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Release|x64.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Release|x64.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Release|x86.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Release|x86.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Signed|Any CPU.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Signed|Any CPU.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Signed|x64.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Signed|x64.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Signed|x86.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.Signed|x86.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.STATIC_ONLY NO_EXPRESSIONS|x64.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.STATIC_ONLY NO_EXPRESSIONS|x64.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.STATIC_ONLY NO_EXPRESSIONS|x86.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.STATIC_ONLY NO_EXPRESSIONS|x86.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.TYPE_SAFE|Any CPU.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.TYPE_SAFE|Any CPU.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.TYPE_SAFE|x64.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.TYPE_SAFE|x64.Build.0 = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.TYPE_SAFE|x86.ActiveCfg = Release|Any CPU
		{83C8AD21-7C2D-4E01-B7CB-AFB7CD6A18E3}.TYPE_SAFE|x86.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Debug|x64.Build.0 = Debug|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Debug|x86.Build.0 = Debug|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.MonoTouch|Any CPU.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.MonoTouch|Any CPU.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.MonoTouch|x64.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.MonoTouch|x64.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.MonoTouch|x86.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.MonoTouch|x86.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Release|Any CPU.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Release|x64.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Release|x64.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Release|x86.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Release|x86.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Signed|Any CPU.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Signed|Any CPU.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Signed|x64.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Signed|x64.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Signed|x86.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.Signed|x86.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.STATIC_ONLY NO_EXPRESSIONS|x64.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.STATIC_ONLY NO_EXPRESSIONS|x64.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.STATIC_ONLY NO_EXPRESSIONS|x86.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.STATIC_ONLY NO_EXPRESSIONS|x86.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.TYPE_SAFE|Any CPU.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.TYPE_SAFE|Any CPU.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.TYPE_SAFE|x64.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.TYPE_SAFE|x64.Build.0 = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.TYPE_SAFE|x86.ActiveCfg = Release|Any CPU
		{B81417A8-30F1-4AF1-97EE-6509B726FA1A}.TYPE_SAFE|x86.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Debug|x64.Build.0 = Debug|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Debug|x86.Build.0 = Debug|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.MonoTouch|Any CPU.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.MonoTouch|Any CPU.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.MonoTouch|x64.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.MonoTouch|x64.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.MonoTouch|x86.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.MonoTouch|x86.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Release|Any CPU.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Release|x64.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Release|x64.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Release|x86.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Release|x86.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Signed|Any CPU.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Signed|Any CPU.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Signed|x64.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Signed|x64.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Signed|x86.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.Signed|x86.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.STATIC_ONLY NO_EXPRESSIONS|x64.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.STATIC_ONLY NO_EXPRESSIONS|x64.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.STATIC_ONLY NO_EXPRESSIONS|x86.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.STATIC_ONLY NO_EXPRESSIONS|x86.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.TYPE_SAFE|Any CPU.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.TYPE_SAFE|Any CPU.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.TYPE_SAFE|x64.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.TYPE_SAFE|x64.Build.0 = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.TYPE_SAFE|x86.ActiveCfg = Release|Any CPU
		{FDFB0934-4D24-43EF-8794-375C3DD9ACA8}.TYPE_SAFE|x86.Build.0 = Release|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Debug|x64.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Debug|x64.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Debug|x86.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Debug|x86.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.MonoTouch|Any CPU.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.MonoTouch|Any CPU.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.MonoTouch|x64.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.MonoTouch|x64.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.MonoTouch|x86.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.MonoTouch|x86.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Release|Any CPU.Build.0 = Release|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Release|x64.ActiveCfg = Release|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Release|x64.Build.0 = Release|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Release|x86.ActiveCfg = Release|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Release|x86.Build.0 = Release|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Signed|Any CPU.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Signed|Any CPU.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Signed|x64.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Signed|x64.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Signed|x86.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.Signed|x86.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.STATIC_ONLY NO_EXPRESSIONS|x64.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.STATIC_ONLY NO_EXPRESSIONS|x64.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.STATIC_ONLY NO_EXPRESSIONS|x86.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.STATIC_ONLY NO_EXPRESSIONS|x86.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.TYPE_SAFE|Any CPU.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.TYPE_SAFE|Any CPU.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.TYPE_SAFE|x64.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.TYPE_SAFE|x64.Build.0 = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.TYPE_SAFE|x86.ActiveCfg = Debug|Any CPU
		{18E7268B-D122-4398-A796-270764724832}.TYPE_SAFE|x86.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Debug|x64.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Debug|x64.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Debug|x86.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Debug|x86.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.MonoTouch|Any CPU.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.MonoTouch|Any CPU.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.MonoTouch|x64.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.MonoTouch|x64.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.MonoTouch|x86.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.MonoTouch|x86.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Release|Any CPU.Build.0 = Release|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Release|x64.ActiveCfg = Release|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Release|x64.Build.0 = Release|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Release|x86.ActiveCfg = Release|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Release|x86.Build.0 = Release|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Signed|Any CPU.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Signed|Any CPU.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Signed|x64.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Signed|x64.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Signed|x86.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.Signed|x86.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.STATIC_ONLY NO_EXPRESSIONS|x64.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.STATIC_ONLY NO_EXPRESSIONS|x64.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.STATIC_ONLY NO_EXPRESSIONS|x86.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.STATIC_ONLY NO_EXPRESSIONS|x86.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.TYPE_SAFE|Any CPU.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.TYPE_SAFE|Any CPU.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.TYPE_SAFE|x64.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.TYPE_SAFE|x64.Build.0 = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.TYPE_SAFE|x86.ActiveCfg = Debug|Any CPU
		{961979B0-84E2-443D-880E-34F715A1E1F4}.TYPE_SAFE|x86.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Debug|x64.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Debug|x86.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.MonoTouch|Any CPU.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.MonoTouch|Any CPU.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.MonoTouch|x64.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.MonoTouch|x64.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.MonoTouch|x86.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.MonoTouch|x86.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Release|Any CPU.Build.0 = Release|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Release|x64.ActiveCfg = Release|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Release|x64.Build.0 = Release|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Release|x86.ActiveCfg = Release|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Release|x86.Build.0 = Release|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Signed|Any CPU.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Signed|Any CPU.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Signed|x64.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Signed|x64.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Signed|x86.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.Signed|x86.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.STATIC_ONLY NO_EXPRESSIONS|x64.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.STATIC_ONLY NO_EXPRESSIONS|x64.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.STATIC_ONLY NO_EXPRESSIONS|x86.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.STATIC_ONLY NO_EXPRESSIONS|x86.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.TYPE_SAFE|Any CPU.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.TYPE_SAFE|Any CPU.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.TYPE_SAFE|x64.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.TYPE_SAFE|x64.Build.0 = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.TYPE_SAFE|x86.ActiveCfg = Debug|Any CPU
		{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}.TYPE_SAFE|x86.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Debug|x64.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Debug|x86.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.MonoTouch|Any CPU.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.MonoTouch|Any CPU.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.MonoTouch|x64.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.MonoTouch|x64.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.MonoTouch|x86.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.MonoTouch|x86.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Release|Any CPU.Build.0 = Release|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Release|x64.ActiveCfg = Release|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Release|x64.Build.0 = Release|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Release|x86.ActiveCfg = Release|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Release|x86.Build.0 = Release|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Signed|Any CPU.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Signed|Any CPU.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Signed|x64.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Signed|x64.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Signed|x86.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.Signed|x86.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.STATIC_ONLY NO_EXPRESSIONS|x64.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.STATIC_ONLY NO_EXPRESSIONS|x64.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.STATIC_ONLY NO_EXPRESSIONS|x86.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.STATIC_ONLY NO_EXPRESSIONS|x86.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.TYPE_SAFE|Any CPU.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.TYPE_SAFE|Any CPU.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.TYPE_SAFE|x64.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.TYPE_SAFE|x64.Build.0 = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.TYPE_SAFE|x86.ActiveCfg = Debug|Any CPU
		{CB351565-D30C-4CE3-B866-41EA29C66EC4}.TYPE_SAFE|x86.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Debug|x64.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Debug|x64.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Debug|x86.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Debug|x86.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.MonoTouch|Any CPU.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.MonoTouch|Any CPU.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.MonoTouch|x64.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.MonoTouch|x64.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.MonoTouch|x86.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.MonoTouch|x86.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Release|Any CPU.Build.0 = Release|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Release|x64.ActiveCfg = Release|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Release|x64.Build.0 = Release|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Release|x86.ActiveCfg = Release|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Release|x86.Build.0 = Release|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Signed|Any CPU.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Signed|Any CPU.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Signed|x64.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Signed|x64.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Signed|x86.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.Signed|x86.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.STATIC_ONLY NO_EXPRESSIONS|x64.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.STATIC_ONLY NO_EXPRESSIONS|x64.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.STATIC_ONLY NO_EXPRESSIONS|x86.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.STATIC_ONLY NO_EXPRESSIONS|x86.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.TYPE_SAFE|Any CPU.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.TYPE_SAFE|Any CPU.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.TYPE_SAFE|x64.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.TYPE_SAFE|x64.Build.0 = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.TYPE_SAFE|x86.ActiveCfg = Debug|Any CPU
		{065761D1-29F7-491D-ABF0-30A8D7872226}.TYPE_SAFE|x86.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Debug|x64.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Debug|x86.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.MonoTouch|Any CPU.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.MonoTouch|Any CPU.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.MonoTouch|x64.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.MonoTouch|x64.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.MonoTouch|x86.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.MonoTouch|x86.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Release|Any CPU.Build.0 = Release|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Release|x64.ActiveCfg = Release|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Release|x64.Build.0 = Release|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Release|x86.ActiveCfg = Release|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Release|x86.Build.0 = Release|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Signed|Any CPU.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Signed|Any CPU.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Signed|x64.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Signed|x64.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Signed|x86.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.Signed|x86.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.STATIC_ONLY NO_EXPRESSIONS|x64.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.STATIC_ONLY NO_EXPRESSIONS|x64.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.STATIC_ONLY NO_EXPRESSIONS|x86.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.STATIC_ONLY NO_EXPRESSIONS|x86.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.TYPE_SAFE|Any CPU.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.TYPE_SAFE|Any CPU.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.TYPE_SAFE|x64.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.TYPE_SAFE|x64.Build.0 = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.TYPE_SAFE|x86.ActiveCfg = Debug|Any CPU
		{131B44E4-4C40-4502-B836-3DF9F73671CE}.TYPE_SAFE|x86.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Debug|x64.ActiveCfg = Debug|x64
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Debug|x64.Build.0 = Debug|x64
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Debug|x86.ActiveCfg = Debug|x86
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Debug|x86.Build.0 = Debug|x86
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.MonoTouch|Any CPU.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.MonoTouch|Any CPU.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.MonoTouch|x64.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.MonoTouch|x64.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.MonoTouch|x86.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.MonoTouch|x86.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Release|Any CPU.Build.0 = Release|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Release|x64.ActiveCfg = Release|x64
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Release|x64.Build.0 = Release|x64
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Release|x86.ActiveCfg = Release|x86
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Release|x86.Build.0 = Release|x86
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Signed|Any CPU.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Signed|Any CPU.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Signed|x64.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Signed|x64.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Signed|x86.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.Signed|x86.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.STATIC_ONLY NO_EXPRESSIONS|Any CPU.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.STATIC_ONLY NO_EXPRESSIONS|x64.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.STATIC_ONLY NO_EXPRESSIONS|x64.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.STATIC_ONLY NO_EXPRESSIONS|x86.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.STATIC_ONLY NO_EXPRESSIONS|x86.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.TYPE_SAFE|Any CPU.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.TYPE_SAFE|Any CPU.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.TYPE_SAFE|x64.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.TYPE_SAFE|x64.Build.0 = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.TYPE_SAFE|x86.ActiveCfg = Debug|Any CPU
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2}.TYPE_SAFE|x86.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{131B44E4-4C40-4502-B836-3DF9F73671CE} = {C2F0A24D-61AE-4FB5-988D-3EEC833ED8D2}
		{D5E41FEB-5FBA-4353-8F77-27897B458DC2} = {C2F0A24D-61AE-4FB5-988D-3EEC833ED8D2}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {40611CF2-FC4C-4206-84DC-36E4A9D223A3}
	EndGlobalSection
	GlobalSection(SubversionScc) = preSolution
		Svn-Managed = True
		Manager = AnkhSVN2019 - Subversion Support for Visual Studio
	EndGlobalSection
EndGlobal
