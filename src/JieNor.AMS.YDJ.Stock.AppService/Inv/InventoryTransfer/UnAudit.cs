using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.Stock.AppService.Common;
using JieNor.AMS.YDJ.Stock.AppService.OpValidation.InventoryTransfer;
using JieNor.AMS.YDJ.Store.AppService.Helper;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InventoryTransfer
{
    /// <summary>
    /// 库存调拨单：反审核
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransfer")]
    [OperationNo("unaudit")]
    public class UnAudit : AbstractOperationServicePlugIn
    {

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype" });
        }

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;  //弹窗提示
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string fbillno = Convert.ToString(newData["fbillno"]);
                var sql = $"select 1 from t_bcm_transfertask where ( ftask_type = 'transferout'or  ftask_type = 'transferin') and fsourcenumber = '{fbillno}'";
                var data = this.DBService.ExecuteDynamicObject(this.Context, sql);
                return data == null || !data.Any();
            }).WithMessage("当前库存调拨单【{0}】已经存在对应的调出或者调入扫描任务, 不允许反审核 !", (newData, oldData) => newData["fbillno"]));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //销售合同”且【源单编号】不为空，且对应上游销售合同的【单据类型】参数配置中【合同调拨出库自动更新单据状态】=“否
                string fsourcetype = Convert.ToString(newData["fsourcetype"]);
                string fsourcenumber = Convert.ToString(newData["fsourcenumber"]);
                if (!newData["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && "ydj_order".Equals(fsourcetype))
                {
                    //获取上游销售合同的单据类型
                    var sql = $@"select  top 1 fbilltype  From T_YDJ_ORDER tyo where fbillno ='{fsourcenumber}'and fmainorgid='{this.Context.Company}'";
                    var fbilltype = this.DBService.ExecuteDynamicObject(this.Context, sql).FirstOrDefault()["fbilltype"] as string;
                    //获取参数配置中【合同调拨出库自动更新单据状态】
                    var orderForm = this.Context.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "ydj_order");
                    var billTypeService = this.Container.GetService<IBillTypeService>();
                    var paramSetObj = billTypeService.GetBillTypeParamSet(this.Context, orderForm, fbilltype);
                    bool fautoalterstatus = false;
                    if (paramSetObj != null)
                    {
                        bool.TryParse(Convert.ToString(paramSetObj["fautoalterstatus"]), out fautoalterstatus);
                    }
                    if (fautoalterstatus)
                    {
                        return true;
                    }
                    return false;
                }
                return true;
            }).WithMessage("对不起，当前调拨单上游合同已完结，不允许反审核！如需调整，请反向调拨处理。"));

            e.Rules.Add(new OrderQtyValidation());
            e.Rules.Add(new UnAuditValidation());
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null) return;
            DirectHelper.UnAudit(this.Context, e.DataEntitys);
            foreach (var item in e.DataEntitys)
            {
                bool fiscallout = Convert.ToBoolean(item["fiscallout"]);//是否做过调出
                if (!fiscallout)
                {
                    var fentity = item["fentity"] as DynamicObjectCollection;
                    foreach (var en in fentity)
                    {
                        en["fstockinqty"] = 0;//分布式调入数量
                        en["fstockinbizqty"] = 0;//分布式调入基本单位数量
                        en["fstockindate"] = null;//分布式调入日期
                    }
                }
            }
            //保存
            this.Context.SaveBizData("stk_inventorytransfer", e.DataEntitys);
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;
            new OrderCommon(this.Context).AlterOrderCloseStatus(e.DataEntitys, this.OperationNo);
            var result = this.Container.GetService<IReserveUpdateService>()
                 .RevertTransferBillReserve(this.Context, this.HtmlForm, e.DataEntitys, this.Option);
            this.Result.MergeResult(result);
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys.IsNullOrEmptyOrWhiteSpace() || !e.DataEntitys.Any())
            {
                return;
            }

            string mainorgid = this.Context.Company;
            IDBService service = this.DBService;
            foreach (var item in e.DataEntitys)
            {
                string sourcebillno = Convert.ToString(item["fsourcenumber"]);

                IndbqtyUpdateHelper.ndbqtyUpdate(this.Context, mainorgid, sourcebillno, service);
            }

        }
    }
}
