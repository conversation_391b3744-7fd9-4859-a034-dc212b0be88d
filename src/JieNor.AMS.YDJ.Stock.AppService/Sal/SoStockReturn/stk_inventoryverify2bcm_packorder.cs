using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Sal.SoStockReturn
{
    [InjectService]
    [FormId("bcm_packorder")]
    [OperationNo("stk_inventoryverify2bcm_packorder")]
    public class stk_inventoryverify2bcm_packorder : AbstractConvertServicePlugIn
    {
        public override void AfterGetSourceBillData(AfterGetSourceBillDataEventArgs e)
        {
            base.AfterGetSourceBillData(e);
            var datas = e.SourceDataEntities.Where(w=> (Convert.ToString(w["fstatus"]) == "E" && Convert.ToInt32(w["fbizpyqty"]) > 0) || Convert.ToString(w["fstatus"]) == "D");
            e.SourceDataEntities = datas;
        }
        public override void BeforeMapFieldValue(BeforeMapFieldValueEventArgs e)
        {
            base.BeforeMapFieldValue(e);
            var status = Convert.ToString(e.SourceDataEntities.FirstOrDefault()?["fstatus"]);
            if (e.FieldMapObject != null && e.FieldMapObject.Id == "fbizremainqty")
            {
                switch (status.ToUpper())
                {
                    case "E":
                        e.FieldMapObject.SrcFieldId = "fbizpyqty";
                        break;
                    case "D":
                        e.FieldMapObject.SrcFieldId = "fbizpdqty";
                        break;
                }
            }
            if (e.FieldMapObject != null && e.FieldMapObject.Id == "fbizqty")
            {
                switch (status.ToUpper())
                {
                    case "E":
                        e.FieldMapObject.SrcFieldId = "fbizpyqty";
                        break;
                    case "D":
                        e.FieldMapObject.SrcFieldId = "fbizpdqty";
                        break;
                }
            }

        }

        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);
            if (e.TargetDataEntities.IsNullOrEmptyOrWhiteSpace() || e.TargetDataEntities.Count() == 0)
            {
                return;
            }
            var data = e.TargetDataEntities.FirstOrDefault();
            var entitys = data["fentity"] as DynamicObjectCollection;
            this.UserContext.Container.GetService<LoadReferenceObjectManager>()?.Load(this.UserContext, data.DynamicObjectType, data, true);
            foreach (var ee in entitys)
            {
                var fmat = ee["fmaterialid_ref"] as DynamicObject;
                if (fmat == null)
                    continue;
                var packtype = Convert.ToString(fmat["fpackagtype"]);
                switch (packtype)
                {
                    case "1":
                        ee["fpackcount"] = 1;
                        break;
                    case "2":
                        ee["fpackcount"] = fmat["fbag"];
                        break;
                    case "3":
                        ee["fpackcount"] = fmat["fpiece"];
                        break;
                }
            }
        }

    }
}
