using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;
using JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveDialog;
using JieNor.Framework.Enums;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Interface.Log;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve
{
    /// <summary>
    /// 预留转移
    /// </summary>
    public partial class ReserveService
    {




        /// <summary>
        /// 预留转移：
        /// 1、销售出库单上保存时做预留，同时如果销售出库单的上游单据有预留，则上游单据的预留做转移
        /// 2、其他出库单上保存时做预留，同时如果其他出库单的上游单据有预留，则上游单据的预留做转移
        /// 3、采购退货单上保存时做预留，同时如果采购退货单的上游单据有预留，则上游单据的预留做转移
        /// 4、调拨单处理：如果调拨单有源单，则源单上的预留信息要做相应的修改（仓库、仓位的修改）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="demandBillForm"></param>
        /// <param name="lstSelRows"></param>
        /// <param name="setting"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        private IOperationResult TransferReserve(UserContext userCtx, HtmlForm demandBillForm, List<ReserveSettingInfo> reserveSettingInfos, OperateOption option)
        {
            IOperationResult result = new OperationResult();
            result.IsSuccess = false;

            var sourceBillMeta = GetSourceFormMeta(userCtx, reserveSettingInfos);

            var needTransfer = CheckNeedTransferBiz(userCtx, demandBillForm, sourceBillMeta);
            if (needTransfer == false)
            {
                return result;
            }

            //获取上游单据信息
            var preBillDatas = GetSourceBillDatas(userCtx, demandBillForm, sourceBillMeta, reserveSettingInfos);
            if (preBillDatas == null || preBillDatas.Count == 0)
            {
                return result;
            }

            //获取上游单据对应的预留单
            var preBillReserveBills = ReserveUtil.GetReserveBillData(userCtx, sourceBillMeta, preBillDatas);
            if (preBillReserveBills == null || preBillReserveBills.Count == 0)
            {
                return result;
            }

            var beUpdate = new List<DynamicObject>();
            foreach (var reserveSettingInfo in reserveSettingInfos)
            {
                var outStockBill = reserveSettingInfo.DemandBillData as DynamicObject;
                if (outStockBill == null)
                {
                    continue;
                }

                //找到对应的上游单据
                var preBillNo = outStockBill["fsourcenumber"]?.ToString();
                var preBillData = preBillDatas.FirstOrDefault(f => preBillNo.EqualsIgnoreCase(f["fbillno"].ToString()));
                if (preBillData == null)
                {
                    continue;
                }

                //如果上游单据是销售合同且已经关闭，不需要更新预留
                if (sourceBillMeta.Id.EqualsIgnoreCase("ydj_order") && Convert.ToString(preBillData["fclosestate"]) == "1")
                {
                    continue;
                }

                //单据已经作废，不需要再更新预留
                if (Convert.ToBoolean(preBillData["fcancelstatus"]))
                {
                    continue;
                }

                //找到对应的上游单据的预留单
                var preBillId = preBillData["Id"]?.ToString();
                var preReserveBill = preBillReserveBills.FirstOrDefault(f => preBillId.EqualsIgnoreCase(f["fsourcepkid"].ToString()));
                if (preReserveBill == null)
                {
                    continue;
                }

                var reserveBillData = reserveSettingInfo.ReserveBillData as DynamicObject;
                TransferReserve(userCtx, demandBillForm, outStockBill, reserveBillData, preReserveBill);

                if (!beUpdate.Any(f => Convert.ToString(f["Id"]) == Convert.ToString(preReserveBill["Id"])))
                {
                    beUpdate.Add(preReserveBill);
                }
                if (!beUpdate.Any(f => Convert.ToString(f["Id"]) == Convert.ToString(reserveBillData["Id"])))
                {
                    beUpdate.Add(reserveBillData);
                }
            }

            if (beUpdate.Count > 0)
            {
                //根据基本单位数量自动反算关联业务单位数量字段（如库存单位，业务单位对应的数量）
                var unitService = userCtx.Container.GetService<IUnitConvertService>();
                unitService.ConvertByBasQty(userCtx, ReserveBillFormMeta, beUpdate, option);
                var para = new Dictionary<string, object>();
                para.Add("IgnoreCheckPermssion", true);
                var invokeResult = userCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(
                    userCtx,
                    "stk_reservebill",
                    beUpdate,
                    "draft",
                    para);
                result.MergeResult(invokeResult);

                if (!invokeResult.IsSuccess)
                {
                    // 暂存方法调用以下方法，因此失败才执行，还原数据
                    ReserveUtil.UpdateOrderReserveQty(userCtx, beUpdate);
                }
            }

            return result;
        }




        /// <summary>
        /// 预留转移---更新上游预留单的预留信息：上游预留单加上预留转移记录
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="reserveBill">下游单据对应的预留单</param>
        /// <param name="preReserveBill">上游单据对应的预留单</param>
        private void TransferReserve(UserContext userCtx, HtmlForm demandBillForm, DynamicObject demandBill, DynamicObject reserveBill, DynamicObject preReserveBill)
        {
            var reservePKID = reserveBill["Id"].ToString();
            var reserveSrcBillNo = reserveBill["fsourcenumber"].ToString();
            var currentEnRows = reserveBill["fentity"] as DynamicObjectCollection;
            var targetEnRows = preReserveBill["fentity"] as DynamicObjectCollection;
            if (currentEnRows == null || currentEnRows.Count == 0 || targetEnRows == null || targetEnRows.Count == 0)
            {
                return;
            }

            DeletePreReserveBillTransferRow(reservePKID, reserveSrcBillNo, targetEnRows);

            string entityKey = "";
            switch (demandBillForm.Id?.ToLower())
            {
                case "stk_sostockout":
                case "stk_otherstockout":
                case "stk_postockreturn":
                case "stk_inventorytransfer":
                    entityKey = "fentity";
                    break;
                case "ydj_order":
                    entityKey = "fentry";
                    break;
                default:
                    throw new BusinessException($"预留转移不支持{demandBillForm.Id}！");
            }

            var currentDemandEnRows = demandBill[entityKey] as DynamicObjectCollection;


            foreach (var currentEnRow in currentEnRows)
            {
                // 当前预留需求行的来源明细行id
                var sourceDemandEnId = Convert.ToString(currentEnRow["fsourceentryid"]);
                var currentDemandEnRow =
                    currentDemandEnRows?.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(sourceDemandEnId));

                string sourceEnFldId = "";
                switch (demandBillForm.Id?.ToLower())
                {
                    case "stk_sostockout":
                    case "stk_otherstockout":
                    case "stk_postockreturn":
                    case "stk_inventorytransfer":
                        sourceEnFldId = "fsourceentryid";
                        break;
                    case "ydj_order":
                        sourceEnFldId = "fsourceentryid_e";
                        break;
                    default:
                        throw new BusinessException($"预留转移不支持{demandBillForm.Id}！");
                }

                // 当前需求单的明细行关联的来源明细行id
                string sourceEnId = Convert.ToString(currentDemandEnRow?[sourceEnFldId]);

                var allBeTransferQty = Convert.ToDecimal(currentEnRow["fqty"]);//待转移的总的预留量
                var srcDetailRows = currentEnRow["fdetail"] as DynamicObjectCollection;
                var targetMapEnRows = GetTargetEnRow(targetEnRows, currentEnRow, sourceEnId);//可能存在一模一样的多个需求行
                if (allBeTransferQty <= 0 || srcDetailRows == null || srcDetailRows.Count == 0 || targetMapEnRows == null || targetMapEnRows.Count == 0)
                {
                    continue;
                }

                // 合并处理
                List<DynamicObjectCollection> targetDetailRows = new List<DynamicObjectCollection>();
                foreach (var targetEnRow in targetMapEnRows)
                {
                    var fdetail = targetEnRow["fdetail"] as DynamicObjectCollection;
                    if (fdetail == null || fdetail.Count == 0)
                    {
                        continue;
                    }

                    targetDetailRows.Add(fdetail);
                }

                //优先按仓库、仓位、库存状态完全匹配的行做转移
                TransferReserveWithStrict(userCtx, demandBillForm, reserveBill, srcDetailRows, targetDetailRows);

                //处理未完成转移的部分（比如销售合同，预留的仓库为A，但是下推销售出库的仓库B，则前面的转移会匹配不到，转移不成功）
                TransferReserveWithNoStrict(userCtx, demandBillForm, reserveBill, srcDetailRows, targetDetailRows);
            }

            //重新汇总需求明细行的预留数量
            foreach (var targetEnRow in targetEnRows)
            {
                var targetDetailRows = targetEnRow["fdetail"] as DynamicObjectCollection;
                CalculateReserveQty(targetEnRow, targetDetailRows);
            }

            //同时记录上游预留单的信息，以便下游预留单删除时，把预留转移再还回来
            reserveBill["fprereservepkid"] = preReserveBill["Id"];
        }


        /// <summary>
        /// 处理未完成转移的部分（比如销售合同，预留的仓库为A，但是下推销售出库的仓库B，则前面的转移会匹配不到，转移不成功，在这里补充进行处理）
        /// </summary>
        /// <param name="demandBillForm"></param>
        /// <param name="reserveBill"></param> 
        /// <param name="srcDetailRows"></param>
        /// <param name="targetDetailRows"></param>
        /// <returns></returns>
        private static void TransferReserveWithNoStrict(UserContext ctx, HtmlForm demandBillForm, DynamicObject reserveBill, DynamicObjectCollection srcDetailRows, List<DynamicObjectCollection> targetDetailRows)
        {
            ISequenceService sequenceService = ctx.Container.GetService<ISequenceService>();

            var fnextreservepkid = reserveBill["Id"].ToString();
            var demandBillNo = reserveBill["fsourcenumber"].ToString();
            //按仓库+库存状态分组
            var currentGrps = srcDetailRows.GroupBy(f => new
            {
                fstorehouseid = Convert.ToString(f["fstorehouseid"]).Trim(),
                fstockstatus = Convert.ToString(f["fstockstatus"]).Trim(),
            }).ToList();

            var beAddTrance = new List<DynamicObject>();
            foreach (var currentGrp in currentGrps)
            {
                //当前预留单对应分组预留数量：总的待转移量
                var beAllTransferQty = currentGrp.ToList()
                                           .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                           .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                                       -
                                       currentGrp.ToList()
                                           .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                           .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                if (beAllTransferQty <= 0)
                {
                    continue;
                }

                // 累加所有预留需求的跟踪明细行
                var allTargetTranceRows = new List<DynamicObject>();
                foreach (var targetDetailRow in targetDetailRows)
                {
                    //目前只针对销售出库单做处理
                    //#77295 【250854】 【慕思现场-6.26 23:59】商品行已出库，但是预留量没有释放
                    //当销售合同出A仓，出库单出B仓并且分多行出库时，就会导致只有第一行会预留转移，最后一行不会做预留转移，因为前面的行预留转移后，会添加到targetDetailRow集合里，根据下面的数据算出来就没有需要预留转移数据了，所以这里只查仓库相同，既源单行的数据
                    if (demandBillForm.Id.Equals("stk_sostockout"))
                    {
                        foreach (var targetDetailRowItem in targetDetailRow)
                        {
                            if (!Convert.ToString(targetDetailRowItem["fstorehouseid"]).Equals(currentGrp.Key.fstorehouseid))
                            {
                                allTargetTranceRows.Add(targetDetailRowItem);
                            }
                        }
                    }
                    else
                    {
                        allTargetTranceRows.AddRange(targetDetailRow);
                    }
                }

                var targetGrps = allTargetTranceRows.GroupBy(f => new
                {
                    fstorehouseid = Convert.ToString(f["fstorehouseid"]).Trim(),
                    fstockstatus = Convert.ToString(f["fstockstatus"]).Trim(),
                }).ToList();

                var targetGrp = targetGrps.FirstOrDefault(f =>
                    f.Key.fstorehouseid == currentGrp.Key.fstorehouseid
                    && f.Key.fstockstatus == currentGrp.Key.fstockstatus)?.ToList();

                //对应分组的已转移数量
                var haveQty = (targetGrp
                    ?.Where(f => Convert.ToString(f["fopdesc"]) == "3" && Convert.ToString(f["fnextreservepkid"]) == fnextreservepkid)
                    ?.Sum(f => Convert.ToDecimal(f["fqty_d"]))).GetValueOrDefault();
                if (beAllTransferQty <= haveQty)
                {
                    continue;
                }

                //上游预留单明细行的总的预留量
                var allTargetDetailRows = beAddTrance.Concat(allTargetTranceRows).ToList();
                var totalQty = allTargetDetailRows
                                   .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                   .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                               -
                               allTargetDetailRows
                                   .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                   .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                if (totalQty <= 0)
                {
                    break;
                }

                //总的还需要的转移量
                var beNeedTransferQty = beAllTransferQty - haveQty;
                if (beNeedTransferQty > totalQty)
                {
                    beNeedTransferQty = totalQty;
                }

                foreach (var targetGrpX in targetGrps)
                {
                    //上游预留单明细行的总的预留量
                    allTargetDetailRows = beAddTrance.Concat(allTargetTranceRows).ToList();
                    totalQty = allTargetDetailRows
                                   .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                   .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                               -
                               allTargetDetailRows
                                   .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                   .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                    if (totalQty <= 0)
                    {
                        break;
                    }

                    if (beNeedTransferQty <= 0)
                    {
                        break;
                    }

                    //上游预留单的对应需求明细的预留数量（总的可以转移的预留数量）
                    var demandIncreaseQty = targetGrpX
                        .Where(f =>
                            Convert.ToString(f["fopdesc"]) != "3"
                            && Convert.ToString(f["fnextreservepkid"]) != fnextreservepkid
                            && Convert.ToString(f["fdirection_d"]) == "0")
                        .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                    var demandDecreaseQty = targetGrpX
                        .Where(f =>
                            Convert.ToString(f["fopdesc"]) != "3"
                            && Convert.ToString(f["fnextreservepkid"]) != fnextreservepkid
                            && Convert.ToString(f["fdirection_d"]) == "1")
                        .Sum(f => Convert.ToDecimal(f["fqty_d"]));

                    var demandQty = demandIncreaseQty - demandDecreaseQty;

                    //上游预留单该分组的已转移数量
                    haveQty = targetGrpX
                        .Where(f =>
                            Convert.ToString(f["fopdesc"]) == "3"
                            && Convert.ToString(f["fnextreservepkid"]) == fnextreservepkid)
                        .Sum(f => Convert.ToDecimal(f["fqty_d"]));

                    //上游预留单该分组还可以转多少量
                    var canQty = demandQty - haveQty;
                    if (canQty <= 0)
                    {
                        continue;
                    }

                    //转移的预留数量
                    var beTransferQty = canQty >= beNeedTransferQty ? beNeedTransferQty : canQty;
                    if (currentGrp.Key.fstockstatus.Trim() == targetGrpX.Key.fstockstatus.Trim()
                        && currentGrp.Key.fstorehouseid.Trim() == targetGrpX.Key.fstorehouseid.Trim()
                        )
                    {
                        // 分配转移预留量到需求明细下
                        foreach (var targetDetailRow in targetDetailRows)
                        {
                            if (beTransferQty <= 0)
                            {
                                break;
                            }

                            var tranceRowIds = targetDetailRow.Select(s => Convert.ToString(s["id"])).ToList();
                            var targetRows = targetGrpX.Where(s => tranceRowIds.Contains(Convert.ToString(s["id"]))).ToList();

                            // 当前预留需求明细下的符合条件的预留量
                            var leaveQty = targetRows
                                               .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                               .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                                           -
                                           targetRows
                                               .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                               .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                            if (leaveQty <= 0)
                            {
                                continue;
                            }

                            // 计算需要转移的预留量
                            leaveQty = leaveQty > beTransferQty ? beTransferQty : leaveQty;
                            if (leaveQty > 0)
                            {
                                var tranceRow = targetRows.First().Clone() as DynamicObject;
                                tranceRow["id"] = sequenceService.GetSequence<string>();
                                tranceRow["foptime"] = DateTime.Now;
                                tranceRow["fopuserid"] = ctx.UserId;
                                tranceRow["fqty_d"] = beTransferQty;
                                tranceRow["fbizqty_d"] = beTransferQty;
                                tranceRow["freservenote"] = "预留转移到下游单据【{0} {1}】".Fmt(demandBillForm.Caption, demandBillNo);
                                tranceRow["fopdesc"] = "3";//预留转移
                                tranceRow["fdirection_d"] = "1";//预留减少                    
                                tranceRow["fnextreservepkid"] = reserveBill["Id"];//记录对应的下游预留单id
                                tranceRow["freservedateto_d"] = DateTime.Now.Date;

                                targetDetailRow.Add(tranceRow);

                                // 用于后缀计算
                                beAddTrance.Add(tranceRow);

                                beTransferQty -= leaveQty;
                            }
                        }
                    }
                    else
                    {
                        // 分配转移预留量到需求明细下
                        foreach (var targetDetailRow in targetDetailRows)
                        {
                            if (beTransferQty <= 0)
                            {
                                break;
                            }

                            var tranceRowIds = targetDetailRow.Select(s => Convert.ToString(s["id"])).ToList();
                            var targetRows = targetGrpX.Where(s => tranceRowIds.Contains(Convert.ToString(s["id"]))).ToList();

                            // 当前预留需求明细下的符合条件的预留量
                            var leaveQty = targetRows
                                               .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                               .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                                           -
                                           targetRows
                                               .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                               .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                            if (leaveQty <= 0)
                            {
                                continue;
                            }

                            // 计算需要转移的预留量
                            leaveQty = leaveQty > beTransferQty ? beTransferQty : leaveQty;
                            if (leaveQty > 0)
                            {
                                var tranceRow = targetRows.First().Clone() as DynamicObject;
                                tranceRow["id"] = sequenceService.GetSequence<string>();
                                tranceRow["foptime"] = DateTime.Now;
                                tranceRow["fopuserid"] = ctx.UserId;
                                tranceRow["fqty_d"] = beTransferQty;
                                tranceRow["fbizqty_d"] = beTransferQty;
                                tranceRow["freservenote"] = "下游单据【{0} {1}】仓库发生变化，预留发生转移到其它仓库".Fmt(demandBillForm.Caption, demandBillNo);
                                tranceRow["fopdesc"] = "1";//预留减少
                                tranceRow["fdirection_d"] = "1";//预留减少
                                tranceRow["fnextreservepkid"] = reserveBill["Id"];

                                targetDetailRow.Add(tranceRow);
                                // 用于后缀计算
                                beAddTrance.Add(tranceRow);

                                tranceRow = targetRows.First().Clone() as DynamicObject;
                                tranceRow["id"] = sequenceService.GetSequence<string>();
                                tranceRow["foptime"] = DateTime.Now;
                                tranceRow["fopuserid"] = ctx.UserId;
                                tranceRow["fqty_d"] = beTransferQty;
                                tranceRow["fbizqty_d"] = beTransferQty;
                                tranceRow["freservenote"] = "下游单据【{0} {1}】仓库发生变化，预留发生转移到该仓库".Fmt(demandBillForm.Caption, demandBillNo);
                                tranceRow["fopdesc"] = "0";//预留增加
                                tranceRow["fdirection_d"] = "0";//预留增加
                                tranceRow["fnextreservepkid"] = reserveBill["Id"];
                                tranceRow["fstockstatus"] = currentGrp.Key.fstockstatus;
                                tranceRow["fstorehouseid"] = currentGrp.Key.fstorehouseid;

                                targetDetailRow.Add(tranceRow);
                                // 用于后缀计算
                                beAddTrance.Add(tranceRow);

                                tranceRow = targetRows.First().Clone() as DynamicObject;
                                tranceRow["id"] = sequenceService.GetSequence<string>();
                                tranceRow["foptime"] = DateTime.Now;
                                tranceRow["fopuserid"] = ctx.UserId;
                                tranceRow["fqty_d"] = beTransferQty;
                                tranceRow["fbizqty_d"] = beTransferQty;
                                tranceRow["freservenote"] = "预留转移到下游单据【{0} {1}】".Fmt(demandBillForm.Caption, demandBillNo);
                                tranceRow["fopdesc"] = "3";//预留转移
                                tranceRow["fdirection_d"] = "1";//预留减少                    
                                tranceRow["fnextreservepkid"] = reserveBill["Id"];//记录对应的下游预留单id
                                tranceRow["fstockstatus"] = currentGrp.Key.fstockstatus;
                                tranceRow["fstorehouseid"] = currentGrp.Key.fstorehouseid;
                                tranceRow["freservedateto_d"] = DateTime.Now.Date;

                                targetDetailRow.Add(tranceRow);
                                // 用于后缀计算
                                beAddTrance.Add(tranceRow);

                                beTransferQty -= leaveQty;
                            }
                        }

                    }

                    beNeedTransferQty -= beTransferQty;
                }
            }
        }



        /// <summary>
        /// 处理未完成转移的部分（比如销售合同，预留的仓库为A，但是下推销售出库的仓库B，则前面的转移会匹配不到，转移不成功，在这里补充进行处理）
        /// </summary>
        /// <param name="demandBillForm"></param>
        /// <param name="reserveBill"></param> 
        /// <param name="srcDetailRows"></param>
        /// <param name="targetDetailRows"></param>
        /// <returns></returns>
        private static void TransferReserveWithNoStrict1(UserContext ctx, HtmlForm demandBillForm, DynamicObject reserveBill, DynamicObjectCollection srcDetailRows, List<DynamicObjectCollection> targetDetailRows)
        {
            ISequenceService sequenceService = ctx.Container.GetService<ISequenceService>();

            var fnextreservepkid = reserveBill["Id"].ToString();
            var demandBillNo = reserveBill["fsourcenumber"].ToString();
            //按仓库+库存状态分组
            var currentGrps = srcDetailRows.GroupBy(f => new
            {
                fstorehouseid = Convert.ToString(f["fstorehouseid"]).Trim(),
                fstockstatus = Convert.ToString(f["fstockstatus"]).Trim(),
            }).ToList();

            var beAddTrance = new List<DynamicObject>();
            foreach (var currentGrp in currentGrps)
            {
                //当前预留单对应分组预留数量：总的待转移量
                var beAllTransferQty = currentGrp.ToList()
                                           .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                           .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                                       -
                                       currentGrp.ToList()
                                           .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                           .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                if (beAllTransferQty <= 0)
                {
                    continue;
                }

                // 累加所有预留需求的跟踪明细行
                var allTargetTranceRows = new List<DynamicObject>();
                foreach (var targetDetailRow in targetDetailRows)
                {
                    allTargetTranceRows.AddRange(targetDetailRow);
                }

                var targetGrps = allTargetTranceRows.GroupBy(f => new
                {
                    fstorehouseid = Convert.ToString(f["fstorehouseid"]).Trim(),
                    fstockstatus = Convert.ToString(f["fstockstatus"]).Trim(),
                }).ToList();

                var targetGrp = targetGrps.FirstOrDefault(f =>
                    f.Key.fstorehouseid == currentGrp.Key.fstorehouseid
                    && f.Key.fstockstatus == currentGrp.Key.fstockstatus)?.ToList();

                //对应分组的已转移数量
                var haveQty = (targetGrp
                    ?.Where(f => Convert.ToString(f["fopdesc"]) == "3" && Convert.ToString(f["fnextreservepkid"]) == fnextreservepkid)
                    ?.Sum(f => Convert.ToDecimal(f["fqty_d"]))).GetValueOrDefault();
                if (beAllTransferQty <= haveQty)
                {
                    continue;
                }

                //上游预留单明细行的总的预留量
                var allTargetDetailRows = beAddTrance.Concat(allTargetTranceRows).ToList();
                var totalQty = allTargetDetailRows
                                   .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                   .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                               -
                               allTargetDetailRows
                                   .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                   .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                if (totalQty <= 0)
                {
                    break;
                }

                //总的还需要的转移量
                var beNeedTransferQty = beAllTransferQty - haveQty;
                if (beNeedTransferQty > totalQty)
                {
                    beNeedTransferQty = totalQty;
                }

                foreach (var targetGrpX in targetGrps)
                {
                    //上游预留单明细行的总的预留量
                    allTargetDetailRows = beAddTrance.Concat(allTargetTranceRows).ToList();
                    totalQty = allTargetDetailRows
                                   .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                   .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                               -
                               allTargetDetailRows
                                   .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                   .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                    if (totalQty <= 0)
                    {
                        break;
                    }

                    if (beNeedTransferQty <= 0)
                    {
                        break;
                    }

                    //上游预留单的对应需求明细的预留数量（总的可以转移的预留数量）
                    var demandIncreaseQty = targetGrpX
                        .Where(f =>
                            Convert.ToString(f["fopdesc"]) != "3"
                            && Convert.ToString(f["fnextreservepkid"]) != fnextreservepkid
                            && Convert.ToString(f["fdirection_d"]) == "0")
                        .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                    var demandDecreaseQty = targetGrpX
                        .Where(f =>
                            Convert.ToString(f["fopdesc"]) != "3"
                            && Convert.ToString(f["fnextreservepkid"]) != fnextreservepkid
                            && Convert.ToString(f["fdirection_d"]) == "1")
                        .Sum(f => Convert.ToDecimal(f["fqty_d"]));

                    var demandQty = demandIncreaseQty - demandDecreaseQty;

                    //上游预留单该分组的已转移数量
                    haveQty = targetGrpX
                        .Where(f =>
                            Convert.ToString(f["fopdesc"]) == "3"
                            && Convert.ToString(f["fnextreservepkid"]) == fnextreservepkid)
                        .Sum(f => Convert.ToDecimal(f["fqty_d"]));

                    //上游预留单该分组还可以转多少量
                    var canQty = demandQty - haveQty;
                    if (canQty <= 0)
                    {
                        continue;
                    }

                    //转移的预留数量
                    var beTransferQty = canQty >= beNeedTransferQty ? beNeedTransferQty : canQty;
                    if (currentGrp.Key.fstockstatus.Trim() == targetGrpX.Key.fstockstatus.Trim()
                        && currentGrp.Key.fstorehouseid.Trim() == targetGrpX.Key.fstorehouseid.Trim()
                        )
                    {
                        // 分配转移预留量到需求明细下
                        foreach (var targetDetailRow in targetDetailRows)
                        {
                            if (beTransferQty <= 0)
                            {
                                break;
                            }

                            var tranceRowIds = targetDetailRow.Select(s => Convert.ToString(s["id"])).ToList();
                            var targetRows = targetGrpX.Where(s => tranceRowIds.Contains(Convert.ToString(s["id"]))).ToList();

                            // 当前预留需求明细下的符合条件的预留量
                            var leaveQty = targetRows
                                               .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                               .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                                           -
                                           targetRows
                                               .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                               .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                            if (leaveQty <= 0)
                            {
                                continue;
                            }

                            // 计算需要转移的预留量
                            leaveQty = leaveQty > beTransferQty ? beTransferQty : leaveQty;
                            if (leaveQty > 0)
                            {
                                var tranceRow = targetRows.First().Clone() as DynamicObject;
                                tranceRow["id"] = sequenceService.GetSequence<string>();
                                tranceRow["foptime"] = DateTime.Now;
                                tranceRow["fopuserid"] = ctx.UserId;
                                tranceRow["fqty_d"] = beTransferQty;
                                tranceRow["fbizqty_d"] = beTransferQty;
                                tranceRow["freservenote"] = "预留转移到下游单据【{0} {1}】".Fmt(demandBillForm.Caption, demandBillNo);
                                tranceRow["fopdesc"] = "3";//预留转移
                                tranceRow["fdirection_d"] = "1";//预留减少                    
                                tranceRow["fnextreservepkid"] = reserveBill["Id"];//记录对应的下游预留单id
                                tranceRow["freservedateto_d"] = DateTime.Now.Date;

                                targetDetailRow.Add(tranceRow);

                                // 用于后缀计算
                                beAddTrance.Add(tranceRow);

                                beTransferQty -= leaveQty;
                            }
                        }
                    }
                    else
                    {
                        // 分配转移预留量到需求明细下
                        foreach (var targetDetailRow in targetDetailRows)
                        {
                            if (beTransferQty <= 0)
                            {
                                break;
                            }

                            var tranceRowIds = targetDetailRow.Select(s => Convert.ToString(s["id"])).ToList();
                            var targetRows = targetGrpX.Where(s => tranceRowIds.Contains(Convert.ToString(s["id"]))).ToList();

                            // 当前预留需求明细下的符合条件的预留量
                            var leaveQty = targetRows
                                               .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                               .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                                           -
                                           targetRows
                                               .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                               .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                            if (leaveQty <= 0)
                            {
                                continue;
                            }

                            // 计算需要转移的预留量
                            leaveQty = leaveQty > beTransferQty ? beTransferQty : leaveQty;
                            if (leaveQty > 0)
                            {
                                var tranceRow = targetRows.First().Clone() as DynamicObject;
                                tranceRow["id"] = sequenceService.GetSequence<string>();
                                tranceRow["foptime"] = DateTime.Now;
                                tranceRow["fopuserid"] = ctx.UserId;
                                tranceRow["fqty_d"] = beTransferQty;
                                tranceRow["fbizqty_d"] = beTransferQty;
                                tranceRow["freservenote"] = "下游单据【{0} {1}】仓库发生变化，预留发生转移到其它仓库".Fmt(demandBillForm.Caption, demandBillNo);
                                tranceRow["fopdesc"] = "1";//预留减少
                                tranceRow["fdirection_d"] = "1";//预留减少
                                tranceRow["fnextreservepkid"] = reserveBill["Id"];

                                targetDetailRow.Add(tranceRow);
                                // 用于后缀计算
                                beAddTrance.Add(tranceRow);

                                tranceRow = targetRows.First().Clone() as DynamicObject;
                                tranceRow["id"] = sequenceService.GetSequence<string>();
                                tranceRow["foptime"] = DateTime.Now;
                                tranceRow["fopuserid"] = ctx.UserId;
                                tranceRow["fqty_d"] = beTransferQty;
                                tranceRow["fbizqty_d"] = beTransferQty;
                                tranceRow["freservenote"] = "下游单据【{0} {1}】仓库发生变化，预留发生转移到该仓库".Fmt(demandBillForm.Caption, demandBillNo);
                                tranceRow["fopdesc"] = "0";//预留增加
                                tranceRow["fdirection_d"] = "0";//预留增加
                                tranceRow["fnextreservepkid"] = reserveBill["Id"];
                                tranceRow["fstockstatus"] = currentGrp.Key.fstockstatus;
                                tranceRow["fstorehouseid"] = currentGrp.Key.fstorehouseid;

                                targetDetailRow.Add(tranceRow);
                                // 用于后缀计算
                                beAddTrance.Add(tranceRow);

                                tranceRow = targetRows.First().Clone() as DynamicObject;
                                tranceRow["id"] = sequenceService.GetSequence<string>();
                                tranceRow["foptime"] = DateTime.Now;
                                tranceRow["fopuserid"] = ctx.UserId;
                                tranceRow["fqty_d"] = beTransferQty;
                                tranceRow["fbizqty_d"] = beTransferQty;
                                tranceRow["freservenote"] = "预留转移到下游单据【{0} {1}】".Fmt(demandBillForm.Caption, demandBillNo);
                                tranceRow["fopdesc"] = "3";//预留转移
                                tranceRow["fdirection_d"] = "1";//预留减少                    
                                tranceRow["fnextreservepkid"] = reserveBill["Id"];//记录对应的下游预留单id
                                tranceRow["fstockstatus"] = currentGrp.Key.fstockstatus;
                                tranceRow["fstorehouseid"] = currentGrp.Key.fstorehouseid;
                                tranceRow["freservedateto_d"] = DateTime.Now.Date;

                                targetDetailRow.Add(tranceRow);
                                // 用于后缀计算
                                beAddTrance.Add(tranceRow);

                                beTransferQty -= leaveQty;
                            }
                        }

                    }

                    beNeedTransferQty -= beTransferQty;
                }
            }
        }



        /// <summary>
        /// 先按仓库、仓位、库存状态完全匹配的行做转移
        /// </summary>
        /// <param name="demandBillForm"></param>
        /// <param name="reserveBill"></param> 
        /// <param name="srcDetailRows"></param>
        /// <param name="targetDetailRows"></param>
        /// <returns></returns>
        private static void TransferReserveWithStrict(UserContext ctx, HtmlForm demandBillForm, DynamicObject reserveBill, DynamicObjectCollection srcDetailRows, List<DynamicObjectCollection> targetDetailRows)
        {
            ISequenceService sequenceService = ctx.Container.GetService<ISequenceService>();

            var demandBillNo = reserveBill["fsourcenumber"].ToString();
            //按仓库+仓位+库存状态分组
            var currentGrps = srcDetailRows.GroupBy(f => new
            {
                fstorehouseid = Convert.ToString(f["fstorehouseid"]).Trim(),
                fstockstatus = Convert.ToString(f["fstockstatus"]).Trim(),
            }).ToList();

            // 累加所有预留需求的跟踪明细行
            var allTargetTranceRows = new List<DynamicObject>();
            foreach (var targetDetailRow in targetDetailRows)
            {
                allTargetTranceRows.AddRange(targetDetailRow);
            }

            var targetGrps = allTargetTranceRows.GroupBy(f => new
            {
                fstorehouseid = Convert.ToString(f["fstorehouseid"]).Trim(),
                fstockstatus = Convert.ToString(f["fstockstatus"]).Trim(),
            }).ToList();

            foreach (var currentGrp in currentGrps)
            {
                //找上游预留单的对应需求明细
                //var targetGrp = targetGrps.FirstOrDefault(f =>
                //    f.Key.fstorehouseid == currentGrp.Key.fstorehouseid
                //    && f.Key.fstockstatus == currentGrp.Key.fstockstatus)?.ToList();
                // 找上游预留单的对应需求明细（取创建时间最早的分组）
                var targetGrp = targetGrps
                    .FirstOrDefault(f =>
                        f.Key.fstorehouseid == currentGrp.Key.fstorehouseid
                        && f.Key.fstockstatus == currentGrp.Key.fstockstatus
                    )?.ToList();


                if (targetGrp == null)
                {
                    continue;
                }

                //上游预留单的对应需求明细的预留数量（总的可以转移的预留数量）
                var targetLeaveQty = targetGrp
                                         .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                         .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                                     -
                                     targetGrp
                                         .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                         .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                if (targetLeaveQty <= 0)
                {
                    continue;
                }

                //当前预留单对应分组预留数量
                var currentLeaveQty = currentGrp
                                          .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                          .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                                      -
                                      currentGrp
                                          .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                          .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                if (currentLeaveQty <= 0)
                {
                    continue;
                }

                //转移的预留数量
                var beTransferQty = targetLeaveQty > currentLeaveQty ? currentLeaveQty : targetLeaveQty;
                if (beTransferQty <= 0)
                {
                    continue;
                }

                // 分配转移预留量到需求明细下
                foreach (var targetDetailRow in targetDetailRows)
                {
                    if (beTransferQty <= 0)
                    {
                        break;
                    }

                    var tranceRowIds = targetDetailRow.Select(s => Convert.ToString(s["id"])).ToList();
                    var targetRows = targetGrp.Where(s => tranceRowIds.Contains(Convert.ToString(s["id"]))).ToList();

                    // 当前预留需求明细下的符合条件的预留量
                    var leaveQty = targetRows
                                       .Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                       .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                                   -
                                   targetRows
                                       .Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                       .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                    if (leaveQty <= 0)
                    {
                        continue;
                    }

                    // 计算需要转移的预留量
                    leaveQty = leaveQty > beTransferQty ? beTransferQty : leaveQty;
                    if (leaveQty > 0)
                    {
                        var tranceRow = targetRows.First().Clone() as DynamicObject;
                        tranceRow["id"] = sequenceService.GetSequence<string>();
                        tranceRow["foptime"] = DateTime.Now;
                        tranceRow["fopuserid"] = ctx.UserId;
                        tranceRow["fqty_d"] = leaveQty;
                        tranceRow["fbizqty_d"] = leaveQty;
                        tranceRow["freservenote"] = "预留转移到下游单据【{0} {1}】".Fmt(demandBillForm.Caption, demandBillNo);
                        tranceRow["fopdesc"] = "3";//预留转移
                        tranceRow["fdirection_d"] = "1";//预留减少                    
                        tranceRow["fnextreservepkid"] = reserveBill["Id"];//记录对应的下游预留单id
                        tranceRow["freservedateto_d"] = DateTime.Now.Date;

                        targetDetailRow.Add(tranceRow);

                        beTransferQty -= leaveQty;
                    }
                }
            }
        }



        /// <summary>
        /// 删除上游业务对应的预留单的预留转移记录
        /// </summary>
        /// <param name="reservePKID"></param>
        /// <param name="targetEnRows"></param>
        private void DeletePreReserveBillTransferRow(string reservePKID, string reserveSrcBillNo, DynamicObjectCollection targetEnRows)
        {
            //先删除转移行
            foreach (var targetEnRow in targetEnRows)
            {
                var targetDetails = targetEnRow["fdetail"] as DynamicObjectCollection;
                var beDelDetail = targetDetails?.Where(f => f["freservenote"] != null && reservePKID.EqualsIgnoreCase(f["fnextreservepkid"].ToString())
                                                        && ("3" == f["fopdesc"]?.ToString() || (f["freservenote"]?.ToString().IndexOf("预留发生转移") > -1 && f["freservenote"]?.ToString().IndexOf(reserveSrcBillNo) > -1))
                                                        )?.ToList();
                if (beDelDetail == null)
                {
                    continue;
                }

                foreach (var item in beDelDetail)
                {
                    targetDetails.Remove(item);
                }

                //重新汇总需求明细行的预留数量
                CalculateReserveQty(targetEnRow, targetDetails);
            }
        }




        /// <summary>
        /// 获取对应上游预留单的需求明细行---按 物料+辅助属性+定制信息+物流跟踪号+计量单位 匹配
        /// </summary>
        /// <param name="targetEnRows">上游预留单的需求明细行</param>
        /// <param name="srcEnRow">当前预留单的需求明细行</param>
        /// <param name="sourceEnId">源单明细行id，优先释放此预留</param>
        /// <returns></returns>
        private static List<DynamicObject> GetTargetEnRow(DynamicObjectCollection targetEnRows, DynamicObject srcEnRow, string sourceEnId)
        {
            if (srcEnRow == null)
            {
                return null;
            }

            var fmaterialid = srcEnRow["fmaterialid"]?.ToString();
            var fattrinfo_e = srcEnRow["fattrinfo_e"]?.ToString();
            if (fattrinfo_e.IsNullOrEmptyOrWhiteSpace())
            {
                fattrinfo_e = " ";
            }
            var fcustomdesc = srcEnRow["fcustomdesc"]?.ToString();
            if (fcustomdesc.IsNullOrEmptyOrWhiteSpace())
            {
                fcustomdesc = " ";
            }
            var funitid = srcEnRow["funitid"]?.ToString();
            var fmtono = srcEnRow["fmtono"]?.ToString();
            if (fmtono.IsNullOrEmptyOrWhiteSpace())
            {
                fmtono = " ";
            }

            //// 源单明细行id，优先释放此预留
            //var fsourceentryid = srcEnRow["fsourceentryid"]?.ToString();

            var targetEnRow = targetEnRows
                .Where(f =>
                    fmaterialid.EqualsIgnoreCase(f["fmaterialid"]?.ToString())
                    && fattrinfo_e.EqualsIgnoreCase(f["fattrinfo_e"]?.ToString())
                    && fcustomdesc.EqualsIgnoreCase(f["fcustomdesc"]?.ToString())
                    && funitid.EqualsIgnoreCase(f["funitid"]?.ToString())
                    && fmtono.EqualsIgnoreCase(f["fmtono"]?.ToString())
                    && sourceEnId.EqualsIgnoreCase(f["fsourceentryid"]?.ToString())
                )
                //.OrderByDescending(f => sourceEnId.EqualsIgnoreCase(f["fsourceentryid"]?.ToString()))
                .ToList();

            return targetEnRow;
        }






        /// <summary>
        /// 获取上游单据信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="sourceBillForm"></param>
        /// <param name="demandBillDatas"></param>
        /// <returns></returns>
        private List<DynamicObject> GetSourceBillDatas(UserContext userCtx, HtmlForm demandBillForm, HtmlForm sourceBillForm, List<ReserveSettingInfo> reserveSettingInfos)
        {
            var fldKey = GetLinkFldKey(userCtx, sourceBillForm, demandBillForm);
            var fld = demandBillForm.GetField(fldKey);
            if (fld == null)
            {
                throw new Exception("该业务未支持预留转移：设置源单关联字段不存在");
            }

            var demandBillDatas = (from p in reserveSettingInfos
                                   where p.DemandBillData != null
                                   select p.DemandBillData as DynamicObject).ToList();

            var dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(userCtx, demandBillDatas, demandBillForm);
            var enRows = dataEntitySet.FindByEntityKey(fld.EntityKey);

            var fsourceinterid = (from p in enRows
                                  select p.DataEntity[fld.PropertyName]?.ToString()
                                  ).Where(f => !f.IsNullOrEmptyOrWhiteSpace()).ToList();

            if (sourceBillForm == null || fsourceinterid == null || !fsourceinterid.Any())
            {
                return null;
            }

            var result = new List<DynamicObject>();
            if (fldKey.EqualsIgnoreCase("fsourcenumber"))
            {
                result = userCtx.LoadBizDataByNo(sourceBillForm.Id, "fbillno", fsourceinterid);
            }
            else
            {
                result = userCtx.LoadBizDataById(sourceBillForm.Id, fsourceinterid);
            }

            return result;
        }


        private string GetLinkFldKey(UserContext userCtx, HtmlForm sourceBillMeta, HtmlForm demandBillForm)
        {
            var srcFormId = sourceBillMeta.Id;
            var targetFormId = demandBillForm.Id;
            if (targetFormId.EqualsIgnoreCase("stk_sostockout") && srcFormId.EqualsIgnoreCase("ydj_order"))//销售合同----销售出库
            {
                return "fsourceinterid";
            }
            else if (targetFormId.EqualsIgnoreCase("stk_sostockout") && srcFormId.EqualsIgnoreCase("sal_deliverynotice"))//发货通知单----销售出库单
            {
                return "fsourceinterid";
            }
            else if (targetFormId.EqualsIgnoreCase("stk_otherstockout") && srcFormId.EqualsIgnoreCase("aft_repairorder"))//售后维修单----其他出库单
            {
                return "fsourceinterid";
            }
            else if (targetFormId.EqualsIgnoreCase("stk_otherstockout") && srcFormId.EqualsIgnoreCase("stk_otherstockoutreq"))//其他出库申请单----其他出库单
            {
                return "fsourceinterid";
            }
            else if (targetFormId.EqualsIgnoreCase("stk_postockreturn") && srcFormId.EqualsIgnoreCase("pur_returnnotice"))//采购通知单----采购退货
            {
                return "fsourceinterid";
            }
            else if (targetFormId.EqualsIgnoreCase("ydj_order") && srcFormId.EqualsIgnoreCase("ydj_saleintention"))//订购意向书----销售合同
            {
                return "fsourcenumber";
            }
            else if (targetFormId.EqualsIgnoreCase("ydj_order") && srcFormId.EqualsIgnoreCase("sal_deliverynotice"))//销售合同---发货通知单

            {
                return "fsourceinterid";
            }
            else if (targetFormId.EqualsIgnoreCase("stk_inventorytransfer") && srcFormId.EqualsIgnoreCase("ydj_order"))//销售合同----库存调拨单
            {
                return "fsourceinterid";
            }

            throw new Exception("该业务未支持预留转移");
        }


        /// <summary>
        /// 获取上游单据信息
        /// </summary>
        /// <param name="userCtx"></param> 
        /// <param name="demandBillDatas"></param>
        /// <returns></returns>
        private HtmlForm GetSourceFormMeta(UserContext userCtx, IEnumerable<ReserveSettingInfo> reserveSettingInfos)
        {
            var demandBillDatas = (from p in reserveSettingInfos
                                   where p.DemandBillData != null
                                   select p.DemandBillData as DynamicObject).ToList();


            return GetSourceFormMeta(userCtx, demandBillDatas);
        }


        /// <summary>
        /// 获取上游单据信息
        /// </summary>
        /// <param name="userCtx"></param> 
        /// <param name="demandBillDatas"></param>
        /// <returns></returns>
        private HtmlForm GetSourceFormMeta(UserContext userCtx, IEnumerable<DynamicObject> demandBillDatas)
        {
            var fsourceformid = (from p in demandBillDatas
                                 where p["fsourcetype"].IsNullOrEmptyOrWhiteSpace() == false
                                 select p["fsourcetype"].ToString())?.FirstOrDefault();

            if (fsourceformid.IsNullOrEmptyOrWhiteSpace())
            {
                return null;
            }

            var meta = this.MetaModelService.LoadFormModel(userCtx, fsourceformid);

            return meta;
        }



        /// <summary>
        /// 判断是否需要做预留转移
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="demandBillForm"></param>
        /// <param name="sourceBillMeta"></param>
        /// <returns></returns>
        private bool CheckNeedTransferBiz(UserContext userCtx, HtmlForm demandBillForm, HtmlForm sourceBillMeta)
        {
            if (demandBillForm == null || sourceBillMeta == null)
            {
                return false;
            }

            var srcFormId = sourceBillMeta.Id;
            var targetFormId = demandBillForm.Id;
            if ((targetFormId.EqualsIgnoreCase("stk_sostockout") && srcFormId.EqualsIgnoreCase("ydj_order"))//销售合同----销售出库
                || (targetFormId.EqualsIgnoreCase("stk_sostockout") && srcFormId.EqualsIgnoreCase("sal_deliverynotice"))//发货通知单----销售出库单
                || (targetFormId.EqualsIgnoreCase("stk_otherstockout") && srcFormId.EqualsIgnoreCase("aft_repairorder"))//售后维修单----其他出库单
                || (targetFormId.EqualsIgnoreCase("stk_otherstockout") && srcFormId.EqualsIgnoreCase("stk_otherstockoutreq"))//其他出库申请单----其他出库单
                || (targetFormId.EqualsIgnoreCase("stk_postockreturn") && srcFormId.EqualsIgnoreCase("pur_returnnotice"))//采购通知单----采购退货
                || (targetFormId.EqualsIgnoreCase("ydj_order") && srcFormId.EqualsIgnoreCase("ydj_saleintention"))//订购意向书----销售合同
                || (targetFormId.EqualsIgnoreCase("ydj_order") && srcFormId.EqualsIgnoreCase("sal_deliverynotice"))//销售合同---发货通知单
                || (targetFormId.EqualsIgnoreCase("stk_inventorytransfer") && srcFormId.EqualsIgnoreCase("ydj_order"))//销售合同---库存调拨单
                )
            {
                return true;
            }

            return false;
        }





    }



}