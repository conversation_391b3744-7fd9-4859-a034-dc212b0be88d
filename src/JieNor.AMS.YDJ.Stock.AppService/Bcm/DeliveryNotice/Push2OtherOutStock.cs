using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Bcm.DeliveryNotice
{
    [InjectService]
    [FormId("stk_otherstockoutreq")]
    [OperationNo("push2otheroutstock")]
    public class Push2OtherOutStock : AbstractPushInOutStockForBCMServicePlugIn
    {
        protected override string GetConvertRuleId()
        {
            return "stk_otherstockoutreq2stk_otherstockout";
        }
    }
}
