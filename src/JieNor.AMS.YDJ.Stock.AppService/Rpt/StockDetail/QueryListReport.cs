using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Rpt.StockDetail
{
    /// <summary>
    /// 采购订单执行情况报表：准备报表动态列模型
    /// </summary>
    [InjectService]
    [FormId("rpt_stockdetail")]
    [OperationNo("QueryListReport")]
    public class QueryListReport : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            //向当前报表模型中增加一个自定义字段
            //this.GetOrAddReportField("ftestfield", "测试字段", HtmlElementType.HtmlField_TextField);

            var invFlexId = this.GetQueryOrSimpleParam<string>("invFlexId");
            if (!invFlexId.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.InvFlexId = invFlexId;
            }
            var fmaterialidfrom = this.GetQueryOrSimpleParam<string>("fmaterialidfrom");
            if (!fmaterialidfrom.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.fmaterialidfrom = fmaterialidfrom;
            }
            var fstorehouseidfrom = this.GetQueryOrSimpleParam<string>("fstorehouseidfrom");
            if (!fstorehouseidfrom.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.fstorehouseidfrom = fstorehouseidfrom;
            }
            var fstorelocationidfrom = this.GetQueryOrSimpleParam<string>("fstorelocationidfrom");
            if (!fstorelocationidfrom.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.fstorelocationidfrom = fstorelocationidfrom;
            }
            var fdatefrom = this.GetQueryOrSimpleParam<string>("fdatefrom");
            if (!fdatefrom.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.fdatefrom = fdatefrom;
            }
            var fdateto = this.GetQueryOrSimpleParam<string>("fdateto");
            if (!fdatefrom.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.fdateto = fdateto;
            }
            var fattrinfo = this.GetQueryOrSimpleParam<string>("fattrinfo");
            if (!fattrinfo.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.fattrinfo = fattrinfo;
            }
            var fattrinfo_e = this.GetQueryOrSimpleParam<string>("fattrinfo_e");
            if (!fattrinfo_e.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.fattrinfo_e = fattrinfo_e;
            }
            var fcustomdesc = this.GetQueryOrSimpleParam<string>("fcustomdesc");
            if (!fcustomdesc.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.fcustomdesc = fcustomdesc;
            }
            var funitid = this.GetQueryOrSimpleParam<string>("funitid");
            if (!funitid.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.funitid = funitid;
            }
            //this.PageSession.fattrinfo_e = this.GetQueryOrSimpleParam<string>("fattrinfo_e");
            //this.PageSession.InvFlexId = this.GetQueryOrSimpleParam<string>("invFlexId");
            //this.PageSession.fmaterialidfrom = this.GetQueryOrSimpleParam<string>("fmaterialidfrom");
            //this.PageSession.fstorehouseidfrom = this.GetQueryOrSimpleParam<string>("fstorehouseidfrom");
            //this.PageSession.fstorelocationidfrom = this.GetQueryOrSimpleParam<string>("fstorelocationidfrom");
            //this.PageSession.fdatefrom = this.GetQueryOrSimpleParam<string>("fdatefrom");
            //this.PageSession.fdateto = this.GetQueryOrSimpleParam<string>("fdateto");
            //this.PageSession.fattrinfo = this.GetQueryOrSimpleParam<string>("fattrinfo");
            //this.PageSession.fcustomdesc = this.GetQueryOrSimpleParam<string>("fcustomdesc");
            //this.PageSession.funitid = this.GetQueryOrSimpleParam<string>("funitid");
        }
    }
}
