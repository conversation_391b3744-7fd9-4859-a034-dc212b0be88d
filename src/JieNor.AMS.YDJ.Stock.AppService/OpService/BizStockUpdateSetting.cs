using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.FileServer;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Stock.AppService.OpService
{


    /// <summary>
    /// 保存时：获取相关库存更新参数
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("Save")]
    public partial class BizStockUpdateSetting_Save : AbstractBizStockUpdateSetting
    {

        /// <summary>
        /// 操作关联服务
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        {
            if (e == null || e.Services == null || e.Services.Count == 0)
            {
                return;
            }

            var invSvr = e.Services.Where(f => "updateinventory".EqualsIgnoreCase(f.ServiceAlias)
                                               || f.ServiceId == YDJHtmlElementType.HtmlBizService_UpdateInventory)?.ToList();
            if (invSvr != null && invSvr.Any())
            {
                foreach (var item in invSvr)
                {
                    e.Services.Remove(item);
                }

                //保存时的库存更新参数，与审核时的库存参数一致
                var invSettings = GetInvStockUpdateSetting("audit");
                e.Services.AddRange(invSettings);
                if (invSettings.Any())
                {
                    BeginWriteStockLog(e.Services);
                }
            }
        }

    }


    /// <summary>
    /// 提交时：获取相关库存更新参数
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("submit")]
    public partial class BizStockUpdateSetting_Sumit : BizStockUpdateSetting_Save
    {
    }



    /// <summary>
    /// 提交时：获取相关库存更新参数
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("SubmitFlow")]
    public partial class BizStockUpdateSetting_SubmitFlow : BizStockUpdateSetting_Save
    {
    }

    /// <summary>
    /// 反作废时：获取相关库存更新参数
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("UnCancel")]
    public partial class BizStockUpdateSetting_UnCancel : BizStockUpdateSetting_Save
    {
    }



    /// <summary>
    /// 撤销时：获取相关库存更新参数
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("UnSubmit")]
    public partial class BizStockUpdateSetting_UnSubmit : AbstractBizStockUpdateSetting
    {

        /// <summary>
        /// 操作关联服务
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        {
            if (e == null || e.Services == null || e.Services.Count == 0)
            {
                return;
            }

            var invSvr = e.Services.Where(f => "updateinventory".EqualsIgnoreCase(f.ServiceAlias)
                                               || f.ServiceId == YDJHtmlElementType.HtmlBizService_UpdateInventory)?.ToList();
            if (invSvr != null && invSvr.Any())
            {
                foreach (var item in invSvr)
                {
                    e.Services.Remove(item);
                }

                //撤销时的库存更新参数，与反审核时的库存参数一致
                var invSettings = GetInvStockUpdateSetting("unaudit");
                e.Services.AddRange(invSettings);

                if (invSettings.Any())
                {
                    BeginWriteStockLog(e.Services);
                }
            }
        }

    }


    /// <summary>
    /// 驳回时：获取相关库存更新参数
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("RejectFlow")]
    public partial class BizStockUpdateSetting_RejectFlow : BizStockUpdateSetting_UnSubmit
    {
    }


    /// <summary>
    /// 删除时：获取相关库存更新参数
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("delete")]
    public partial class BizStockUpdateSetting_Delete : AbstractBizStockUpdateSetting
    {

        /// <summary>
        /// 操作关联服务
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        {
            if (e == null || e.Services == null || e.Services.Count == 0)
            {
                return;
            }

            var invSvr = e.Services.Where(f => "updateinventory".EqualsIgnoreCase(f.ServiceAlias)
                                               || f.ServiceId == YDJHtmlElementType.HtmlBizService_UpdateInventory)?.ToList();
            if (invSvr != null && invSvr.Any())
            {
                foreach (var item in invSvr)
                {
                    e.Services.Remove(item);
                }

                //删除时的库存更新参数，取删除操作上配置的，如果取不到，取反审核上配置
                var invSettings = GetInvStockUpdateSetting("delete");
                if (invSettings == null || invSettings.Count == 0)
                {
                    invSettings = GetInvStockUpdateSetting("unaudit");
                }
                e.Services.AddRange(invSettings);

                if (invSettings.Any())
                {
                    BeginWriteStockLog(e.Services);
                }
            }
        }

    }



    /// <summary>
    /// 作废时：获取相关库存更新参数
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("cancel")]
    public partial class BizStockUpdateSetting_Cancel : BizStockUpdateSetting_UnSubmit
    {
    }


    /// <summary>
    /// 审核时：获取相关库存更新参数
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("Audit")]
    public partial class BizStockUpdateSetting_Audit : AbstractBizStockUpdateSetting
    {

        /// <summary>
        /// 操作关联服务
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        {
            if (e == null || e.Services == null || e.Services.Count == 0)
            {
                return;
            }

            var invSvr = e.Services.Where(f => "updateinventory".EqualsIgnoreCase(f.ServiceAlias)
                                               || f.ServiceId == YDJHtmlElementType.HtmlBizService_UpdateInventory)?.ToList();
            if (invSvr != null && invSvr.Any())
            {
                foreach (var item in invSvr)
                {
                    e.Services.Remove(item);
                }
            }

            var invSettings = GetInvStockUpdateSetting("audit");
            e.Services.AddRange(invSettings);

            if (invSettings.Any())
            {
                BeginWriteStockLog(e.Services);
            }
        }

    }




    /// <summary>
    /// 反审核时：获取相关库存更新参数
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("UnAudit")]
    public partial class BizStockUpdateSetting_UnAudit : AbstractBizStockUpdateSetting
    {
        /// <summary>
        /// 操作关联服务
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        {
            if (e == null || e.Services == null || e.Services.Count == 0)
            {
                return;
            }

            var invSvr = e.Services.Where(f => "updateinventory".EqualsIgnoreCase(f.ServiceAlias)
                                               || f.ServiceId == YDJHtmlElementType.HtmlBizService_UpdateInventory)?.ToList();
            if (invSvr != null && invSvr.Any())
            {
                foreach (var item in invSvr)
                {
                    e.Services.Remove(item);
                }

                var invSettings = GetInvStockUpdateSetting("UnAudit");
                e.Services.AddRange(invSettings);

                if (invSettings.Any())
                {
                    BeginWriteStockLog(e.Services);
                }
            }
            else//反审核操作服务里面没定义的，但是在保存、提交、反作废上面定义了更新库存的，则这时候反审核不需要取消库存更新，否则保存时或提交时更新库存的服务就失效了
            {
                if (IsUpdateInvSetting(new List<string>() { "Submit", "Save" }))
                {
                    invSvr = e.Services.Where(f => f.ServiceId == YDJHtmlElementType.HtmlBizService_UpdateInventory)?.ToList();
                    foreach (var item in invSvr)
                    {
                        e.Services.Remove(item);
                    }
                }
            }
        }

    }






    public abstract class AbstractBizStockUpdateSetting : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            EndWriteStockLog();
        }


        /// <summary>
        /// 从模型上获取定义的库存更新参数
        /// </summary>
        /// <param name="opCode"></param>
        /// <returns></returns>
        internal List<FormServiceDesc> GetInvStockUpdateSetting(string opCode = "audit")
        {
            var newInvSvr = new List<FormServiceDesc>();
            var htmlOperation = this.HtmlForm.FormOperations.FirstOrDefault(o => o.Id.EqualsIgnoreCase(opCode));
            if (htmlOperation == null) return newInvSvr;
            foreach (var opService in htmlOperation.OperationServices)
            {
                if (opService.ServiceId == YDJHtmlElementType.HtmlBizService_UpdateInventory)
                {
                    newInvSvr.Add(new FormServiceDesc()
                    {
                        ServiceId = opService.ServiceId,
                        ParamString = opService.Parameter,
                        Condition = opService.Precondition,
                    });
                }
            }

            return newInvSvr;
        }



        /// <summary>
        /// 操作配置中是否配置了相关的库存更新服务
        /// </summary>
        /// <param name="opCode">操作码</param>
        /// <returns></returns>
        internal bool IsUpdateInvSetting(List<string> opCode)
        {
            var pkIdSql = @"select a.fid from t_sys_opserconfig a  
                            inner join t_sys_opserconfigentry b on a.fid=b.fid and b.fopser='updateinventory'
                            where a.fbizobject='{0}' and a.fbizop in ({1}) and a.fforbidstatus='0' 
                                 and ( fmainorgid='0' or fmainorgid='{2}' or fmainorgid='{3}')
                            ".Fmt(this.OperationContext.HtmlForm.Id, opCode.JoinEx(",", true), this.Context.Company, this.Context.TopCompanyId);
            var pkIds = this.DBService.ExecuteDynamicObject(this.Context, $"{pkIdSql} and fmainorgid='{this.Context.Company}'", null)?.Select(o => o[0])?.ToArray();

            return !(pkIds == null || pkIds.Length == 0);
        }

        internal void BeginWriteStockLog(IEnumerable<FormServiceDesc> serivces)
        {

            try
            {
                StringBuilder log = new StringBuilder();
                log.AppendLine();
                log.AppendLine("---------------------库存更新开始事件---------------------------");
                log.AppendLine();

                log.AppendLine("[HtmlForm]:" + this.HtmlForm.Id);
                log.AppendLine("[HtmlForm]:" + this.HtmlForm.Caption);
                log.AppendLine("[OperationNo]:" + this.OperationNo);
                log.AppendLine("[Service]:" + serivces?.ToJson(true));

                this.Option.SetVariableValue("__StockLog__", log);
            }
            catch { }
        }

        internal void EndWriteStockLog()
        {
            try
            {
                if (this.Option.TryGetVariableValue("__StockLog__", out StringBuilder log))
                {
                    var actualTimes = this.Option.GetVariableValue("__StockTimes__", 0);
                    var expectedTimes = GetStockBillStockUpdateTimes();

                    if (actualTimes != expectedTimes)
                    {
                        log.AppendLine();
                        log.AppendLine("实际库存更新次数:" + actualTimes);
                        log.AppendLine("期望库存更新次数:" + expectedTimes);

                        log.AppendLine("---------------------库存更新结束事件---------------------------");

                        string fileName =
                            $"stock/{(this.Context.Companys.FirstOrDefault(s => s.CompanyId.EqualsIgnoreCase(this.Context.Company))?.CompanyNumber ?? "")}_{DateTime.Now.ToString("HHmmssfff")}";

                        this.Logger.WriteLogToFile(log.ToString(), fileName);
                    }
                }
            }
            catch { }
        }

        internal int GetStockBillStockUpdateTimes()
        {
            switch (this.HtmlForm.Id?.ToLower())
            {
                case "stk_inventorytransfer":
                case "stk_inventoryverify":
                    return 2;
                default:
                    return 1;
            }
        }
    }








}