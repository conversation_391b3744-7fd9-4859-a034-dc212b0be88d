using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 总部对账单服务接口定义
    /// </summary>
    public interface IStatementService
    {
        /// <summary>
        /// 对账相符
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities">总部对账单列表</param>
        void ConfirmBill(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntities);

        /// <summary>
        /// 对账不符
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities">总部对账单列表</param>
        void UnconfirmBill(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntities);

        /// <summary>
        /// 对账撤销
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities">总部对账单列表</param>
        void UndoBill(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntities);
    }
}
