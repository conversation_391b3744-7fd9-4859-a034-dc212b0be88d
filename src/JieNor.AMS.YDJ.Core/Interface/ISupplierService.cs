using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 供应商服务接口定义
    /// </summary>
    public interface ISupplierService
    {
        /// <summary>
        /// 获取总部直营模式下的协同企业目标地址
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="cooCompanyId">协同企业Id</param>
        /// <param name="cooProductId">协同产品Id</param>
        /// <returns>协同企业目标地址</returns>
        TargetSEP GetSyncTargetSEP(UserContext userCtx, string cooCompanyId, string cooProductId);
    }
}