using System.Collections.Generic;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;

namespace JieNor.AMS.YDJ.Core.DataEntity
{
    /// <summary>
    /// 预留借货设置信息
    /// </summary>
    public class ReserveBorrowSettingInfo
    {
        /// <summary>
        /// 需求明细
        /// </summary>
        public List<ReserveBorrowDemandInfo> DemandInfos { get; set; } = new List<ReserveBorrowDemandInfo>();

        /// <summary>
        /// 借出需求单标识
        /// </summary>
        public string CreditorDemandFormId { get; set; }

        /// <summary>
        /// 借出需求单内码
        /// </summary>
        public string CreditorDemandBillId { get; set; }

        /// <summary>
        /// 贷方需求单
        /// </summary>
        public object CreditorDemandBill { get; set; }

        /// <summary>
        /// 借出预留单内码
        /// </summary>
        public string CreditorReserveBillId { get; set; }

        /// <summary>
        /// 借出预留单
        /// </summary>
        public object CreditorReserveBill { get; set; }
    }

    /// <summary>
    /// 预留借货设置-----需求明细信息
    /// </summary>
    public class ReserveBorrowDemandInfo
    {
        /// <summary>
        /// 行号
        /// </summary>
        public int Seq { get; set; }

        #region 库存维度

        /// <summary>
        /// 物料id
        /// </summary>
        public string MaterialId { get; set; }

        /// <summary>
        /// 辅助属性
        /// </summary>
        public string AttrInfo { get; set; }
        /// <summary>
        /// 辅助属性-扩展
        /// </summary>
        public string AttrInfo_e { get; set; }

        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; }

        /// <summary>
        /// 基本计量单位
        /// </summary>
        public string UnitId { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        public string BizUnitId { get; set; }

        /// <summary>
        /// 物流跟踪号
        /// </summary>
        public string Mtono { get; set; }

        /// <summary>
        /// 仓库 
        /// </summary>
        public string StoreHouseId { get; set; }

        /// <summary>
        ///  库存状态
        /// </summary>
        public string StockStatus { get; set; }

        /// <summary>
        /// 业绩品牌
        /// </summary>
        public string ResultBrandId { get; set; }

        #endregion

        /// <summary>
        /// 本次需释放预留量
        /// </summary>
        public decimal BizReleaseQty { get; set; }

        /// <summary>
        /// 基本单位本次需释放预留量
        /// </summary>
        public decimal ReleaseQty { get; set; }

        /// <summary>
        /// 借入客户内码
        /// </summary>
        public string BorrowerCustomerId { get; set; }

        /// <summary>
        /// 借入需求单内码
        /// </summary>
        public string BorrowerDemandFormId { get; set; } = "ydj_order";

        /// <summary>
        /// 借入需求单内码
        /// </summary>
        public string BorrowerDemandBillId { get; set; }

        /// <summary>
        /// 借入需求单编码
        /// </summary>
        public string BorrowerDemandBillNo { get; set; }

        /// <summary>
        /// 借入需求单数据包
        /// </summary>
        public object BorrowerDemandBill { get; set; }

        /// <summary>
        /// 借入需求单的需求明细内码
        /// </summary>
        public string BorrowerDemandEntryId { get; set; }

        /// <summary>
        /// 借入需求单的需求明细数据包
        /// </summary>
        public object BorrowerDemandEntry { get; set; }

        /// <summary>
        /// 借出需求单标识
        /// </summary>
        public string CreditorDemandFormId { get; set; }

        /// <summary>
        /// 借出需求单内码
        /// </summary>
        public string CreditorDemandBillId { get; set; }

        /// <summary>
        /// 借出需求单编码
        /// </summary>
        public string CreditorDemandBillNo { get; set; }

        /// <summary>
        /// 借出需求单的需求明细内码
        /// </summary>
        public string CreditorDemandEntryId { get; set; }

        /// <summary>
        /// 借出预留单id
        /// </summary>
        public string CreditorReserveBillId { get; set; }

        /// <summary>
        /// 借出预留单的需求明细行id
        /// </summary>
        public string CreditorReserveEntryId { get; set; }

        /// <summary>
        /// 是否成功转移
        /// </summary>
        public bool IsSuccessTransfer { get; set; }
    }
}
