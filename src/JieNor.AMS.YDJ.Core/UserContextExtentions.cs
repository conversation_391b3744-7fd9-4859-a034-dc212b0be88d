using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataEntity.BillType;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json;

namespace JieNor.Framework
{
    /// <summary>
    /// 用户上下文扩展类
    /// </summary>
    public static class UserContextExtentions
    {
        /// <summary>
        /// 创建经销商用户上下文
        /// </summary>
        /// <param name="userCtx">当前用户上下文</param>
        /// <param name="agentId">经销商id</param>
        /// <returns></returns>
        public static UserContext CreateAgentDBContext(this UserContext userCtx, string agentId)
        {
            if (string.IsNullOrWhiteSpace(agentId))
                return userCtx;
            UserContext ctx = new UserContext();
            UserAuthTicket session = new UserAuthTicket();

            // 用系统预设的管理员身份操作
            session.UserId = "sysadmin";
            session.DisplayName = "系统管理员";
            session.UserName = "系统管理员";

            session.Product = userCtx.ProductId();
            session.Company = agentId;
            session.BizOrgId = agentId;
            session.TopCompanyId = userCtx.TopCompanyId;
            session.ParentCompanyId = userCtx.TopCompanyId;
            var _parentCompanyId = userCtx.GetAgentParentCompanyId(agentId);
            if (!_parentCompanyId.IsNullOrEmptyOrWhiteSpace())
            {
                session.ParentCompanyId = _parentCompanyId;
            }
            session.Companys = userCtx.GetAllCompanys().Where(s => s.Value.CompanyId == agentId).Select(s => s.Value).ToList();

            // 初始化SessionId
            session.Id = Guid.NewGuid().ToString("N");
            session.IsDirectSale = CommonUtil.GetCurrentAgentIsDirectSale(session.BizOrgId);
            ctx.SetUserSession(session);
            ctx.RequestId = userCtx.RequestId;
            ctx.Container = ServiceStack.HostContext.TryResolve<IServiceContainer>().BeginLifetimeScope(userCtx.RequestId);

            return ctx;
        }

        /// <summary>
        /// 创建经销商用户上下文
        /// </summary>
        /// <param name="userCtx">当前用户上下文</param>
        /// <param name="agentNo">经销商编码</param>
        /// <returns></returns>
        public static UserContext CreateAgentDBContextByNo(this UserContext userCtx, string agentNo)
        {
            if (agentNo.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("经销商编码不能为空！");

            var company = userCtx.GetAllCompanys().Values.FirstOrDefault(s => s.CompanyNumber.EqualsIgnoreCase(agentNo));
            if (company == null) throw new BusinessException($"经销商【{agentNo}】不存在！");

            UserContext ctx = new UserContext();
            UserAuthTicket session = new UserAuthTicket();

            // 用系统预设的管理员身份操作
            session.UserId = "sysadmin";
            session.DisplayName = "系统管理员";
            session.UserName = "系统管理员";

            session.Product = userCtx.ProductId();
            session.Company = company.CompanyId;
            session.BizOrgId = company.CompanyId;
            session.TopCompanyId = userCtx.TopCompanyId;
            session.ParentCompanyId = userCtx.TopCompanyId;
            var _parentCompanyId = userCtx.GetAgentParentCompanyId(company.CompanyId);
            if (!_parentCompanyId.IsNullOrEmptyOrWhiteSpace())
            {
                session.ParentCompanyId = _parentCompanyId;
            }
            session.Companys = new List<CompanyDCInfo> { company };

            // 初始化SessionId
            session.Id = Guid.NewGuid().ToString("N");
            session.IsDirectSale = CommonUtil.GetCurrentAgentIsDirectSale(session.BizOrgId);
            ctx.SetUserSession(session);
            ctx.RequestId = userCtx.RequestId;
            ctx.Container = ServiceStack.HostContext.TryResolve<IServiceContainer>().BeginLifetimeScope(userCtx.RequestId);

            return ctx;
        }

        public static UserContext CreaeteaAgentDBContextWithUser(this UserContext userCtx, string agentId)
        {
            return CreaeteaAgentDBContextWithUser(userCtx, agentId, userCtx.UserId);
        }

        public static UserContext CreaeteaAgentDBContextWithUser(this UserContext userCtx, string agentId, string userId)
        {
            if (string.IsNullOrWhiteSpace(agentId))
                return userCtx;
            var searchsql = $"select top 1 a.fid,a.fname,a.fnumber from t_sec_user a with(nolock) left join t_sec_user b with(nolock) on a.fnumber = b.fnumber where b.fid = '{userId}' and a.fmainorgid = '{agentId}'";
            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, searchsql).FirstOrDefault();
            if (dynObjs == null)
                return userCtx;
            UserContext ctx = new UserContext();
            UserAuthTicket session = new UserAuthTicket();

            // 用系统预设的管理员身份操作
            session.UserId = Convert.ToString(dynObjs["fid"]);
            session.DisplayName = Convert.ToString(dynObjs["fname"]);
            session.UserName = Convert.ToString(dynObjs["fnumber"]);

            session.Product = userCtx.ProductId();
            session.Company = agentId;
            session.BizOrgId = agentId;
            session.TopCompanyId = userCtx.TopCompanyId;
            session.ParentCompanyId = userCtx.TopCompanyId;
            var _parentCompanyId = userCtx.GetAgentParentCompanyId(agentId);
            if (!_parentCompanyId.IsNullOrEmptyOrWhiteSpace())
            {
                session.ParentCompanyId = _parentCompanyId;
            }
            session.Companys = userCtx.GetAllCompanys().Where(s => s.Value.CompanyId == agentId).Select(s => s.Value).ToList();

            // 初始化SessionId
            session.Id = Guid.NewGuid().ToString("N");
            session.IsDirectSale = CommonUtil.GetCurrentAgentIsDirectSale(session.BizOrgId);
            ctx.SetUserSession(session);
            ctx.RequestId = userCtx.RequestId;
            ctx.Container = ServiceStack.HostContext.TryResolve<IServiceContainer>().BeginLifetimeScope(userCtx.RequestId);

            return ctx;
        }

        /// <summary>
        /// 通过部门Id，获取部门对应门店的组织id
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="deptId">部门Id</param>
        /// <returns>对应门店的组织id</returns>
        public static string GetStoreOrgIdByDept(this UserContext ctx, string deptId)
        {
            return ProductDataIsolateHelper.GetStoreOrgIdByDept(ctx, deptId);
        }



        /// <summary>
        /// 通过部门Id，获取部门对应门店的经销商组织id
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="deptId">部门Id</param>
        /// <returns>对应门店的经销商组织id</returns>
        public static string GetAgentOrgIdByDept(UserContext ctx, string deptId)
        {
            return ProductDataIsolateHelper.GetAgentOrgIdByDept(ctx, deptId);
        }


        /// <summary>
        /// 依据登录用户信息，获取用户可以查看到的商品信息。
        /// 用户所属组织是总部，则可以看到总部的商品信息;
        /// 用户所属组织是分公司，按 商品上维护的使用组织进行隔离
        /// 用户是经销商或门店的，可以看到的商品信息= 总部授权商品 + 自己建的商品
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <returns>临时表或视图：可以看到的商品Id</returns>
        public static string GetAuthProductDataPKID(this UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            return new ProductDataIsolateHelper().GetAuthProductDataPKID(ctx, rulePara);
        }

        /// <summary>
        /// 获取经销商或门店用户户可以看到的品牌信息= 总部授权品牌 + 自己建的品牌
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns>临时表或视图：可以看到的品牌Id，总部用户返回空，由外层通用接口处理</returns>
        public static Tuple<string, string> GetAgenAuthBrandDataPKID(this UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            return new ProductDataIsolateHelper().GetAgenAuthBrandDataPKID(ctx, rulePara);
        }


        /// <summary>
        /// 获取经销商或门店用户户可以看到的品牌信息= 总部授权品牌 + 自己建的品牌
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns>临时表或视图：可以看到的品牌Id，总部用户返回空，由外层通用接口处理</returns>
        public static Tuple<string, string> GetAgenAuthSeriDataPKID(this UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            return new ProductDataIsolateHelper().GetAgenAuthSeriDataPKID(ctx, rulePara);
        }

        /// <summary>
        /// 指定的经销商是否启用经销价
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <returns>是否启用经销价</returns>
        public static bool IsEnableSellPrice(this UserContext ctx)
        {
            if (ctx.IsTopOrg) return false;

            // 先取缓存
            var cache = ctx.Container.GetService<IRedisCache>();
            var cacheKey = "enablesellprice:{0}".Fmt(ctx.Company);
            var enableSellPrice = cache.Get<string>(ctx, cacheKey);
            if (!enableSellPrice.IsNullOrEmptyOrWhiteSpace())
            {
                return enableSellPrice.EqualsIgnoreCase("1");
            }

            var agentObj = ctx.LoadBizBillHeadDataById("bas_agent", ctx.Company, "fenablesellprice");
            enableSellPrice = Convert.ToString(agentObj?["fenablesellprice"]);

            // 缓存起来
            cache.Set(cacheKey, enableSellPrice);

            return enableSellPrice.EqualsIgnoreCase("1");
        }

        /// <summary>
        /// 清除指定经销商的是否启用经销价标记缓存
        /// </summary>
        /// <param name="ctx">上下文</param>
        public static void ClearEnableSellPriceCache(this UserContext ctx)
        {
            if (ctx.IsTopOrg) return;

            var cache = ctx.Container.GetService<IRedisCache>();
            var cacheKey = "enablesellprice:{0}".Fmt(ctx.Company);
            cache.Remove(ctx, cacheKey);
        }

        /// <summary>
        /// 获取当前经销商业务对象的单据类型
        /// </summary>
        /// <param name="userCtx">经销商用户上下文</param>
        /// <param name="bizObject">业务对象</param>
        /// <param name="fid">主键</param>
        /// <returns></returns>
        public static BillTypeInfo GetBillTypeByBizObject(this UserContext userCtx, string bizObject, string fid)
        {
            var svc = userCtx.Container.GetService<IBillTypeService>();
            var billTypeInfo = svc.GetBillTypeInfor(userCtx, fid);

            if (billTypeInfo != null)
            {
                var allBillTypeInfos = svc.GetBillTypeInfors(userCtx, billTypeInfo.fbizobject);
                var matchBillTypes = allBillTypeInfos.Where(s => s.fname.EqualsIgnoreCase(billTypeInfo.fname));
                // 先取本地
                var local = matchBillTypes.FirstOrDefault(s => s.fmainorgid.EqualsIgnoreCase(userCtx.Company));
                if (local != null)
                {
                    return local;
                }

                // 本取总部
                var topOrg =
                    matchBillTypes.FirstOrDefault(s => s.fmainorgid.EqualsIgnoreCase(userCtx.TopCompanyId));
                if (topOrg != null)
                {
                    return topOrg;
                }

                // 任意取一个
                return matchBillTypes.FirstOrDefault();
            }

            return billTypeInfo;
        }

        /// <summary>
        /// 获取当前经销商业务对象的未禁用的单据类型
        /// 该方法与GetBillTypeByBizObject相差一个禁用条件，担心影响其他模块所以新增一个方法-2023-04-14
        /// </summary>
        /// <param name="userCtx">经销商用户上下文</param>
        /// <param name="bizObject">业务对象</param>
        /// <param name="fid">主键</param>
        /// <returns></returns>
        public static BillTypeInfo GetNotForbidBillTypeByBizObject(this UserContext userCtx, string bizObject, string fid)
        {
            var svc = userCtx.Container.GetService<IBillTypeService>();
            var billTypeInfo = svc.GetBillTypeInfor(userCtx, fid);

            if (billTypeInfo != null)
            {
                var allBillTypeInfos = svc.GetBillTypeInfors(userCtx, billTypeInfo.fbizobject);
                var matchBillTypes = allBillTypeInfos.Where(s => !s.fforbidstatus)
                    .Where(s => s.fname.EqualsIgnoreCase(billTypeInfo.fname));
                // 先取本地
                var local = matchBillTypes.FirstOrDefault(s => s.fmainorgid.EqualsIgnoreCase(userCtx.Company));
                if (local != null)
                {
                    return local;
                }

                // 本取总部
                var topOrg =
                    matchBillTypes.FirstOrDefault(s => s.fmainorgid.EqualsIgnoreCase(userCtx.TopCompanyId));
                if (topOrg != null)
                {
                    return topOrg;
                }

                // 任意取一个
                return matchBillTypes.FirstOrDefault();
            }

            return billTypeInfo;
        }

        /// <summary>
        /// 根据（过滤条件+数据隔离规则）批量加载业务单据头的指定字段数据
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formId">业务表单标识</param>
        /// <param name="filter">过滤条件</param>
        /// <param name="selectFieldName">指定要查询的单据头字段名称，多个字段用逗号分隔，比如：fname,fnumber</param>
        /// <param name="paramList">SQL参数集合</param>
        /// <returns>返回查询到的动态数据包，数据包中只包含主键ID和指定要查询的字段数据</returns>
        public static List<DynamicObject> LoadBizBillHeadDataByACLFilter(this UserContext userCtx,
            string formId,
            string filter,
            string selectFieldName = "fname",
            IEnumerable<SqlParam> paramList = null)
        {
            if (filter.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentNullException("参数 filter 不能为空！");
            }
            if (selectFieldName.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentNullException("参数 selectFieldName 不能为空！");
            }

            var metaService = userCtx.Container.GetService<IMetaModelService>();
            var formMeta = metaService.LoadFormModel(userCtx, formId);
            var orgFld = formMeta.GetField("fmainorgid");
            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "", "");

            if (formMeta.Isolate == "1" && orgFld != null)
            {
                //按组织隔离
                if (aclFilter.IsNullOrEmptyOrWhiteSpace())
                {
                    filter = "(fmainorgid='' or fmainorgid='0' or or fmainorgid='{1}' ) and ({0})".Fmt(filter, userCtx.Company);
                }
                else
                {
                    filter = "(fmainorgid='' or fmainorgid='0' or ({1})) and ({0})".Fmt(filter, aclFilter);
                }
            }
            else if (orgFld != null)
            {
                //不按组织隔离，则取总部数据及自己组织的数据
                if (aclFilter.IsNullOrEmptyOrWhiteSpace())
                {
                    filter = "(fmainorgid='' or fmainorgid='0' or fmainorgid='{1}' or fmainorgid='{2}') and ({0})".Fmt(filter, userCtx.Company, userCtx.TopCompanyId);
                }
                else
                {
                    filter = "(fmainorgid='' or fmainorgid='0' or ({1})) and ({0})".Fmt(filter, aclFilter);
                }
            }

            var sqlText = $@"select {formMeta.BillPKFldName} id,{selectFieldName} from {formMeta.BillHeadTableName} where {filter}";

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText, paramList)?.OfType<DynamicObject>()?.ToList();

            return dynObjs;
        }





        /// <summary>
        /// 获取预留单信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="demandForm">业务单据标识</param> 
        ///  <param name="demandBillPKId">业务单据id</param> 
        /// <returns></returns>
        public static DynamicObject GetReserveBill(this UserContext userCtx, string demandForm, string demandBillPKId)
        {
            var svc = userCtx.Container.GetService<IReserveUpdateService>();
            var data = svc.GetReserveBill(userCtx, demandForm, demandBillPKId);

            return data;
        }


        /// <summary>
        /// 获取预留单信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="demandForm">业务单据标识</param> 
        ///  <param name="demandBillDatas">业务单据id</param> 
        /// <returns></returns>
        public static List<DynamicObject> GetReserveBill(this UserContext userCtx, string demandForm, IEnumerable<DynamicObject> demandBillDatas)
        {
            var svc = userCtx.Container.GetService<IReserveUpdateService>();
            var data = svc.GetReserveBill(userCtx, demandForm, demandBillDatas.ToList());

            return data;
        }

        /// <summary>
        /// 登录授权检查
        /// 作者：张鹏飞
        /// 日期：2022-05-17
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        public static void LoginAuthorizationCheck(this UserContext userCtx)
        {
            /* 1.目前PC和小程序切换组织，都会执行重新登录操作，但是都只判断了用户禁用，所以：
                        1.1.在PC和小程序执行登录操作时，需判断：用户【禁用状态】=否，且对应员工【禁用状态】=否，且员工对岗位【禁用状态】=否，且员工对角色【禁用状态】=否，且员工对应的部门【禁用状态】=否，且部门对应的门店【禁用状态】=否，且门店对应的经销商【禁用状态】=否，此时才允许登录成功，否则直接提示：当前用户[账号]关联的[档案]在[企业名称]中已被禁用，请联系系统管理员进行处理！ */

            //获取当前关联的员工与部门
            if (!userCtx.IsTopOrg)
            {
                var dbSvc = userCtx.Container.GetService<IDBService>();
                string sql = string.Empty;

                // 重写逻辑：

                sql = $@"
select 
	u.fname as user_fname, u.fforbidstatus as user_fforbidstatus
	, s.fname as staff_fname, s.fforbidstatus as staff_fforbidstatus
	, p.fname as position_fname, p.fforbidstatus as position_fforbidstatus
	, d.fname as dept_fname, d.fforbidstatus as dept_fforbidstatus
	, store.fname as store_fname, store.fforbidstatus as store_fforbidstatus
from t_sec_user u with(nolock) 
left join t_bd_staff s with(nolock) on s.flinkuserid=u.fid
left join t_bd_staffentry se with(nolock) on se.fid=s.fid
left join t_ydj_position p with(nolock) on p.fid=se.fpositionid
left join t_bd_department d with(nolock) on d.fid=se.fdeptid
left join t_bas_store store with(nolock) on store.fid=d.fstore
where u.fid='{userCtx.UserId}';
";

                var dynObjs = dbSvc.ExecuteDynamicObject(userCtx, sql);

                // 判断用户状态
                var user = dynObjs.FirstOrDefault();
                if (user != null && IsForbidStatus(Convert.ToString(user["user_fforbidstatus"])))
                {
                    InvalidAuthorization(userCtx, "用户");
                }

                // 判断员工状态
                var staff = dynObjs.FirstOrDefault(s => !Convert.ToString(s["staff_fname"]).IsNullOrEmptyOrWhiteSpace());
                if (staff != null && IsForbidStatus(Convert.ToString(staff["staff_fforbidstatus"])))
                {
                    InvalidAuthorization(userCtx, "员工");
                }

                // 判断员工对应的岗位状态
                var positions = dynObjs.Where(s => !Convert.ToString(s["position_fname"]).IsNullOrEmptyOrWhiteSpace());
                if (!positions.IsNullOrEmpty())
                {
                    foreach (var position in positions)
                    {
                        if (IsForbidStatus(Convert.ToString(position["position_fforbidstatus"])))
                        {
                            InvalidAuthorization(userCtx, "员工岗位");
                        }
                    }
                }

                // 判断员工对应的部门状态
                var depts = dynObjs.Where(s => !Convert.ToString(s["dept_fname"]).IsNullOrEmptyOrWhiteSpace());
                if (!depts.IsNullOrEmpty())
                {
                    foreach (var dept in depts)
                    {
                        if (IsForbidStatus(Convert.ToString(dept["dept_fforbidstatus"])))
                        {
                            InvalidAuthorization(userCtx, "部门");
                        }
                    }
                }

                // 判断员工对应的门店状态
                var stores = dynObjs.Where(s => !Convert.ToString(s["store_fname"]).IsNullOrEmptyOrWhiteSpace());
                if (!stores.IsNullOrEmpty())
                {
                    foreach (var store in stores)
                    {
                        if (IsForbidStatus(Convert.ToString(store["store_fforbidstatus"])))
                        {
                            InvalidAuthorization(userCtx, "部门对应门店");
                        }
                    }
                }


                ////经销商
                //var sql = @"select fname from t_bas_agent with(nolock) where fid='{0}'".Fmt (userCtx.Company);
                //var currentAgent = dbSvc.ExecuteDynamicObject(userCtx, sql).FirstOrDefault();
                //if(currentAgent ==null )
                //{
                //    return;
                //}

                ////获取用户信息
                //sql = @"select fname,fforbidstatus from t_sec_user with(nolock) where fid='{0}'".Fmt(userCtx.UserId);
                //var user = dbSvc.ExecuteDynamicObject(userCtx, sql).FirstOrDefault();
                //if (!user.IsNullOrEmptyOrWhiteSpace() && user.DynamicObjectType.Properties.ContainsKey("fforbidstatus"))
                //{
                //    var userAvailable = Convert.ToString(user["fforbidstatus"]) == "0" || Convert.ToString(user["fforbidstatus"]).EqualsIgnoreCase("true");
                //    InvalidAuthorization(userCtx, userAvailable, "用户");
                //}

                ////员工
                //var staffForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "ydj_staff");
                //var staffDm = userCtx.Container.GetService<SuperOrm.DataManager.IDataManager>();
                //staffDm.InitDbContext(userCtx, staffForm.GetDynamicObjectType(userCtx));
                //var dataReader = userCtx.GetPkIdDataReader(staffForm, "fmainorgid=@fmainorgid and flinkuserid=@userid",
                //    new List<SqlParam>
                //    {
                //        new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                //        new SqlParam("@userid", System.Data.DbType.String, userCtx.UserId)
                //    });
                //var staffObj = staffDm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
                //if (!staffObj.IsNullOrEmptyOrWhiteSpace())
                //{
                //    userCtx.Container.GetService<LoadReferenceObjectManager>().Load(userCtx, staffObj.DynamicObjectType, staffObj, false);
                //    var company = userCtx.Companys.FirstOrDefault(t => t.CompanyId == userCtx.Company);
                //    if (staffObj.DynamicObjectType.Properties.ContainsKey("fforbidstatus"))
                //    {
                //        var staffAvailable = !Convert.ToBoolean(staffObj["fforbidstatus"]);
                //        InvalidAuthorization(userCtx, staffAvailable, "员工");
                //    }
                //    var refMgr = userCtx.Container.GetService<LoadReferenceObjectManager>(); //加载引用数据
                //    refMgr.Load(userCtx, staffObj.DynamicObjectType, staffObj, false);

                //    //获取员工对应的岗位                
                //    var positionEntitys = (staffObj["fentity"] as DynamicObjectCollection)?.ToList();
                //    if (!positionEntitys.IsNullOrEmptyOrWhiteSpace() && positionEntitys.Count > 0)
                //    {
                //        var positions = positionEntitys.Select(t => t["fpositionid_ref"] as DynamicObject).ToList();
                //        if (positions.Count > 0)
                //        {
                //            if (positions.Any(t => t.DynamicObjectType.Properties.ContainsKey("fforbidstatus")))
                //            {
                //                var positionAvailable = positions.Any(t => !Convert.ToBoolean(t["fforbidstatus"]));
                //                InvalidAuthorization(userCtx, positionAvailable, "员工岗位");
                //            }
                //        }

                //        //获取员工对应的部门
                //        var staffDepts = positionEntitys.Select(t => t["fdeptid_ref"] as DynamicObject).ToList();
                //        if (!staffDepts.IsNullOrEmptyOrWhiteSpace() && staffDepts.Count > 0)
                //        {
                //            refMgr.Load(userCtx, staffDepts.FirstOrDefault().DynamicObjectType, staffDepts, false);
                //            if (staffDepts.Any(t => t.DynamicObjectType.Properties.ContainsKey("fforbidstatus")))
                //            {
                //                var deptAvailable = staffDepts.Any(t => !Convert.ToBoolean(t["fforbidstatus"]));
                //                InvalidAuthorization(userCtx, deptAvailable, "部门");
                //            }

                //            if (staffDepts.Any(t => t.DynamicObjectType.Properties.ContainsKey("fstore") && t.DynamicObjectType.Properties.ContainsKey("fstore_ref")))
                //            {
                //                //获取部门对应的门店
                //                var stores = staffDepts.Where(t => Convert.ToString(t["fstore"]).Trim().Length != 0).Select(t => t["fstore_ref"] as DynamicObject).Where(f => f != null).ToList();
                //                if (stores != null && stores.Count > 0)
                //                {
                //                    if (stores.Any(t => t.DynamicObjectType.Properties.ContainsKey("fforbidstatus")))
                //                    {
                //                        var storeAvailable = stores.Any(t => !Convert.ToBoolean(t["fforbidstatus"]));
                //                        InvalidAuthorization(userCtx, storeAvailable, "部门对应门店");
                //                    }
                //                }
                //            }
                //        }
                //    }

                //    ////获取员工对应的角色
                //    //var queryRoleSql = $@"SELECT COUNT(1) AS roleusercount FROM t_sec_roleuser ru INNER JOIN t_sec_role r ON ru.froleid = r.fid
                //    //where r.fmainorgid = '{userCtx.Company}' and ru.fuserid = '{userId}' AND  r.fforbidstatus = 0";
                //    //var dbService = userCtx.Container.GetService<IDBService>();
                //    //var roleUserCount = dbService.ExecuteDynamicObject(userCtx, queryRoleSql, null).FirstOrDefault();
                //    //var roleAvailable = true;//Convert.ToInt32(roleUserCount["roleusercount"]) > 0;               
                //}
            }
        }

        /// <summary>
        /// 判断当前经销商是否直营
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static bool IsDirectSale(this UserContext userCtx)
        {
            var currentCompany = userCtx.Company;
            bool isTrue = false;
            if (currentCompany != null)
            {
                var agentDy = userCtx.LoadBizBillHeadDataById("bas_agent",currentCompany,"fnumber,fmanagemodel");

                // 添加空值检查，防止NullReferenceException
                if (agentDy != null)
                {
                    //'0':'经销商','1':'直营'
                    var manageModel = Convert.ToString(agentDy["fmanagemodel"]);

                    //如果是空的，那么返回false
                    if (manageModel.IsNullOrEmptyOrWhiteSpace())
                    {
                        isTrue = false;
                    }
                    else if (manageModel.EqualsIgnoreCase("1"))
                    {
                        isTrue = true;
                    }
                    else
                    {
                        isTrue = false;
                    }
                }
                else
                {
                    // 如果经销商信息不存在，默认返回false（非直营）
                    isTrue = false;
                }
            }

            return isTrue;
        }

        private static bool IsForbidStatus(string forbidstatus)
        {
            return forbidstatus.EqualsIgnoreCase("1") || forbidstatus.EqualsIgnoreCase("true");
        }

        private static void InvalidAuthorization(UserContext userCtx, string docStr)
        {
            throw new BusinessException($"当前用户[{userCtx.UserName}]关联的[{docStr}档案]在[{userCtx.Companys.FirstOrDefault(s => s.CompanyId.EqualsIgnoreCase(userCtx.Company))?.CompanyName}]中已被禁用，请联系系统管理员进行处理！");
        }
    }
}
