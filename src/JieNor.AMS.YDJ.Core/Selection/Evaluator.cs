using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Scripting.Hosting;
using IronPython.Hosting;

namespace JieNor.AMS.YDJ.Core
{
    public static class Evaluator
    {
        /// <summary>
        /// Python 脚本引擎
        /// </summary>
        static ScriptEngine pyEngine = Python.CreateEngine();

        /// <summary>
        /// 运行 Python 表达式
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns>表达式的执行结果</returns>
        public static decimal ExcutePython(string expression)
        {
            return ExcutePython<decimal>(expression);
        }

        /// <summary>
        /// 运行 Python 表达式
        /// </summary>
        /// <typeparam name="T">返回值的类型</typeparam>
        /// <param name="expression">表达式</param>
        /// <returns>表达式的执行结果</returns>
        public static T ExcutePython<T>(string expression)
        {          
            var value = pyEngine.Execute(expression);
            return (T)Convert.ChangeType(value, typeof(T));
        }

        #region 运行 JScript 表达式

        static Microsoft.JScript.Vsa.VsaEngine vsaEngine = Microsoft.JScript.Vsa.VsaEngine.CreateEngine();

        /// <summary>
        /// 运行 JScript 表达式
        /// </summary>
        /// <param name="expression">表达式</param>
        /// <returns>表达式的执行结果</returns>
        public static decimal EvaluateByItem(string expression)
        {
            object eval = Microsoft.JScript.Eval.JScriptEvaluate(expression, vsaEngine);
            if (double.IsNaN(Convert.ToDouble(eval)))
            {
                return 0;
            }
            return Convert.ToDecimal(eval);
        }

        #endregion 

        #region 运行 .NET 函数

        static Dictionary<string, Type> _dictType = new Dictionary<string, Type>();

        static Object lockObj = new Object();

        /// <summary>
        /// 运行 .NET 函数
        /// </summary>
        /// <param name="spaceName">命名空间</param>
        /// <param name="classFullName">类的全名，包括命名空间</param>
        /// <param name="methodName">函数名</param>
        /// <param name="paras">函数的输入参数</param>
        /// <param name="message">函数的输出参数，通常是一个消息字符串</param>
        /// <returns>函数的执行结果</returns>
        public static decimal EvaluateByFunc(string spaceName, string classFullName, string methodName, object[] paras, out string message)
        {
            return EvaluateByFunc<dynamic>(spaceName, classFullName, methodName, paras, out message);
        }

        /// <summary>
        /// 运行 .NET 函数
        /// </summary>
        /// <typeparam name="T">返回值的类型</typeparam>
        /// <param name="spaceName">命名空间</param>
        /// <param name="classFullName">类的全名，包括命名空间</param>
        /// <param name="methodName">函数名</param>
        /// <param name="paras">函数的输入参数</param>
        /// <param name="message">函数的输出参数，通常是一个消息字符串</param>
        /// <returns>函数的执行结果</returns>
        public static T EvaluateByFunc<T>(string spaceName, string classFullName, string methodName, object[] paras, out string message)
        {
            var eval = default(T);

            lock (lockObj)
            {
                //通过反射加载指定类的类型
                Type type = null;
                if (!_dictType.TryGetValue(classFullName, out type))
                {
                    type = Assembly.Load(spaceName).CreateInstance(classFullName).GetType();
                    _dictType.Add(classFullName, type);
                }
            }

            //获取类函数
            MethodInfo method = _dictType[classFullName].GetMethod(methodName);

            //实例化类
            //object obj = Activator.CreateInstance(_dictType[classFullName]);

            //调用非静态函数
            //object eval = method.Invoke(obj, methodParam);

            //调用静态函数
            var objValue = method.Invoke(null, paras);

            //转换为 T 类型
            if (objValue != null)
            {
                eval = (T)Convert.ChangeType(objValue, typeof(T));
            }

            //获取函数的输出参数
            message = Convert.ToString(paras[paras.Length - 1]);

            //返回函数的执行结果
            return eval;
        }

        #endregion
    }
}
