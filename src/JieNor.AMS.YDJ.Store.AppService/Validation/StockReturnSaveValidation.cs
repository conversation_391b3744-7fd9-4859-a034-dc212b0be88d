using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.AMS.YDJ.Core.DataEntity;
using System.Data;

namespace JieNor.AMS.YDJ.Store.AppService.Validation
{
    /// <summary>
    /// 优先，采购退货和销售退货选单保存校验
    /// 禅道：http://dmp.jienor.com:81/zentao/task-view-44637.html
    /// </summary>

    [InjectService("stockreturnsave")]
    [ServiceMetaAttribute("name", "库存选单保存服务")]
    [ServiceMetaAttribute("serviceid", YDJHtmlElementType.HtmlBizService_StockReturnSave)]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class StockReturnSaveService : AbstractBaseService
    {
        /// <summary>
        /// 库存更新配置
        /// </summary>
        public StockSaveSetting WritebackRule { get; private set; }

        /// <summary>
        /// 来源表单模型
        /// </summary>
        public HtmlForm SourceHtmlForm { get; private set; }

        /// <summary>
        /// 初始化服务参数
        /// </summary>
        /// <param name="servicePara"></param>
        protected override void OnServiceInitialized(string servicePara)
        {
            base.OnServiceInitialized(servicePara);
            var serParaObj = Newtonsoft.Json.Linq.JObject.Parse(servicePara);
            string jsonSerConfig = Convert.ToString(serParaObj["serConfig"]);

            this.WritebackRule = jsonSerConfig.FromJson<StockSaveSetting>(true);

            if (this.WritebackRule == null)
            {
                throw new BusinessException("反写服务参数必须配置！");
            }

            if (this.WritebackRule.WritebackFieldKey.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"反写服务参数{nameof(this.WritebackRule.WritebackFieldKey)}必须配置！");
            }

            if (this.WritebackRule.SourceFormId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"反写服务参数{nameof(this.WritebackRule.SourceFormId)}必须配置！");
            }

            this.SourceHtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.WritebackRule.SourceFormId);
            if (this.SourceHtmlForm == null)
            {
                throw new BusinessException($"待反写的表单标识配置不正确：{this.WritebackRule.SourceFormId}，模型不存在！");
            }

            var writebackField = this.SourceHtmlForm.GetField(this.WritebackRule.WritebackFieldKey);
            if (writebackField == null)
            {
                throw new BusinessException($"待反写的源单字段标识配置不正确：{this.WritebackRule.WritebackFieldKey}，字段不存在！");
            }
        }
        public override void ExecuteService(ref DynamicObject[] dataEntities)
        {
            List<DynamicObject> lstBillObjs = new List<DynamicObject>();
            lstBillObjs.AddRange(dataEntities);
            var dataObjs = lstBillObjs.ToArray();
            //将待处理的数据整理入临时表
            var tmpTableName = this.CreateTempSourceTable(dataObjs);
            if (tmpTableName.IsNullOrEmptyOrWhiteSpace()) return;
            //执行字段反写逻辑
            //处理反写后超额逻辑判断
            this.CheckExcessCondition(tmpTableName);
            //throw new NotImplementedException();
        }

        /// <summary>
        /// 根据反写规则及当前数据将源单的字段信息写入临时表
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private string CreateTempSourceTable(DynamicObject[] dataEntities)
        {
            var sourceControlFieldKey = this.HtmlForm.GetField(this.WritebackRule.SourceControlFieldKey);
            var currentLinkIdField = this.HtmlForm.GetField(this.WritebackRule.LinkIdFieldKey);
            var currentLinkFormField = this.HtmlForm.GetField(this.WritebackRule.LinkFormFieldKey);
            var linkFilterString = this.WritebackRule.LinkFilterString;

            if (currentLinkIdField == null)
            {
                throw new BusinessException($"反写规则配置参数错误：{this.WritebackRule.LinkIdFieldKey}字段标识不存在！");
            }
            if (!string.IsNullOrEmpty(linkFilterString.Trim()))
            {
                linkFilterString = " And " + linkFilterString;
            }

            var sourceLinkField = this.SourceHtmlForm.GetField(this.WritebackRule.SourceLinkFieldKey);

            DataTable dtTmp = new DataTable();
            dtTmp.Columns.AddRange(new DataColumn[]
            {
                new DataColumn("fsourceformid",typeof(string)),
                new DataColumn("fsourcelinkid",typeof(string)),
                new DataColumn("fqty",typeof(decimal)),//当前单据数量,
                new DataColumn("finstockqty",typeof(decimal))//历史已保存的入库单数量
            });

            ExtendedDataEntitySet ds = new ExtendedDataEntitySet();
            ds.Parse(this.Context, dataEntities, this.HtmlForm);
            var allLinkDataEntities = ds.FindByEntityKey(currentLinkIdField.EntityKey);

            Dictionary<string, Dictionary<string, object>> dctRowObjs = new Dictionary<string, Dictionary<string, object>>(StringComparer.OrdinalIgnoreCase);
            foreach (var dataEntity in allLinkDataEntities)
            {
                var linkId = currentLinkIdField.DynamicProperty.GetValue<string>(dataEntity.DataEntity);
                var sqty = sourceControlFieldKey.DynamicProperty.GetValue<decimal>(dataEntity.DataEntity);
                if (linkId.IsNullOrEmptyOrWhiteSpace()) continue;
                Dictionary<string, object> dctRowObj = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
                if (!dctRowObjs.TryGetValue(linkId, out dctRowObj))
                {
                    dctRowObj = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
                    dctRowObjs[linkId] = dctRowObj;
                }

                dctRowObj["fsourcelinkid"] = linkId;

                string sql = $"SELECT ISNULL(SUM({this.WritebackRule.SourceControlFieldKey}),0) fqty FROM {this.HtmlForm.BillHeadTableName} as a with(nolock) inner join   {this.HtmlForm.BillHeadTableName}entry  as b with(nolock) on a.fid=b.fid WHERE {this.WritebackRule.LinkIdFieldKey}='{linkId}' {linkFilterString}  AND b.fentryid<>'{dataEntity.DataEntity["Id"]}' ";
                int historyQty = 0;
                using (var reader = this.DBService.ExecuteReader(this.Context, sql.ToString()))
                {
                    while (reader.Read())
                    {
                        historyQty = Convert.ToInt32(reader[0]);
                    }
                }
                dctRowObj["fqty"] = sqty;//当前数据包数量
                dctRowObj["finstockqty"] = historyQty;//历史数量
                object sourceFormId = this.WritebackRule.SourceFormId;
                if (currentLinkFormField != null)
                {
                    BizDynamicDataRow bizRow = new BizDynamicDataRow(null);
                    bizRow.ActiveDataObject = dataEntity.DataEntity;
                    if (!bizRow.TryGetMember(currentLinkFormField.Id, out sourceFormId))
                    {
                        throw new BusinessException("不支持关联字段与关联表单类型字段分布在不同的明细实体里！");
                    }
                }
                dctRowObj["fsourceformid"] = sourceFormId;

                //当前数据行涉及的源单标识跟反写单标识一致，认为此数据有效
                if (this.WritebackRule.SourceFormId.EqualsIgnoreCase(sourceFormId as string))
                {
                    dctRowObjs[linkId] = dctRowObj;
                }
            }


            foreach (var kvpItem in dctRowObjs)
            {
                var dr = dtTmp.NewRow();
                dtTmp.Rows.Add(dr);
                foreach (var subItem in kvpItem.Value)
                {
                    dr[subItem.Key] = subItem.Value;
                }
            }
            if (dtTmp.Rows.Count == 0) return null;

            return this.DBService.CreateTempTableWithDataTable(this.Context, dtTmp, 500);
        }

        private void CheckExcessCondition(string tmpTableName)
        {

            //如果未配置超额条件，则认为不做超额检查
            if (this.WritebackRule.ExcessCondition.IsNullOrEmptyOrWhiteSpace()) return;
            SqlBuilderParameter sourceQueryPara = new SqlBuilderParameter(this.Context, this.SourceHtmlForm);
            sourceQueryPara.UseInneJoin = true;
            sourceQueryPara.PageIndex = -1;
            sourceQueryPara.PageCount = -1;
            if (!this.WritebackRule.ExcessCondition.IsNullOrEmptyOrWhiteSpace())
            {
                //当前单据+历史单据+采购订单
                sourceQueryPara.FilterString = " ( tmp.fqty+tmp.finstockqty+ {0} ) ".Fmt(this.WritebackRule.ExcessCondition);
            }
            if (!this.WritebackRule.SourceLinkFieldKey.IsNullOrEmptyOrWhiteSpace())
            {
                sourceQueryPara.SelectedFieldKeys.Add(this.WritebackRule.SourceLinkFieldKey);
            }
            sourceQueryPara.SelectedFieldKeys.Add(this.WritebackRule.WritebackFieldKey);
            sourceQueryPara.SelectedFieldKeys.Add(this.SourceHtmlForm.NumberFldKey);
            sourceQueryPara.SelectedFieldKeys.Add(this.WritebackRule.SourceControlFieldKey);

            var sourceQueryObj = QueryService.BuilQueryObject(sourceQueryPara);


            var sourceQueryMetaInfo = QueryService.GetHtmlFormQueryMetaInfo(this.Context, this.SourceHtmlForm);

            var sourceNumberField = this.SourceHtmlForm.GetNumberField();
            var writebackField = this.SourceHtmlForm.GetField(this.WritebackRule.WritebackFieldKey);
            var controlField = this.SourceHtmlForm.GetField(this.WritebackRule.SourceControlFieldKey);

            var sourceLinkFld = this.SourceHtmlForm.GetField(this.WritebackRule.SourceLinkFieldKey);
            var sourceLinkFldKey = this.WritebackRule.SourceLinkFieldKey;
            if (sourceLinkFld == null)
            {
                sourceLinkFldKey = $"{writebackField.EntityKey}.{writebackField.Entity.PkFieldName}";
            }
            var sourceLinkSelectFld = sourceQueryMetaInfo.GetSelectField(sourceLinkFldKey);



            StringBuilder sbSql = new StringBuilder();
            sbSql.Append($@"
                            {sourceQueryObj.SqlSelect},tmp.fqty  +tmp.finstockqty as 'fbillqty'
                            {sourceQueryObj.SqlFrom}
                            inner join {tmpTableName} tmp on tmp.fsourcelinkid={sourceLinkSelectFld.SelectFldName}
                            {sourceQueryObj.SqlWhere}
                        ");

            using (var reader = this.DBService.ExecuteReader(this.Context, sbSql.ToString(), sourceQueryPara.DynamicParams))
            {
                while (reader.Read())
                {
                    var billNo = "";
                    if (sourceNumberField != null)
                    {
                        billNo = Convert.ToString(reader[sourceNumberField.FieldName]);
                    }
                    double dSrcQty = 0, dOccureQty = 0;
                    string srcFieldName = "", dstFieldName = "";
                    if (controlField != null)
                    {
                        dSrcQty = Convert.ToDouble(reader[controlField.FieldName]); ;
                        srcFieldName = controlField.Caption;
                    }
                    if (writebackField != null)
                    {
                        dOccureQty = Convert.ToDouble(reader[writebackField.FieldName]);
                        dOccureQty = Convert.ToDouble(reader["fbillqty"]);
                        dstFieldName = writebackField.Caption;
                    }
                    throw new BusinessException($"{this.WritebackRule.ExcessMessage ?? "操作失败，不允许出现超额作业"}<br>超额信息(已发生数量[{dOccureQty}]大于可用总量[{dSrcQty}])，关联单据：{this.SourceHtmlForm.Caption}/{billNo}");
                }
            }
        }
    }
}