using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PoStockIn
{
    /// <summary>
    /// 采购入库单：反作废
    /// </summary>
    [InjectService]
    [FormId("stk_postockin")]
    [OperationNo("UnCancel")]
    public class UnCancel : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            //更新销售单商品明细中总部已发货数
            var datas = e.DataEntitys;
            //UpdateOrderEntryBizHQDeliveryQty(datas);

        }
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            // 反写采购订单和销售合同【总部已发货数】
            Core.Helpers.OrderQtyWriteBackHelper.WriteBackHqDeliveryQty(
                this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);
            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys);
        }
        private void UpdateOrderEntryBizHQDeliveryQty(DynamicObject[] datas)
        {

            #region 删除时计算，更新采购单商品明细总部已发货数
            Dictionary<string, string> newEntryDic = new Dictionary<string, string>();
            var soOrderentryDic = new Dictionary<string, decimal>();
            foreach (var dataEntity in datas)
            {
                var entryDatas = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entry in entryDatas)
                {
                    // 实收数量
                    var qty = Convert.ToDecimal(entry["fbizqty"]);
                    newEntryDic.Add(Convert.ToString(entry["id"]), qty.ToString() + "|" + Convert.ToString(entry["fpoorderentryid"]));
                    soOrderentryDic.Add(Convert.ToString(entry["fsoorderentryid"]), qty);
                }
            }

            var oldEntryList = new List<DynamicObject>();
            oldEntryList = this.DBService.ExecuteDynamicObject(this.Context, "select fentryid,fpoorderentryid,fsoorderentryid,fbizqty from t_stk_postockinentry where fentryid in('" + string.Join("','", newEntryDic.Select(x => x.Key).ToList()) + "')").ToList();

            var sql = "";
            if (oldEntryList.Any())
            {
                var orderEntryList = this.DBService.ExecuteDynamicObject(this.Context, "select fentryid,fbizhqdeliveryqty from t_ydj_orderentry where fentryid in('" + string.Join("','", oldEntryList.Select(x => x["fsoorderentryid"]).ToList()) + "')").ToList();//查询销售单商品明细
                orderEntryList?.ForEach(o =>
                {
                    var newQty = soOrderentryDic[Convert.ToString(o["fentryid"])];//需要修改的总部已发货数
                    var oldQty = Convert.ToDecimal(o["fbizhqdeliveryqty"]);//修改前的入库实收数量
                    var writeTransPurQty = oldQty - newQty;  //需要修改的总部已发货数
                    sql += $" update t_ydj_orderentry set fbizhqdeliveryqty={writeTransPurQty} where fentryid='{Convert.ToString(o["fentryid"])}'";
                });
            }
            if (!sql.IsNullOrEmptyOrWhiteSpace())
            {
                using (var reader = this.Context.Container.GetService<IDBService>().ExecuteReader(this.Context, sql))
                {

                }
            }
            #endregion
        }
    }
}