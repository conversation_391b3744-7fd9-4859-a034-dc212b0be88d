using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.CustomEventData;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PoStockIn
{
    /// <summary>
    /// 采购入库单：选单
    /// </summary>
    [InjectService]
    [FormId("stk_postockin")]
    [OperationNo("Pull")]
    public class Pull : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.BeforeGetConvertRule:
                    this.BeforeGetConvertRule(e);
                    break;
                default:
                    break;
            }
        }

        /*
        2、采购入库单选单时，支持多对一
        a、其合并入库规则是：同一供应商 且 同一采购部门 且 同一采购员 ；
        b、当选择的多笔采购订单不满足合并入库规则时，点<确定>，提示报错：同一供应商 且 同一采购部门 且 同一采购员 的采购订单才允许合并入库，请重新选择！
        任务链接：http://dmp.jienor.com:81/zentao/task-view-29341.html
        */
        private void BeforeGetConvertRule(OnCustomServiceEventArgs e)
        {
            var dataEntities = e.DataEntities;

            if (dataEntities == null || dataEntities.Length == 0) return;

            var eventData = e.EventData as BeforeGetConvertRuleData;
            if (eventData == null) return;

            var rule = this.MetaModelService.LoadConvertRule(this.Context, eventData.RuleId);
            if (rule == null) return;

            // 源单不是采购订单，跳过
            if (!rule.SourceFormId.EqualsIgnoreCase("ydj_purchaseorder")) return;

            // 如果采购入库单的存在源单，但源单不是采购订单，报错
            if (dataEntities.Any(s =>
            {
                string sourceType = Convert.ToString(s["fsourcetype"]);
                return !sourceType.IsNullOrEmptyOrWhiteSpace() && !sourceType.EqualsIgnoreCase("ydj_purchaseorder");
            }))
            {
                throw new BusinessException($"{this.HtmlForm.Caption}存在源单类型不是采购订单的，请重新选择！");
            }

            var pkids = eventData.SelectedRows?.Where(s => !s.PkValue.IsNullOrEmptyOrWhiteSpace()).Select(s => s.PkValue).Distinct();
            if (pkids == null || pkids.Count() == 0) return;

            var sourceDataEntities = this.Context.LoadBizDataById(rule.SourceFormId, pkids);
            if (sourceDataEntities == null || sourceDataEntities.Count == 0) return;

            var hasDifferent = sourceDataEntities.Any(x => Convert.ToBoolean(x["frenewalflag"])) && sourceDataEntities.Any(x => !Convert.ToBoolean(x["frenewalflag"]));
            if (hasDifferent)
            {
                throw new BusinessException("仅允许全部选焕新单据或者全部选非焕新单据，请重新选择！");
            }

            //筛选出采购订单商品明细中的套件头
            // 所有的商品ID
            var allEntrys = sourceDataEntities?.SelectMany(t => t["fentity"] as DynamicObjectCollection);
            var materialIds = allEntrys.Select(entry => Convert.ToString(entry["fmaterialid"])).Where(materialid => !materialid.IsNullOrEmptyOrWhiteSpace())?.Distinct()?.ToList();
            var suiteHeadEntryIds = new List<string>();
            if (materialIds != null && materialIds.Any())
            {
                var productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", materialIds, "fsuiteflag");
                var suiteHeadIds = productObjs.Where(t => Convert.ToString(t["fsuiteflag"]).ToLower() == "true" || Convert.ToString(t["fsuiteflag"]) == "1")
                                              .Select(m => Convert.ToString(m["id"])).ToList();//套件头商品ID
                suiteHeadEntryIds = allEntrys.Where(t => suiteHeadIds.Contains(Convert.ToString(t["fmaterialid"])))
                                             .Select(f => Convert.ToString(f["id"])).ToList();//查询套件头商品所在行的明细行内码
                //将选中行中为套件头的数据清除
                var unHeadSelect = eventData.SelectedRows.Where(t => string.IsNullOrWhiteSpace(t.EntryPkValue) || (!string.IsNullOrWhiteSpace(t.EntryPkValue) && !suiteHeadEntryIds.Contains(t.EntryPkValue)));
                eventData.SelectedRows = unHeadSelect;
            }
            if (!eventData.SelectedRows.Any())
            {
                throw new BusinessException("采购入库单选单无需选择套件头商品，请重新选择！");
            }

            foreach (var dataEntity in dataEntities)
            {
                string poStaffId = Convert.ToString(dataEntity["fpostaffid"]);
                string poDeptId = Convert.ToString(dataEntity["fpodeptid"]);
                string supplierId = Convert.ToString(dataEntity["fsupplierid"]);
                var renewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);

                /*
                 * 情况1：采购入库单有【供应商】、【采购员】、【采购部门】且【入库明细】有数据，按单据头的【供应商】、【采购员】、【采购部门】判断
                 * 情况2：采购入库单有【供应商】、【采购员】、【采购部门】但【入库明细】没数据，按【选单】的【源单】判断
                 * 情况3：采购入库单有【供应商】、但【采购员】、【采购部门】和【入库明细】没数据，按【选单】的【源单】判断
                 * 总结：只有情况1按单据头判断，其他情况按选单判断
                 */

                var entities = dataEntity["fentity"] as DynamicObjectCollection;

                var isAllNotEmpty = !supplierId.IsNullOrEmptyOrWhiteSpace()
                                    && (entities != null && entities.Any());
                if (!isAllNotEmpty)
                {
                    var sourceDataEntity = sourceDataEntities.FirstOrDefault();

                    poStaffId = Convert.ToString(sourceDataEntity?["fpostaffid"]);
                    poDeptId = Convert.ToString(sourceDataEntity?["fpodeptid"]);
                    supplierId = Convert.ToString(sourceDataEntity?["fsupplierid"]);
                    renewalflag = Convert.ToBoolean(sourceDataEntity["frenewalflag"]);
                }

                foreach (var sourceDataEntity in sourceDataEntities)
                {
                    string sourcePoStaffId = Convert.ToString(sourceDataEntity["fpostaffid"]);
                    string sourcePoDeptId = Convert.ToString(sourceDataEntity["fpodeptid"]);
                    string sourceSupplierId = Convert.ToString(sourceDataEntity["fsupplierid"]);

                    var isSame = supplierId.EqualsIgnoreCase(sourceSupplierId);

                    if (!isSame) throw new BusinessException("同一供应商 的采购订单才允许合并入库，请重新选择！");

                    var sourcerenewalflag = Convert.ToBoolean(sourceDataEntity["frenewalflag"]);
                    if (renewalflag != sourcerenewalflag)
                    {
                        throw new BusinessException("仅允许全部选焕新单据或者全部选非焕新单据，请重新选择！");
                    }
                }
            }
        }
    }
}
