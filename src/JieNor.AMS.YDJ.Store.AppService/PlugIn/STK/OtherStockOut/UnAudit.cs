using JieNor.AMS.YDJ.Store.AppService.Helper;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.OtherStockOut
{
    [InjectService]
    [FormId("stk_otherstockout")]
    [OperationNo("unaudit")]
    public class UnAudit : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            DirectHelper.UnAudit(this.Context, e.DataEntitys);
        }
    }
}
