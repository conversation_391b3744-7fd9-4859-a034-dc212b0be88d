using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SoStockOut
{
    /// <summary>
    /// 销售出库：提交
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("Submit")]
    public class Submit : AbstractOperationServicePlugIn
    {

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fmaterialid" });
        }
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            var errMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var type = newData["fsourcetype"].ToString();
                if (!type.IsNullOrEmptyOrWhiteSpace() && type == "ydj_order")
                {
                    //销售合同允许出库的最低金额比例
                    var profileService = this.Context.Container.GetService<ISystemProfile>();
                    var systemParameter = profileService.GetSystemParameter(this.Context, "bas_storesysparam");
                    var fsourcenumber = newData["fsourcenumber"].ToString();
                    var dm = GetOrderDm(fsourcenumber);
                    if (dm.IsNullOrEmptyOrWhiteSpace())
                    {
                        errMsg = "选单数据不正确！";
                        return false;
                    }

                    //若启用了流程则会走流程的条件，不需要走这段业务条件
                    var fflowinstanceid = Convert.ToString(newData["fflowinstanceid"]);
                    if (fflowinstanceid.IsNullOrEmptyOrWhiteSpace())
                    {
                        //任务 37793 参数改为从合同单据类型配置里读取 ，不再使用销售管理参数配置了。
                        var fproportionoratioamount = 100;
                        //找上游合同的单据类型参数配置
                        var fsourcebillno = Convert.ToString(newData["fsourcenumber"]);
                        if (!fsourcebillno.IsNullOrEmptyOrWhiteSpace())
                        {
                            var billTypeService = this.Container.GetService<IBillTypeService>();
                            var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                            var reader = Context.GetPkIdDataReader(orderForm, $@"fbillno='{fsourcebillno}'", new List<SqlParam>() { });
                            var formDt = orderForm.GetDynamicObjectType(Context);
                            var orderdm = Context.Container.GetService<IDataManager>();
                            orderdm.InitDbContext(Context, formDt);
                            var dynObjs = orderdm.SelectBy(reader).OfType<DynamicObject>().FirstOrDefault();
                            var paramSetObj = billTypeService.GetBillTypeParamSet(this.Context, orderForm, Convert.ToString(dynObjs["fbilltype"]));
                            if (paramSetObj != null)
                            {
                                int.TryParse(Convert.ToString(paramSetObj["fproportionoratioamount"]), out fproportionoratioamount);
                            }
                        }
                        var freceivable = dm["freceivable"];
                        var fsumamount = dm["fsumamount"];
                        //#37762改为订单总额-申请退货金额
                        var newfsumamount = (Convert.ToDecimal(fsumamount) - Convert.ToDecimal(dm["frefundamount"])) * fproportionoratioamount / 100;
                        if (Convert.ToDouble(freceivable) < Convert.ToDouble(newfsumamount))
                        {
                            errMsg = "当前订单确认已收金额不足" + newfsumamount + "元，暂不允许出库！";
                            return false;
                        }
                    }

                    var products= newData["fentity"] as DynamicObjectCollection;
                    var orderproducts = dm["fentry"] as DynamicObjectCollection;
                    if (!products.IsNullOrEmpty() && products.Count > 0)
                    {
                        foreach (var item in products)
                        {
                            if (!item["fsoorderentryid"].ToString().IsNullOrEmptyOrWhiteSpace()) {
                                var objdata = orderproducts.Where(x => x["id"].ToString() == item["fsoorderentryid"].ToString()).FirstOrDefault();
                                if (objdata == null) {
                                    errMsg = $"第{item["Fseq"]}行商品{(item["fmaterialid_ref"] as DynamicObject)?["fnumber"]}与上游合同明细商品不关联，请联系管理员进行排查，谢谢！";
                                    return false;
                                }
                               
                            }
                        }
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errMsg));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fsourcebillno = Convert.ToString(newData["fsourcenumber"]);
                var sysProfile = this.Container.GetService<ISystemProfile>();
                //销售合同超额收款控制出库
                var forderoverpaymentdisout = sysProfile.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "forderoverpaymentdisout", false);

                if (!fsourcebillno.IsNullOrEmptyOrWhiteSpace() && forderoverpaymentdisout)
                {
                    return this.CheckCanOutByFunreceived(newData, out errMsg);
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errMsg));
        }

        //根据源单ID找到对应合同
        private DynamicObject GetOrderDm(string fbillno)
        {
            var purForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, purForm.GetDynamicObjectType(this.Context));

            var where = "fbillno=@fbillno";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fbillno", System.Data.DbType.String, fbillno)
            };

            var reader = this.Context.GetPkIdDataReader(purForm, where, sqlParam);
            var purOrder = dm.SelectBy(reader).OfType<DynamicObject>().FirstOrDefault();
            return purOrder;
        }
        private bool CheckCanOutByFunreceived(DynamicObject newData, out string errorMsg)
        {
            bool canout = true;
            errorMsg = "";
            var sourenumber = Convert.ToString(newData?["fsourcenumber"]);
            var orderObj = this.Context.LoadBizDataByACLFilter("ydj_order", $"fbillno = '{sourenumber}' ").FirstOrDefault();
            if (orderObj != null)
            {
                var freceived = Convert.ToDecimal(orderObj?["funreceived"]);
                if (freceived < 0)
                {
                    canout = false;
                    errorMsg = $"对不起，上游销售合同【{sourenumber}】已经超额收款，管理员已控制不允许出库，请核查，谢谢！！";
                }
            }
            return canout;
        }
    }
}
