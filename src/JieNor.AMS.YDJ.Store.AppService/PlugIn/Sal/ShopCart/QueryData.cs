using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.ShopCart
{
    /// <summary>
    /// 购物车：列表查询数据插件
    /// </summary>
    [InjectService]
    [FormId("sal_shopcart")]
    [OperationNo("querydata")]
    public class QueryData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 自定义服务端事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.PrepareQueryBuilderParameter:
                    this.PrepareQueryBuilderParameter(e);
                    break;
            }
        }

        /// <summary>
        /// 列表准备查询过滤参数事件
        /// </summary>
        /// <param name="e"></param>
        public void PrepareQueryBuilderParameter(OnCustomServiceEventArgs e)
        {
            var param = e.EventData as SqlBuilderParameter;
            if (param == null) return;

            //提供额外的过滤条件：当前登录用户只能查看自己的购物车数据
            param.FilterString = param.FilterString.JoinFilterString("fcreatorid=@fcreatorid");
            param.AddParameter(new SqlParam("@fcreatorid", System.Data.DbType.String, this.Context.UserId));
        }
    }
}