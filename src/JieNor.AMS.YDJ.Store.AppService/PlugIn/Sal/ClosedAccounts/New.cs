using JieNor.AMS.YDJ.Core.Interface.Finance;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.ClosedAccounts
{
    /// <summary>
    /// 财务关账/反关账：加载
    /// 作者：zpf
    /// 日期：2022-07-28
    /// </summary>
    [InjectService]
    [FormId("ydj_closedaccounts")]
    [OperationNo("new")]
    public class New : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 设置最近一次的关账日期
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            //库存结束初始化后，不允许再新增初始库存单据
            var baseStockService = this.Container.GetService<IFinanceBaseService>();
            var lastclosuredate = baseStockService.GetLatestFinanceCloseDate(this.Context);
            if (lastclosuredate.HasValue && e.DataEntitys != null && e.DataEntitys.Any())
            {
                e.DataEntitys[0]["flastclosuredate"] = lastclosuredate;
            }
        }
    }
}
