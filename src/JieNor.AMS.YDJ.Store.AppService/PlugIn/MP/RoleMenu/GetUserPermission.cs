using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MP.RoleMenu
{
    /// <summary>
    /// 小程序权限：获取菜单权限列表
    /// </summary>
    [InjectService]
    [FormId("mp_rolemenu")]
    [OperationNo("GetUserPermission")]
    public class GetUserPermission : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 操作结束处理逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var auth = MPMenuHelper.GetMPTabbars(this.Context);
            var menuPerms = MPPermHelper.GetMPMenuPermDataByUser(this.Context, this.Context.UserId);
            if(auth==null)
            {
                this.Result.SimpleMessage = "未给对应用户授权";
                this.Result.IsSuccess = true;
                return;
            }
            foreach (var tabbar in auth)
            {
                foreach (var group in tabbar.groups)
                {
                    foreach (var menu in group.menus)
                    {
                        string key = $"{tabbar.tabbar}:{group.group}:{menu.id}";
                        if (menuPerms.ContainsKey(key))
                        {
                            menu.isAllow = menuPerms[key];
                        }
                    }
                }
            }

            this.Result.SrvData = auth.ToJson();
            this.Result.IsSuccess = true;
        }
    }
}