using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Core.Reserve;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 保存《采购订单》时要增加新渠道商品类型校验
    /// </summary>
    public class Validation_ChannelType : AbstractBaseValidation
    {
        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        private HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            this.HtmlForm = formInfo;

            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }

            CheckChannelType(userCtx, dataEntities, result);

            return result;
        }


        private void CheckChannelType(UserContext ctx, DynamicObject[] dataEntitys, ValidationResult result)
        {
            var loadReferenceObjectManager = ctx.Container.GetService<LoadReferenceObjectManager>();
            loadReferenceObjectManager.Load(this.Context, dataEntitys, true, this.HtmlForm, new List<string> { "fmaterialid" });

            //#75244 【250491】 【慕思现场4.10-4.14】新渠道系列拆分Z2 慕思经典-甄选 ，Z5 慕思经典-优选
            var seriesIds =
                this.DBService.ExecuteDynamicObject(this.Context,
                    "select fid as id from t_ydj_series with(nolock) where  fisnewchannel='1' ").Select(s => Convert.ToString(s["id"]));

            foreach (DynamicObject newData in dataEntitys)
            {
                DynamicObject fbill = newData["fbilltypeid_ref"] as DynamicObject;
                if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
                {
                    continue;
                }
                var entry = newData["fentity"] as DynamicObjectCollection;
                foreach (DynamicObject item in entry)
                {
                    if (this.Context.IsSecondOrg && seriesIds.Contains(Convert.ToString(item["fresultbrandid"])))
                    {
                        List<string> msg = new List<string>();
                        if (string.IsNullOrWhiteSpace(Convert.ToString(item?["fchannel"])) && (fbill["fnumber"].ToString().EqualsIgnoreCase("BHDD_SYS_01") || fbill["fname"].ToString().EqualsIgnoreCase("备货订单"))) {
                            msg.Add("合作渠道");
                        }
                        if (string.IsNullOrWhiteSpace(Convert.ToString(item?["fgoodschanneltype"]))) {
                            msg.Add("渠道类型");
                        }
                        if (msg.Count > 0) {
                            var mat = item["fmaterialid_ref"] as DynamicObject;
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $@"第{item["fseq"]}行新渠道商品{mat?["fname"].ToString()}, 对应{string.Join(",", msg)}不允许为空 ! ! ！",
                                DataEntity = item,
                            });
                        }
                    }
                    if (!this.Context.IsSecondOrg&&seriesIds.Contains(Convert.ToString(item["fresultbrandid"])) && string.IsNullOrWhiteSpace(Convert.ToString(item?["fgoodschanneltype"])))
                    {
                        var mat = item["fmaterialid_ref"] as DynamicObject;
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $@"第{item["fseq"]}行新渠道商品{mat?["fname"]}, 对应渠道类型不允许为空 ! ! ！",
                            DataEntity = item,
                        }); 
                    }
                }
            }
        }



    }


}
