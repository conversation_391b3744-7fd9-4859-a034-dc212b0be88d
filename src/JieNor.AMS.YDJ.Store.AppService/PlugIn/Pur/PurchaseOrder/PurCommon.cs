using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur
{
    public class PurCommon
    {
        private UserContext Context;
        public PurCommon(UserContext context)
        {
            this.Context = context;
        }
        /// <summary>
        /// 【采购数量】必须为【采购件数】的倍数 验证
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public bool CheckPurMul(DynamicObject dataEntity, out string message)
        {
            message = "";
            DynamicObjectCollection entrys = dataEntity["fentity"] as DynamicObjectCollection;

            if (entrys != null)
            {
                //加载全部商品ID
                var prodids = entrys.Select(x => x["fmaterialid"].ToString()).ToList();

                var products = Context.LoadBizDataById("ydj_product", prodids);
                //将商品和件数加入列表
                Dictionary<string, string> prodic = new Dictionary<string, string>();
                foreach (var item in products)
                {
                    if (Convert.ToInt32(item["fpackqty"]) > 1)
                    {
                        prodic[item["Id"].ToString()] = item["fpackqty"].ToString() + "," + (item["fname"].ToString());
                    }
                }
                //判断【采购数量】必须为【采购件数】的倍数
                foreach (var item in entrys)
                {
                    if (prodic.ContainsKey(item["fmaterialid"].ToString()))
                    {
                        var qty = prodic[item["fmaterialid"].ToString()].Split(',')[0];
                        var fname = prodic[item["fmaterialid"].ToString()].Split(',')[1];
                        //倍数
                        var mul = Convert.ToDecimal(qty);
                        var fseq = Convert.ToInt32(item["fseq"]);
                        //判断【采购数量】是否是【采购件数】的倍数
                        if (mul > 0 && Convert.ToDecimal(item["fbizqty"]) % mul != 0)
                        {
                            message += "当前明细行【" + fseq + "】商品【" + fname + "】设置了采购件数为【" + mul + "】, 当前采购数量必须为采购件数的倍数, 请修改后操作 ! \r\n";
                        }
                    }
                }
                if (message != "")
                    return false;

            }
            return true;
        }
    }
}
