using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseOrder
{
    /// <summary>
    /// 采购订单：确认
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("PriceFirm")]
    public class PriceFirm : UpdateBizStatus
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        public override string OperationName { get { return "确认"; } }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
        }
    }
}