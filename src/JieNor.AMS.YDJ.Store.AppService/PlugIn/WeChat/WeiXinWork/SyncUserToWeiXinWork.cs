using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System.Data;
using Senparc.Weixin.Work;
using Senparc.Weixin.Work.Containers;
using Senparc.Weixin;
using Senparc.Weixin.Work.AdvancedAPIs;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.WeChat.WinXinWork
{
    /// <summary>
    /// 用户同步至企业微信
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    [OperationNo("synctowxwork")]
    public class SyncUserToWeiXinWork : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if(e.DataEntitys.IsNullOrEmptyOrWhiteSpace() || e.DataEntitys.Count() == 0)
            {
                return;
            }
            string corpId = this.GetAppConfig("ms.wxwork.corpid.ydj");
            var txlSecret = this.GetAppConfig("ms.wxwork.txlsecret.ydj");
            if (corpId.IsNullOrEmptyOrWhiteSpace() || txlSecret.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;
            //获取微信配置参数
            var profileService = this.Container.GetService<ISystemProfile>();
            var syncType = profileService.GetSystemParameter(this.Context, "bas_weixinparam", "fsynctype", "1");
            foreach (var data in e.DataEntitys)
            {
                //防止过期，每同步一个都重新生成一个token
                var accessToken = AccessTokenContainer.GetToken(corpId, txlSecret);
                string userId = "";
                string strSyncType = "微信号匹配";
                if (syncType.EqualsIgnoreCase("1"))
                {
                    //微信号匹配
                    userId = data["fwechatid"]?.ToString();
                }
                else if (syncType.EqualsIgnoreCase("2"))
                {
                    //手机号匹配
                    userId = data["fphone"]?.ToString();
                    strSyncType = "手机号匹配";
                }
                if (userId.IsNullOrEmptyOrWhiteSpace())
                {
                    this.Result.ComplexMessage.ErrorMessages.Add("当前微信用户匹配方式为按【{0}】用户【{1}】对应字段内容为空，不允许同步！".Fmt(strSyncType, data["fnumber"]));
                    continue;
                }
                //判断企业微信是否存在当前用户userid，存在则更新
                try
                {
                    var res = MailListApi.GetMember(accessToken, userId);
                    if (!res.IsNullOrEmptyOrWhiteSpace() && res.errcode == ReturnCode_Work.请求成功)
                    {
                        //存在，则更新
                        createOrUpdateMemmber(data, accessToken, userId, true);
                        continue;
                    }
                }catch(Exception ex)
                {
                    
                }
                //创建用户
                createOrUpdateMemmber(data, accessToken, userId);
            }
        }
        /// <summary>
        /// 创建或者更新用户
        /// </summary>
        /// <param name="data"></param>
        /// <param name="accessToken"></param>
        /// <param name="isUpdate">是否更新</param>
        private void createOrUpdateMemmber(DynamicObject data, string accessToken, string userId, bool isUpdate = false)
        {
            var gender = data["fgender"]?.ToString();
            if (gender.EqualsIgnoreCase("sex2"))
            {
                //性别女
                gender = "2";
            }
            else
            {
                gender = "1";
            }
            long[] departMent = new long[] { 1 };  //部门默认是1
            try
            {
                if (isUpdate)
                {
                    var res = MailListApi.UpdateMember(accessToken, userId, convertTrimString(data, "fname"), convertTrimString(data, "fphone"), null, null, null, gender, null, convertTrimString(data, "femail"));
                    if (!res.IsNullOrEmptyOrWhiteSpace() && res.errcode == ReturnCode_Work.请求成功)
                    {
                        //var userIds = new string[] { userId };
                        //res = MailListApi.InviteMemberBatch(accessToken, userIds);
                        this.Result.ComplexMessage.SuccessMessages.Add("用户【{0}】更新成功！".Fmt(data["fnumber"]));
                    }
                }
                else
                {
                    var res = MailListApi.CreateMember(accessToken, userId, convertTrimString(data, "fname"), convertTrimString(data, "fphone"), null, departMent, null, gender, null, convertTrimString(data, "femail"));
                    if (!res.IsNullOrEmptyOrWhiteSpace() && res.errcode == ReturnCode_Work.请求成功)
                    {
                        //var userIds = new string[] { userId };
                        //res = MailListApi.InviteMemberBatch(accessToken, userIds);
                        this.Result.ComplexMessage.SuccessMessages.Add("用户【{0}】同步成功！".Fmt(data["fnumber"]));
                    }
                }
            }
            catch (Exception ex)
            {
                this.Result.ComplexMessage.ErrorMessages.Add("用户【{0}】同步失败：{1}".Fmt(data["fnumber"], ex.ToString()));
            }
        }
        /// <summary>
        /// 去掉空格，防止json转换过程报错
        /// </summary>
        /// <param name="data"></param>
        /// <param name="fieldName"></param>
        /// <returns></returns>
        private string convertTrimString(DynamicObject data,string fieldName)
        {
            return data[fieldName].IsNullOrEmptyOrWhiteSpace() ? "" : data[fieldName].ToString();
        }
    }
}