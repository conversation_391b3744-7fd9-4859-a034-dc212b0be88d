using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin
{
    /// <summary>
    /// 查看设计方案插件抽象类
    /// </summary>
    class AbdesignerProposal: AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                throw new BusinessException($"请先保存后再查看设计方案！");
            }
            if (e.DataEntitys.Length > 1)
            {
                throw new BusinessException($"该操作不支持批量执行！");
            }
            var dataEntity = e.DataEntitys[0];
            
            //设计方案主键ID
            var designerProposalIds = new List<string>();

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
                //new SqlParam("fbillno", System.Data.DbType.String, dataEntity["fbillno"])
            };

            switch (this.HtmlForm.Id.ToLower())
            {
                //销售意向
                case "ydj_saleintention":
                    //获取模型表单，根据名字查询模型
                    var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_designscheme");
                    //操作数据库
                    var dm = this.Container.GetService<IDataManager>();
                    dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                    string where = $"fmainorgid=@fmainorgid and fsourcenumber=@fbillno";
                    sqlParam.Add(new SqlParam("fbillno", System.Data.DbType.String, dataEntity["fbillno"]));
                    //  根据表单模型及表头过滤条件返回datareader
                    var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);   
                    //查询数据              
                 var   proposalRecords = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
                    if (proposalRecords != null)
                    {                     
                        designerProposalIds = proposalRecords.Select(o => o["id"] as string).ToList();
                    }
                    break;

                    //销售合同
                case "ydj_order":

                    //如果是增补销售合同，则查询原销售合同关联的设计方案记录
                    var strSql = "";
                    if (Convert.ToString(dataEntity["ftype"]).EqualsIgnoreCase("order_type_02"))
                    {
                        // sqlParam.Add(new SqlParam("fbillno", System.Data.DbType.String, dataEntity["fsourcenumber"]));
                        //如果当前单据是增补单，则直接查询关联的设计方案
                        strSql = "select fid from t_ydj_designscheme where fmainorgid=@fmainorgid and fsourcenumber=@fbillno ";
                    }
                    else {
                       
                        //找出 销售合同，销售意向 所关联的设计方案记录
                        strSql = $@"
                    select fid from t_ydj_designscheme where fmainorgid=@fmainorgid and fsourcenumber=@fbillno 
                    union 
                    select t3.fid from t_ydj_order t1 
                    inner join t_ydj_saleintention t2 on t1.fsourcenumber=t2.fbillno and t2.fmainorgid=@fmainorgid 
                    inner join t_ydj_designscheme t3 on t2.fbillno=t3.fsourcenumber and t3.fmainorgid=@fmainorgid 
                    where t1.fmainorgid=@fmainorgid and t1.fbillno=@fbillno";
                    }
                    sqlParam.Add(new SqlParam("fbillno", System.Data.DbType.String, dataEntity["fbillno"]));
                    using (var reader = this.DBService.ExecuteReader(this.Context, strSql, sqlParam))
                    {
                        while (reader.Read())
                        {
                            var scaleRecordId = reader.GetString("fid");
                            if (!scaleRecordId.IsNullOrEmptyOrWhiteSpace())
                            {                             
                                designerProposalIds.Add(scaleRecordId);
                            }
                        }
                    }

                    break;

                default:
                    break;
            }
            //是否存在设计方案主键ID
            if (designerProposalIds.Count<=0)
            {
                throw new BusinessException($"当前尚未存在设计方案，或者设计方案记录已被删除，请先添加设计方案后再查看");
            }            
            this.Result.SrvData = designerProposalIds;
            this.Result.IsSuccess = true;
        }
    }
}
