using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Api;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.BarcodeMaster;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sec
{
    /// <summary>
    /// 修改当前用户密码
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("modifypwd")]
    public class ModifyPassword : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            var newPwd = this.GetQueryOrSimpleParam<string>("newpwd");
            var gateway = this.Context.Container.GetService<IHttpServiceInvoker>();
            Dictionary<string,object> keyValuePairs = new Dictionary<string, object>();
            keyValuePairs.Add("IgnoreCheckPermssion", "true");
            keyValuePairs.Add("newpwd", newPwd);
            keyValuePairs.Add("fnumber", this.Context.UserName);
            keyValuePairs.Add("fphone", this.Context.UserName);
            var result = gateway.InvokeBillOperation(this.Context, "sec_user", e.DataEntitys, "sendmodifypwd",keyValuePairs);
        }
    }
}
