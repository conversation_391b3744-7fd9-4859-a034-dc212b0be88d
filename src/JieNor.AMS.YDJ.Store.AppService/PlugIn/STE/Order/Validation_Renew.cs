using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Core.Reserve;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{


    /// <summary>
    /// 销售合同：焕新校验
    /// </summary>
    public class Validation_Renew : AbstractBaseValidation
    {


        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public virtual string OperationDesc { get; private set; }

        public LoadReferenceObjectManager RefObjMgr { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }
            this.RefObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();

            //加载引用数据
            this.RefObjMgr?.Load(this.Context, dataEntities, false, formInfo, new List<string> { "frenewtype" });

            List<DynamicObject> checkDatas = new List<DynamicObject>();
            foreach (var item in dataEntities)
            {
                //只校验焕新订单
                if (!Convert.ToBoolean(item["frenewalflag"])) continue;
                checkDatas.Add(item);
            }
            if (!checkDatas.Any())
            {
                return result;
            }
            dataEntities = checkDatas.ToArray();
            //保存时的"重复性校验"和"焕新订单 必录"校验 挪到一键采购了
            foreach (var dataEntity in dataEntities)
            {
                CheckOrderData(userCtx, dataEntity, formInfo, result);
                //CheckIsRepeat(userCtx, dataEntity, formInfo, result);
            }

            return result;
        }
        /// <summary>
        /// 重复性校验
        /// 3、当【焕新订单标记】勾选 并且【结算进度】不等于“已退款” ，依据{【焕新订单类型】+【会员商城交易流水号】}做重复性校验。
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntity"></param>
        /// <param name="formInfo"></param>
        /// <param name="result"></param>
        private void CheckIsRepeat(UserContext userCtx, DynamicObject dataEntity, HtmlForm formInfo, ValidationResult result)
        {
            if (Convert.ToBoolean(dataEntity["frenewalflag"])
                    && !Convert.ToString(dataEntity["fsettlprogress"]).EqualsIgnoreCase("3")
                    && !Convert.ToString(dataEntity["frenewtype"]).IsNullOrEmptyOrWhiteSpace()
                    )
            {
                //会员商城交易流水号为空不校验
                if (Convert.ToString(dataEntity["fmembershiptranid"]).IsNullOrEmptyOrWhiteSpace()) return;

                var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "ydj_order");
                var dm = userCtx.Container.GetService<IDataManager>();
                dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));

                string where = $@" fmainorgid = @fmainorgid and frenewtype=@frenewtype and fsettlprogress != '3' and fmembershiptranid = @fmembershiptranid and fid != @fid ";
                var sqlParam = new SqlParam[]
                {
                    new SqlParam("fid", System.Data.DbType.String, dataEntity["id"]),
                    new SqlParam("frenewtype", System.Data.DbType.String, dataEntity["frenewtype"]),
                    new SqlParam("fmainorgid", System.Data.DbType.String, dataEntity["fmainorgid"]),
                    new SqlParam("fmembershiptranid", System.Data.DbType.String, dataEntity["fmembershiptranid"])
                };
                var dataReader = userCtx.GetPkIdDataReader(htmlForm, where, sqlParam);
                var order = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();

                if (order != null)
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $@"对不起，当前焕新订单{dataEntity["fbillno"]}的会员商城交易流水号 与{order["fbillno"]}冲突，请检查！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        /// <summary>
        /// 校验焕新订单 必录
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="formInfo"></param>
        /// <param name="result"></param>
        /// <param name="noOrders"></param>
        private void CheckOrderData(UserContext userCtx, DynamicObject dataEntity, HtmlForm formInfo, ValidationResult result)
        {
            //1、优先校验【焕新订单类型】必录。
            if (Convert.ToString(dataEntity?["frenewtype"] ?? "").IsNullOrEmptyOrWhiteSpace())
            {
                result.Errors.Add(new ValidationResultEntry()
                {
                    ErrorMessage = $@"对不起，焕新订单{dataEntity["fbillno"]}的焕新订单类型必填！",
                    DataEntity = dataEntity,
                });
                return;
            }
        }
    }
}
