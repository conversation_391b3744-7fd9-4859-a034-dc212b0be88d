using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Core.Reserve;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{


    /// <summary>
    /// 销售合同：库存校验
    /// </summary>
    public class Validation_Inv : AbstractBaseValidation
    {


        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public virtual string OperationDesc { get; private set; }

        public LoadReferenceObjectManager RefObjMgr { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }

            var orderService = this.Context.Container.GetService<IOrderService>();
            var editFlag = orderService.CheckEditSpecifiedField(userCtx, dataEntities);
            if (!editFlag) return result;

            this.RefObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();

            //如果单据只修改了【跟单备注】字段，则不需要检查库存
            List<DynamicObject> checkDatas = new List<DynamicObject>();
            foreach (var item in dataEntities)
            {
                var headSnapshot = item.GetDataEntitySnapshot();
                if (headSnapshot == null)
                {
                    //新增时没有快照，直接跳过
                    checkDatas.Add(item);
                    continue;
                }
                if (headSnapshot.ContainsKey("flogisticsitems") && !headSnapshot["flogisticsitems"].Changed)
                {
                    //【跟单备注】没有修改，直接跳过
                    checkDatas.Add(item);
                    continue;
                }
                if (headSnapshot.Any(t => t.Key != "flogisticsitems" && t.Value.Changed))
                {
                    //表头除了【跟单备注】之外的其他字段被修改
                    checkDatas.Add(item);
                    continue;
                }
                //判断表体中是否有被修改的
                var entrys = item["fentry"] as DynamicObjectCollection;
                foreach (var ent in entrys)
                {
                    var entSnapshot = ent.GetDataEntitySnapshot();
                    if (entSnapshot == null)
                    {
                        //有新增行
                        checkDatas.Add(item);
                        break; ;
                    }
                    if (entSnapshot.Any(t => t.Value.Changed))
                    {
                        //表体【商品明细】中有字段被修改
                        checkDatas.Add(item);
                        break;
                    }
                }
            }
            if (!checkDatas.Any())
            {
                ////加载引用数据
                //var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
                //refObjMgr?.Load(this.Context, formInfo.GetDynamicObjectType(this.Context), dataEntities, false);
                return result;
            }
            dataEntities = checkDatas.ToArray();

            CheckStockUsableQty(userCtx, formInfo, dataEntities, result, option);

            return result;
        }





        /// <summary>
        /// 检查（停购/库存状态.样品/出现货）商品可用量
        /// 系统内所有（停购/库存状态.样品/出现货）合同单未出库的商品数不得超过（系统现有的库存数 - 与当前订单无关的预留数）
        /// </summary>
        /// <param name="newData"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        private void CheckStockUsableQty(UserContext ctx, HtmlForm formInfo, DynamicObject[] dataEntitys, ValidationResult result, OperateOption option)
        {
            var systemProfileService = ctx.Container.GetService<ISystemProfile>();
            var initStatus = systemProfileService.GetSystemParameter(this.Context, "stk_invcompleteinit", "finitstatus", "");

            //如果没有结束初始化库存，无需检查
            if (initStatus.EqualsIgnoreCase("invinitstatus_type_02") == false)
            {
                return;
            }

            //加载引用数据
            this.RefObjMgr?.Load(this.Context, dataEntitys, false, formInfo, new List<string> { "fproductid", "fstockstatus" });

            var entryEntity = formInfo.GetEntryEntity("fentry");

            //获取合同变更快照信息
            var metaSnap = this.MetaModelService.LoadFormModel(this.Context, "bas_billchangesnapshot");
            var dmSnap = this.GetDataManager(this.Context.Container, option);
            dmSnap.InitDbContext(this.Context, metaSnap.GetDynamicObjectType(this.Context));
            var serializer = this.Context.Container.GetService<IDynamicSerializer>();
            var ids = dataEntitys.Where(t => Convert.ToString(t["fchangestatus"]) == "1").Select(t => Convert.ToString(t["id"])).ToList();
            List<DynamicObject> snaps = dmSnap.SelectBy("fbillinterid in ('{0}') and fmainorgid='{1}' and fworkobject='{2}'".Fmt(string.Join("','", ids),
            this.Context.Company, formInfo?.Id)).OfType<DynamicObject>().ToList();
            //解析快照信息
            var fbillsnapshots = snaps?.Select(t => Convert.ToString(t["fbillsnapshot"])).ToList();
            List<DynamicObject> snapDatas = new List<DynamicObject>();
            fbillsnapshots?.ForEach(t => snapDatas.Add(serializer.FromDynamicJson<DynamicObject>(formInfo.GetDynamicObjectType(this.Context),
                    t)));
            this.RefObjMgr?.Load(this.Context, snapDatas.ToArray(), false, formInfo, new List<string> { "fproductid", "fstockstatus" });

            List<DynamicObject> allBeCheckEnRows = GetNeedCheckEntryRow(ctx, formInfo, dataEntitys, result, snapDatas);
            if (!allBeCheckEnRows.Any())
            {
                return;
            }

            //获取商品的库存情况
            List<BizInvCheckResult> invQtyInfo = GetInventorInfo(ctx, formInfo, dataEntitys);
            if (invQtyInfo.Count > 0)
            {
                //按库存维度汇总（可能存在多行一样的商品）
                var grpBy = allBeCheckEnRows.GroupBy(f => new
                {
                    fmaterialid = Convert.ToString(f["fproductid"]),
                    fattrinfo = Convert.ToString(f?["fattrinfo"]),
                    fattrinfo_e = Convert.ToString(f?["fattrinfo_e"]),
                    fcustomdesc = Convert.ToString(f["fcustomdes_e"]),
                    funitid = Convert.ToString(f["funitid"]),
                    //fstockstatus = Convert.ToString(f["fstockstatus"]),
                    fmtono = Convert.ToString(f["fmtono"]),
                    //fstorehouseid = Convert.ToString(f["fstorehouseid"]),
                    //fstorelocationid = Convert.ToString(f["fstorelocationid"]),
                }).ToList();

                foreach (var item in grpBy)
                {
                    var invDatas = invQtyInfo.Where(f => f.fmaterialid == item.Key.fmaterialid && f.fattrinfo_e.Trim() == item.Key.fattrinfo_e.Trim()
                                                        && f.fcustomdesc.Trim() == item.Key.fcustomdesc.Trim() && f.funitid.Trim() == item.Key.funitid.Trim()
                                                        && f.fmtono.Trim() == item.Key.fmtono.Trim())?.ToList();
                    //如果单据上录入了仓库仓位，则具体到对应的仓库仓位进行比较
                    //if (!item.Key.fstorehouseid.IsNullOrEmptyOrWhiteSpace())
                    //{
                    //    invDatas = invDatas?.Where(f => f.fstorehouseid.EqualsIgnoreCase(item.Key.fstorehouseid))?.ToList();
                    //}

                    //if (!item.Key.fstorelocationid.IsNullOrEmptyOrWhiteSpace())
                    //{
                    //    invDatas = invDatas?.Where(f => f.fstorelocationid.EqualsIgnoreCase(item.Key.fstorelocationid))?.ToList();
                    //}

                    if (invDatas == null || invDatas.Count == 0)
                    {
                        continue;
                    }

                    var qty_bill = invDatas.Sum(f => f.fqty_bill);//当前单据数量 
                    var qty_inv = invDatas.Sum(f => f.fqty_inv);//即时库存
                    var qty_onWay = invDatas.Sum(f => f.fqty_onWay);//在途量
                    var qty_reserve = invDatas.Sum(f => f.fqty_reserve); //预留量
                    var qty_diff = invDatas.Sum(f => f.fqty);//差额：即时库存 - 预留量 + 在途量 - 当前单据数量

                    if (qty_diff < 0)
                    {
                        var matObj = item.First()["fproductid_ref"] as DynamicObject;
                        if (matObj == null)
                        {
                            continue;
                        }
                        var matInfo = "{0} {1}".Fmt(matObj["fname"], matObj["fnumber"]);
                        var invMsg = "预计出库量 {0}，库存量 {1}，预留量 {2}，差异量 {3}".Fmt(GetDecimalFmt(qty_bill), GetDecimalFmt(qty_inv), GetDecimalFmt(qty_reserve), GetDecimalFmt(qty_diff));
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = @"商品【{0}】已经停购且库存不足【{1}】，不允许下单！".Fmt(matInfo, invMsg),
                            DataEntity = item.First().Parent as DynamicObject,
                        });
                    }
                }
            }
        }

        private string GetDecimalFmt(decimal qty)
        {
            if (qty == 0)
            {
                return "0";
            }

            var str = qty.ToString().Split('.');
            if (str.Length == 1)
            {
                return qty.ToString();
            }

            var x = str[1].TrimEnd('0');
            if (x.IsNullOrEmptyOrWhiteSpace())
            {
                return str[0];
            }

            var result = str[0] + "." + x;
            return result;
        }

        /// <summary>
        /// 获取库存校验结果
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntitys"></param>
        /// <returns></returns>
        private List<BizInvCheckResult> GetInventorInfo(UserContext ctx, HtmlForm formInfo, DynamicObject[] dataEntitys)
        {
            var checkPara = new BizInvCheckPara()
            {
                fbizobject = new Framework.DataTransferObject.BaseDataSummary()
                { Id = formInfo.Id, Name = formInfo.Caption, Number = formInfo.Id },
                fctrlfldkey = new Framework.DataTransferObject.BaseDataSummary()
                {
                    Id = "",// "fstorehouseid,fstorelocationid,fstockstatus",
                    Name = "",// "仓库,仓位,库存状态",
                    Number = "",// "fstorehouseid,fstorelocationid,fstockstatus"
                },
                fctrlstrength = new Framework.DataTransferObject.BaseDataSummary()
                { Id = "1", Name = "严格控制", Number = "1" },
                fonwaybill = new Framework.DataTransferObject.BaseDataSummary(),
                freserveqty = true,
                OperationNo = this.OperationNo,
            };
            var invSvc = ctx.Container.GetService<IBizCheckInvService>();
            var checkResult = invSvc.BizCheckInventory(ctx, formInfo, dataEntitys, checkPara, OperateOption.Create());

            return checkResult;
        }

        /// <summary>
        /// 获取需要进行库存校验的商品明细行
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntitys">待校验的销售合同</param>
        /// <param name="result">校验结果</param>
        /// <returns></returns>
        private List<DynamicObject> GetNeedCheckEntryRow(UserContext ctx, HtmlForm formInfo, DynamicObject[] dataEntitys, ValidationResult result, List<DynamicObject> snapDatas = null)
        {
            //ExtendedDataEntitySet dataSet = new ExtendedDataEntitySet();
            //dataSet.Parse(this.Context, dataEntitys, formInfo);
            //var entrys = dataSet.FindByEntityKey("fentry").Select(o => o.DataEntity).ToList();
            var entrys = dataEntitys.SelectMany(o => o["fentry"] as DynamicObjectCollection).ToList();

            //变更数据明细集合
            var snapDataEntrys = (snapDatas.SelectMany(t => t["fentry"] as DynamicObjectCollection).ToList());

            // 过滤停购商品明细（兼容旧逻辑）
            IOrderService orderService = ctx.Container.GetService<IOrderService>();
            var endSaleEntrys = orderService.FilterEndPurchase(this.Context, entrys, snapDatas);
            if (endSaleEntrys.Count <= 0)
            {
                return endSaleEntrys;
            }

            //库存状态.样品=是 或者 出现货=否 的商品行，不需要校验库存，直接提示不允许下单
            List<DynamicObject> notOutSpotEntrys = new List<DynamicObject>();
            foreach (var entry in endSaleEntrys)
            {
                var qty = Convert.ToDecimal(entry["fqty"]);
                var outQty = Convert.ToDecimal(entry["foutqty"]);
                if (qty <= outQty)
                {
                    notOutSpotEntrys.Add(entry);
                    continue;//已经全部出库了，不做校验
                }

                int.TryParse(Convert.ToString(entry["fclosestatus_e"]), out var close);

                if (close == (int)CloseStatus.Auto || close == (int)CloseStatus.Whole || close == (int)CloseStatus.Manual)
                {
                    notOutSpotEntrys.Add(entry);
                    continue;//自动关闭、手工关闭、整单关闭行不做校验
                }
                var matObj = entry["fproductid_ref"] as DynamicObject;
                if (matObj == null)
                {
                    continue;
                }
                var matInfo = "{0} {1}".Fmt(matObj["fname"], matObj["fnumber"]);

                //var isChange = dataEntitys.Any(t=>(t["fentry"] as DynamicObjectCollection).Any(te=>Convert.ToString(te["id"]) == Convert.ToString(entry["id"])) && Convert.ToString(t["fchangestatus"]) == "1");
                // 1. 库存状态.样品=是，直接提示
                var stockStatus = entry["fstockstatus_ref"] as DynamicObject;
                bool isSample = Convert.ToBoolean(stockStatus?["fissample"]);
                if (isSample)
                {
                    notOutSpotEntrys.Add(entry);
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = @"商品【{0}】已经停购且库存状态是样品，不允许下单！".Fmt(matInfo),
                        DataEntity = entry.Parent as DynamicObject,
                    });
                    continue;
                }
                // 2. 出现货=否，直接提示
                /*
                7.   变更修改完后，点击<保存>，对于在未变更前已添加的非出现货商品行，不需要参与停购校验
                   a)   变更保存时，拿当前属于【停购】=是 或【销售组织】=已禁用的明细商品行与快照里面的原始明细商品行进行匹配，若匹配找得到，则该行明细商品不参与停购校验
                */

                //《销售合同》在审核状态下点击<保存>不需要进行停购停产的校验
                var isfromfirstinventory = dataEntitys.Any(t => (t["fentry"] as DynamicObjectCollection).Any(te => Convert.ToString(te["id"]) == Convert.ToString(entry["id"])&& Convert.ToBoolean(te["fisfromfirstinventory"])));
                if (isfromfirstinventory) { continue;}
                var isexsit = dataEntitys.Any(t => (t["fentry"] as DynamicObjectCollection).Any(te => Convert.ToString(te["id"]) == Convert.ToString(entry["id"])) && Convert.ToString(t["fstatus"]) == "E");
                if (!isexsit)
                {
                    var isSnapDataEntry = true;
                    var fsaleorgentrys = matObj["fsaleorgentry"] as DynamicObjectCollection;
                    var isDisable = !fsaleorgentrys?.Any(t => Convert.ToString(t["fdisablestatus"]) == "1");//是否全部禁用，只要存在启用的则不需要验证是否组织禁用
                    if (Convert.ToBoolean(matObj["fendpurchase"]) || Convert.ToBoolean(isDisable))//【停购】= 是 或【销售组织】= 已禁用
                    {
                        isSnapDataEntry = snapDataEntrys.Any(t => Convert.ToString(t["id"]) == Convert.ToString(entry["id"]));
                    }
                    var isOutSpot = Convert.ToBoolean(entry["fisoutspot"]);
                    if (!isOutSpot && !isSnapDataEntry)
                    {
                        notOutSpotEntrys.Add(entry);
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = @"商品【{0}】已经停购且非出现货，不允许下单！".Fmt(matInfo),
                            DataEntity = entry.Parent as DynamicObject,
                        });
                        continue;
                    }
                }
            }

            // 需要进行库存可用量判断的行（排除直接提示不允许下单的行）
            foreach (var entry in notOutSpotEntrys)
            {
                endSaleEntrys.Remove(entry);
            }

            return endSaleEntrys;
        }

        /// <summary>
        /// orm数据读写引擎
        /// </summary>
        protected IDataManager GetDataManager(IServiceContainer container, OperateOption option)
        {
            if (this.Context == null) return null;
            var dm = container?.GetService<IDataManager>();
            dm.Option = option;
            return dm;
        }


    }


}
