using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.Stock;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 销售订单：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 移动端挂单结算下推合同的数据
        /// </summary>
        private JArray orderInfos = null;

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fbilltypeid"]).NotEmpty().WithMessage("单据类型不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fdate"]).NotEmpty().WithMessage("业务日期不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //外部带单是否可为空
                var channelNullable = true;
                var spService = this.Container.GetService<ISystemProfile>();
                string sysProfileValue = spService.GetProfile(this.Context, "fw", $"bas_storesysparam_parameter");
                if (!sysProfileValue.IsNullOrEmptyOrWhiteSpace())
                {
                    var storeSysParam = JObject.Parse(sysProfileValue);
                    if (storeSysParam != null && storeSysParam["fischannelnullable"] != null)
                    {
                        channelNullable = Convert.ToBoolean(storeSysParam["fischannelnullable"]);
                    }
                }
                if (channelNullable) return true;
                return !newData["fchannel"].IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("外部带单不能为空！"));

            //“销售员信息”必须符合以下要求
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return CheckDutyEntry(newData);
            }).WithMessage(@"销售员信息【销售员、比例、金额】不能为空，
                            且最多只能有一个主要销售员，
                            且比例必须等于100！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var phone = Convert.ToString(newData["fphone_e"]);
                if (phone.IsNullOrEmptyOrWhiteSpace()) return true;
                var profileService = this.Container.GetService<ISystemProfile>();
                var enablePhoneLengthCtrl = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablephonelengthctrl", false);
                if (false == enablePhoneLengthCtrl)
                {
                    return true;
                }
                return phone.Trim().Length == 11;
            }).WithMessage("手机号填写11位数字"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var phone = Convert.ToString(newData["fphone_e"]);
                if (phone.IsNullOrEmptyOrWhiteSpace()) return true;
                var profileService = this.Container.GetService<ISystemProfile>();
                var enablePhoneLengthCtrl = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablephonelengthctrl", false);
                if (false == enablePhoneLengthCtrl)
                {
                    return true;
                }
                else
                {
                    if (!CheckPhoneNum.isNumeric(phone.Replace(" ", "")))
                    {
                        return false;
                    }
                    return true;
                }
            }).WithMessage("手机号存在特殊字符不符合规范！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var phone = Convert.ToString(newData["fphone_e"]);
                if (phone.IsNullOrEmptyOrWhiteSpace()) return true;
                var profileService = this.Container.GetService<ISystemProfile>();
                var enablePhoneLengthCtrl = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablephonelengthctrl", false);
                if (false == enablePhoneLengthCtrl)
                {
                    return true;
                }
                else
                {
                    if (phone.IndexOf(" ") > 0)
                    {
                        return false;
                    }
                    return true;
                }
            }).WithMessage("手机号存在空格不符合规范！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var profileService = this.Container.GetService<ISystemProfile>();
                var fphonestandardcontrol = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fphonestandardcontrol", false);
                string phoneNo = Convert.ToString(newData["fphone_e"]);
                if (!CheckPhoneNum.CheckPhone(this.Context, phoneNo, fphonestandardcontrol))
                {
                    return false;
                }
                return true;
            }).WithMessage("手机号前3位不符合国内电信号段规范，请填入国内真实手机号！"));

            /*
                定义表体校验规则
            */
            //至少要有一行商品明细
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var profileService = this.Container.GetService<ISystemProfile>();
                var emptySaletentionEntry = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "femptysaletentionentry", true);
                if (emptySaletentionEntry)
                {
                    return true;
                }
                DynamicObjectCollection entitys = newData["fentity"] as DynamicObjectCollection;
                return !(entitys == null || entitys.Count <= 0);

            }).WithMessage("至少要有一行商品明细！"));

            // R-BL-B009库存查询-清库存及可销库存控制
            IInventoryService inventoryService = this.Container.GetService<IInventoryService>();
            string errorMessage = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";

                var entry = newData["fentity"] as DynamicObjectCollection;

                return inventoryService.CheckInventory(this.Context, entry, this.HtmlForm.Id, out errorMessage);

            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }

        /// <summary>
        /// 检查“销售员信息明细”是否合法
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool CheckDutyEntry(DynamicObject newData)
        {
            DynamicObjectCollection entry = newData["fdutyentry"] as DynamicObjectCollection;

            //至少要有一行销售员信息
            if (entry == null || entry.Count <= 0) return true; //兼容历史单据没有明细的情况

            bool isVlid = true;

            string dutyId = "";
            decimal ratio = 0, amount = 0, ratioSum = 0;
            int dutyCount = 0;
            bool isMain = false;

            //要移除的明细列表
            List<DynamicObject> toRemoveList = new List<DynamicObject>();

            foreach (DynamicObject item in entry)
            {
                isMain = Convert.ToBoolean(item["fismain"]);
                dutyId = Convert.ToString(item["fdutyid"]);
                ratio = Convert.ToDecimal(item["fratio"]);
                amount = Convert.ToDecimal(item["famount"]);

                if (dutyId.IsNullOrEmptyOrWhiteSpace() && ratio <= 0)
                {
                    toRemoveList.Add(item);
                    continue;
                }

                //主销售员 如果不是空行，则以下字段不能为空
                if (isMain && (dutyId.IsNullOrEmptyOrWhiteSpace() || ratio <= 0 || amount < 0))
                {
                    isVlid = false;
                    break;
                }

                if (isMain)
                {
                    dutyCount++;
                }

                ratioSum += ratio;
            }

            foreach (var item in toRemoveList)
            {
                entry.Remove(item);
            }

            if (!isVlid) { return isVlid; }

            //至少要有一个主要销售员，且最多只能有一个主要销售员
            //if (dutyCount == 0 || dutyCount > 1)

            //至少要有一个主要销售员，其它为带单员
            if (dutyCount == 0)
            {
                return false;
            }

            //明细比例汇总必须 = 100
            return ratioSum == 100;
        }

        /// <summary>
        /// 检查商场合同号必录性
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private DynamicObject[] checkMallorderno(DynamicObject[] dataEntities)
        {
            var checkEntities = dataEntities?.Where(x => string.IsNullOrWhiteSpace(Convert.ToString(x["fmallorderno"]))).ToArray();
            if (checkEntities == null || checkEntities.Length <= 0)
            {
                return dataEntities;
            }
            var deptIds = checkEntities.Select(x => Convert.ToString(x["fdeptid"]))
                                       .Where(x => false == string.IsNullOrWhiteSpace(x))
                                       .Distinct()
                                       .ToList();
            if (deptIds == null || deptIds.Count <= 0)
            {
                return dataEntities;
            }

            var deptForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_dept");
            var deptDm = this.Container.GetService<IDataManager>();
            deptDm.InitDbContext(this.Context, deptForm.GetDynamicObjectType(this.Context));
            deptIds = deptDm.Select(deptIds)
                            .OfType<DynamicObject>()
                            .Where(x => Convert.ToBoolean(x["fisunifiedorder"]))
                            .Select(x => Convert.ToString(x["id"]))
                            .ToList();
            if (deptIds == null || deptIds.Count <= 0)
            {
                return dataEntities;
            }

            var results = new List<DynamicObject>();
            foreach (var dataEntity in dataEntities)
            {
                if (false == string.IsNullOrWhiteSpace(Convert.ToString(dataEntity["fmallorderno"])))
                {
                    results.Add(dataEntity);
                    continue;
                }
                var deptId = Convert.ToString(dataEntity["fdeptid"]);
                if (false == deptIds.Contains(deptId))
                {
                    results.Add(dataEntity);
                    continue;
                }
                this.Result.ComplexMessage.ErrorMessages.Add($"单据[{dataEntity["fbillno"]}]的商场合同号不能为空！");
            }
            return results.ToArray();
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            e.DataEntitys = checkMallorderno(e.DataEntitys);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            // 是否是小程序API发起的保存操作
            var callerTerminal = this.Option.GetVariableValue("callerTerminal", "");
            if (callerTerminal.EqualsIgnoreCase("MPAPI"))
            {
                // 根据指定的业务单位数量自动反算关联基本单位数量字段
                var unitService = this.Container.GetService<IUnitConvertService>();
                unitService.ConvertByBizQty(this.Context, this.HtmlForm, e.DataEntitys, "fbizqty",
                    OperateOption.Create());
            }

            // JN_XCX_R031 小程序-意向单-创建意向单生成商机-接口
            // 判断意向单是否源自商机，如果没有，则创建
            CreateCustomerRecordIfSaleIntentionNoCustomerRecord(e.DataEntitys);

            var orderInfoStr = this.GetQueryOrSimpleParam<string>("orders");

            var saleService = this.Container.GetService<ISaleIntentionService>();
            //var productInfoService = this.Container.GetService<IProductInfoService>();
            //productInfoService.CheckProductIsPullOffShelves(this.Context, "fmaterialid", this.HtmlForm, e.DataEntitys);
            var customerField = this.HtmlForm.GetField("fcustomerid") as HtmlBaseDataField;
            var linkStaffField = this.HtmlForm.GetField("flinkstaffid");

            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), e.DataEntitys, true);
            foreach (var dataEntity in e.DataEntitys)
            {
                //检查收货人是否有值，没有默认携带客户的联系人
                var customerObj = customerField?.RefDynamicProperty.GetValue<DynamicObject>(dataEntity);
                var linkStaffId = linkStaffField?.DynamicProperty.GetValue<string>(dataEntity);
                if (linkStaffId.IsNullOrEmptyOrWhiteSpace())
                {
                    linkStaffField?.DynamicProperty.SetValue(dataEntity, customerObj?["fcontacts"]);
                }

                //如果是协同订单，则需要协同文件到对方系统
                if (saleService.CheckIsSync(this.Context, dataEntity))
                {
                    this.CheckEntryProduct(dataEntity);

                    //设置图纸信息上传人
                    DynamicObjectCollection drawEntitys = dataEntity["fdrawentity"] as DynamicObjectCollection;
                    if (drawEntitys != null)
                    {
                        foreach (var drawEntity in drawEntitys)
                        {
                            if (drawEntity["fuploader"].IsNullOrEmptyOrWhiteSpace())
                            {
                                var uploader = this.Context.DisplayName;
                                if (uploader.IsNullOrEmptyOrWhiteSpace())
                                {
                                    uploader = this.Context.UserName;
                                }
                                drawEntity["fuploader"] = uploader;
                            }
                        }
                    }

                    //找出销售方上传的文件，然后协同给采购方
                    List<DynamicObject> synFiles = null;
                    if (drawEntitys != null)
                    {
                        synFiles = drawEntitys.Where(t => t["fsourceentryid"].IsNullOrEmptyOrWhiteSpace()).ToList();
                    }

                    //更新采购方的采购订单信息
                    this.SendSynUpdatePurchaseOrder(dataEntity, synFiles);
                }

                //如果是移动端挂单结算，则将意向单设置为已审核状态
                if (string.IsNullOrWhiteSpace(orderInfoStr) == false)
                {
                    dataEntity["fstatus"] = "E";
                }
            }

            UpdateCustomer(e.DataEntitys);

            HandleCustomerRecord(e.DataEntitys);
        }

        /// <summary>
        /// 检查明细商品是否已发布
        /// </summary>
        /// <param name="dataEntity"></param>
        private void CheckEntryProduct(DynamicObject dataEntity)
        {
            var entrys = dataEntity["fentity"] as DynamicObjectCollection;
            if (entrys != null && entrys.Count > 0)
            {
                var productNames = new List<string>();
                foreach (var entry in entrys)
                {
                    var product = entry["fmaterialid_ref"] as DynamicObject;
                    if (product == null) continue;

                    var sendStatus = Convert.ToString(product["fsendstatus"]);
                    if (sendStatus != "已发布" && sendStatus != "待更新")
                    {
                        productNames.Add($"{product["fnumber"] as string}/{product["fname"] as string}");
                    }
                }
                if (productNames.Count > 0)
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"{this.HtmlForm.Caption} {dataEntity["fbillno"]} 商品【{string.Join("，", productNames)}】");
                    throw new BusinessException($"{this.HtmlForm.Caption}的以下商品发布状态不是 已发布/待更新，不允许保存！");
                }
            }
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if (orderInfos == null || orderInfos.Count <= 0 || this.Result.IsSuccess == false)
            {
                return;
            }
            var result = JArray.FromObject(this.Result.SrvData);
            for (var i = 0; i < result.Count; i++)
            {
                var item = result[i];
                var order = orderInfos[i];
                item["orderId"] = order["id"];
                item["orderName"] = order["name"];
                item["orderNumber"] = order["number"];
            }
            this.Result.SrvData = result;
        }

        /// <summary>
        /// 更新采购方的采购订单信息
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="synFiles"></param>
        private void SendSynUpdatePurchaseOrder(DynamicObject dataEntity, List<DynamicObject> synFiles)
        {
            //客户信息
            var customer = this.GetCustomerById(dataEntity);

            //客户的协同企业ID
            var cooCompanyId = customer["fcoocompanyid"] as string;
            var cooProductId = customer["fcooproductid"] as string;
            if (cooCompanyId.IsNullOrEmptyOrWhiteSpace()) return;

            //报价清单字段的协同
            var quoteId = Convert.ToString(dataEntity["fquotelist"]);
            var quoteName = Convert.ToString(dataEntity["fquotelist_txt"]);

            //数据发送时采用异步消息模式发送，消息中指定回调类型
            var responseResult = this.Gateway.Invoke(
                this.Context,
                new TargetSEP(cooCompanyId, cooProductId),
                new CommonBillDTO()
                {
                    FormId = "ydj_purchaseorder",
                    OperationNo = "UpdateSyn",
                    BillData = "",
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SimpleData = new Dictionary<string, string>
                    {
                        { "tranId", dataEntity["ftranid"] as string },
                        { "quoteId", quoteId },
                        { "quoteName", quoteName },
                        { "synFiles", synFiles.ToJson() }
                    }
                }) as CommonBillDTOResponse;
            responseResult?.OperationResult?.ThrowIfHasError(true, $"协同更新失败，对方系统未返回任何响应！");
        }

        /// <summary>
        /// 准备操作关联服务
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        {
            e.Services.Add(new FormServiceDesc()
            {
                ServiceAlias = "filesyn",
                Condition = "fsourceentryid==null or fsourceentryid=='' or fsourceentryid==' '",
                ParamString = new
                {
                    companyFieldKey = "fcustomerid.fcoocompanyid",
                    productFieldKey = "fcustomerid.fcooproductid",
                }.ToJson()
            });
            //e.Services.Add(new FormServiceDesc { ServiceAlias = "SaveOrder" });
        }

        /// <summary>
        /// 获取客户信息
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private DynamicObject GetCustomerById(DynamicObject dataEntity)
        {
            string customerId = Convert.ToString(dataEntity["fcustomerid"]);
            if (customerId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"客户ID为空！");

            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_customer");
            var dm = this.Container.GetService<IDataManager>();

            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            var customer = dm.Select(customerId) as DynamicObject;
            if (customer == null) throw new BusinessException($"客户不存在！");

            return customer;
        }

        /// <summary>
        /// 调用操作事物后触发的时间
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            //选择设计方案
            SaveDTypeAndNumber(e.DataEntitys);
            //选择量尺
            SaveSTypeAndNumber(e.DataEntitys);
            //移动端挂单结算下推合同
            PushOrder(e.DataEntitys);
            //出现货自动预留
            OutSpotAutoReserve(e.DataEntitys);
        }

        /// <summary>
        /// 选择设计方案
        /// </summary>
        /// <param name="dataEntities"></param>
        private void SaveDTypeAndNumber(DynamicObject[] dataEntities)
        {
            //获取表单模型
            var metaModelService = this.Container.GetService<IMetaModelService>();
            //加载设计方案模型数据
            var designFrom = this.MetaModelService.LoadFormModel(this.Context, "ydj_designscheme");
            //操作数据库
            var dm = this.Container.GetService<IDataManager>();
            //初始化上下文
            dm.InitDbContext(this.Context, designFrom.GetDynamicObjectType(this.Context));
            //方案编号
            foreach (var dataEntity in dataEntities)
            {
                var responsorFdesignscheme = Convert.ToString(dataEntity["fdesignscheme"]);
                var responsFbillno = dataEntity["fbillno"];

                //根据设计方案表单模型及编码信息返回设计方案信息datareader
                var dataReader = this.Context.GetPkIdDataReaderWithNumber(designFrom, new List<string> { responsorFdesignscheme });

                //查询设计方案数据               
                var selectFdesignscheme = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();

                //判断是否存在方案编号
                if (!responsorFdesignscheme.IsNullOrEmptyOrWhiteSpace())
                {
                    var responsFsourcetype = Convert.ToString(selectFdesignscheme?["fsourcetype"]);
                    var responsFnumber = Convert.ToString(selectFdesignscheme?["fsourcenumber"]);

                    //如果不存在，源单类型为销售合同，源单编号为合同编号
                    if (responsFsourcetype.IsNullOrEmptyOrWhiteSpace() && responsFnumber.IsNullOrEmptyOrWhiteSpace())
                    {
                        selectFdesignscheme["fsourcetype"] = "ydj_saleintention";
                        selectFdesignscheme["fsourcenumber"] = responsFbillno;

                    }
                    //初始化上下文
                    dm.InitDbContext(this.Context, designFrom.GetDynamicObjectType(this.Context));
                    dm.Save(selectFdesignscheme);
                    // this.AddRefreshPageAction();
                    this.Result.IsSuccess = true;
                    this.Result.SimpleMessage = "保存成功";
                }
            }
        }

        /// <summary>
        /// 选择量尺
        /// </summary>
        /// <param name="dataRntities"></param>
        private void SaveSTypeAndNumber(DynamicObject[] dataEntities)
        {
            //获取表单模型
            var motaModelService = this.Container.TryGetService<IMetaModelService>();
            //加载量尺模型数据
            var scalerFrom = this.MetaModelService.LoadFormModel(this.Context, "ydj_scalerecord");
            //操作数据库
            var dm = this.Container.GetService<IDataManager>();
            //初始化上下文
            dm.InitDbContext(this.Context, scalerFrom.GetDynamicObjectType(this.Context));
            foreach (var dataEntity in dataEntities)
            {
                var responsorFbillno = dataEntity["fbillno"];
                //量尺编号
                var responsorFscalere = Convert.ToString(dataEntity["fscalerecord"]);
                //根据量尺表单模型及编码信息返回量尺信息datareader
                var dataReader = this.Context.GetPkIdDataReaderWithNumber(scalerFrom, new List<string> { responsorFscalere });
                //查询量尺数据
                var selectFscalere = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();

                //判断是否存在量尺编号
                if (!responsorFscalere.IsNullOrEmptyOrWhiteSpace())
                {
                    var responsFsourcetype = Convert.ToString(selectFscalere?["fsourcetype"]);
                    var responsFnumber = Convert.ToString(selectFscalere?["fsourcenumber"]);
                    //如果不存在源单类型和源单编号，源单类型为销售合同，源单编号为合同编号
                    if (responsFsourcetype.IsNullOrEmptyOrWhiteSpace() && responsFnumber.IsNullOrEmptyOrWhiteSpace())
                    {
                        selectFscalere["fsourcetype"] = "ydj_saleintention";
                        selectFscalere["fsourcenumber"] = responsorFbillno;
                    }
                    dm.InitDbContext(this.Context, scalerFrom.GetDynamicObjectType(this.Context));
                    dm.Save(selectFscalere);
                    //this.AddRefreshPageAction();
                    this.Result.IsSuccess = true;
                    this.Result.SimpleMessage = "保存成功";
                }
            }


        }

        /// <summary>
        /// 移动端挂单结算下推合同
        /// </summary>
        /// <param name="dataEntities"></param>
        private void PushOrder(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }
            var orderInfoStr = this.GetQueryOrSimpleParam<string>("orders");
            if (string.IsNullOrWhiteSpace(orderInfoStr))
            {
                return;
            }
            var orders = JArray.Parse(orderInfoStr);
            if (orders == null || orders.Count <= 0)
            {
                return;
            }
            if (dataEntities.Length != orders.Count)
            {
                throw new BusinessException("挂单的数量与合同的数量不相等");
            }

            //将销售意向单下推生成销售合同
            string targetFormId = "ydj_order";
            var convertService = this.Container.GetService<IConvertService>();
            DynamicObject[] targetDataObjects = null;

            var pushResult = convertService.Push(this.Context, new BillConvertContext()
            {
                RuleId = "ydj_saleintention2ydj_order",
                SourceFormId = this.HtmlForm.Id,
                TargetFormId = targetFormId,
                SelectedRows = dataEntities.Select(x => new SelectedRow { PkValue = Convert.ToString(x["Id"]) }).ToConvertSelectedRows(),
                Option = this.Option
            });
            targetDataObjects = (pushResult.SrvData as ConvertResult)?.TargetDataObjects?.ToArray();

            if (targetDataObjects == null || targetDataObjects.Length <= 0 || targetDataObjects.Length != orders.Count)
            {
                throw new BusinessException("意向单转销售合同失败!");
            }

            //将移动端传回的合同数据合并到合同中
            var metaModelService = this.Container.GetService<IMetaModelService>();
            var dcSerializer = this.Container.GetService<IDynamicSerializer>();
            var targetForm = metaModelService.LoadFormModel(this.Context, targetFormId);
            var targetType = targetForm.GetDynamicObjectType(this.Context);
            var prepareService = this.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(this.Context, targetForm, targetDataObjects, OperateOption.Create());

            for (var i = 0; i < targetDataObjects.Length; i++)
            {

                JObject orderObject = orders[i] as JObject;
                DynamicObject targetEntity = targetDataObjects[i];
                orderObject["id"] = Convert.ToString(targetEntity["id"]);
                foreach (var entryForm in targetForm.EntryList)
                {
                    if (entryForm is HtmlSubEntryEntity)
                    {
                        continue;
                    }
                    var entryEntities = targetEntity[entryForm.Id] as DynamicObjectCollection;
                    var orderEntries = orderObject[entryForm.Id] as JArray;

                    if (entryEntities == null || entryEntities.Count <= 0 || orderEntries == null || orderEntries.Count <= 0)
                    {
                        continue;
                    }

                    int count = Math.Min(entryEntities.Count, orderEntries.Count);

                    for (var j = 0; j < count; j++)
                    {
                        var entry = entryEntities[j];
                        var orderEntry = orderEntries[j];
                        orderEntry["id"] = Convert.ToString(entry["id"]);
                    }
                }
            }

            dcSerializer.Sync(targetType, targetDataObjects, orders, (propKey) =>
            {
                var el = targetForm.GetElement(propKey);
                if (el is HtmlField) return (el as HtmlField).DynamicProperty;
                if (el is HtmlEntryEntity) return (el as HtmlEntryEntity).DynamicProperty;
                return null;
            },
             null,
             null,
             null);

            //保存合同 传是否处理 默认单据类型的 参数
            var saveResult = this.Gateway.InvokeBillOperation(this.Context,
                                                                targetFormId,
                                                                 targetDataObjects,
                                                                "save",
                                                                new Dictionary<string, object> { { "handleBillType", "true" } }
                                                             );

            saveResult.ThrowIfHasError(true, "销售合同保存失败!");

            orderInfos = JArray.FromObject(saveResult.SrvData);
        }

        /// <summary>
        /// 预留更新
        /// </summary>
        /// <param name="dataEntities"></param>
        private void OutSpotAutoReserve(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            //过滤出未下推过合同的意向单
            var soDatas = dataEntities.Where(x => string.IsNullOrWhiteSpace(Convert.ToString(x["forderid"]))).ToArray();

            var result = ReserveUtil.UpdateReserve(this.Context, this.HtmlForm, soDatas, this.Option);

            this.Result.MergeResult(result);
        }

        /// <summary>
        /// 如果销售意向没有商机，则创建商机
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void CreateCustomerRecordIfSaleIntentionNoCustomerRecord(DynamicObject[] dataEntitys)
        {
            // 判断意向单是否源自商机，如果没有，则创建
            // 增加判断，如果是已在意向，不再创建
            var notCustomerRecordObjs =
                dataEntitys.Where(s => s.DataEntityState.FromDatabase == false && Convert.ToString(s["fsourcetype"]).IsNullOrEmptyOrWhiteSpace());

            if (notCustomerRecordObjs.Any() == false) return;

            var customerRecordForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customerrecord");
            var customerRecordDt = customerRecordForm.GetDynamicObjectType(this.Context);
            var customerRecordDutyEntryDt = customerRecordForm.GetEntryEntity("fdutyentry").DynamicObjectType;


            List<DynamicObject> customerRecordObjs = new List<DynamicObject>();

            Dictionary<DynamicObject, DynamicObject> customerRecord2SaleIntention =
                new Dictionary<DynamicObject, DynamicObject>();

            foreach (var saleIntentionObj in notCustomerRecordObjs)
            {
                var customerRecordObj = new DynamicObject(customerRecordDt);

                string customerId = Convert.ToString(saleIntentionObj["fcustomerid"]);
                var customerObj = this.Context.LoadBizDataById("ydj_customer", customerId);

                string customerType = Convert.ToString(customerObj["ftype"]);
                string customerRecordType = string.Empty;

                switch (customerType.ToLowerInvariant())
                {
                    // 公司客户
                    case "customertype_01":
                        customerRecordType = "1";
                        break;
                    // 个人客户
                    default:
                        customerRecordType = "2";
                        break;
                }

                // 来自意向单的字段
                customerRecordObj["fdescription"] = saleIntentionObj["fdescription"];
                customerRecordObj["fgoshopdate"] = DateTime.Today;
                customerRecordObj["fexpectdate"] = DateTime.Today;
                customerRecordObj["fcustomerid"] = saleIntentionObj["fcustomerid"];
                customerRecordObj["fcontacts"] = saleIntentionObj["flinkstaffid"];
                customerRecordObj["fphone"] = saleIntentionObj["fphone_e"];
                customerRecordObj["fprovince"] = saleIntentionObj["fprovince"];
                customerRecordObj["fcity"] = saleIntentionObj["fcity"];
                customerRecordObj["fregion"] = saleIntentionObj["fregion"];
                customerRecordObj["faddress"] = saleIntentionObj["faddress_e"];
                customerRecordObj["fbuildingid"] = saleIntentionObj["fbuildingid"];
                customerRecordObj["fdeptid"] = saleIntentionObj["fdeptid"];
                customerRecordObj["fdutyid"] = saleIntentionObj["fstaffid"];

                customerRecordObj["fcountry"] = "CN";
                customerRecordObj["fphase"] = "customerrecord_phase_02";

                // 来自客户的字段
                customerRecordObj["ftype"] = customerRecordType;
                customerRecordObj["fwechat"] = customerObj["fwechat"];
                customerRecordObj["fchannelid"] = customerObj["fchannelid"];
                customerRecordObj["fcustomername"] = customerObj["fname"];
                customerRecordObj["fgender"] = customerObj["fgender"];
                customerRecordObj["fage"] = customerObj["fage"];
                customerRecordObj["fcustomersource"] = customerObj["fsource"];

                #region 销售员明细

                DynamicObjectCollection saleintentionDutyEntry = (DynamicObjectCollection)saleIntentionObj["fdutyentry"];
                DynamicObjectCollection customerRecordDutyEntry = (DynamicObjectCollection)customerRecordObj["fdutyentry"];

                if (saleintentionDutyEntry.Count == 0)
                {
                    var dutyObj = new DynamicObject(customerRecordDutyEntryDt);
                    dutyObj["fismain"] = true;
                    dutyObj["fdutyid"] = saleIntentionObj["fstaffid"];
                    customerRecordDutyEntry.Add(dutyObj);
                }
                else
                {
                    foreach (var duty in saleintentionDutyEntry)
                    {
                        var dutyObj = new DynamicObject(customerRecordDutyEntryDt);
                        dutyObj["fismain"] = duty["fismain"];
                        dutyObj["fdutyid"] = duty["fdutyid"];
                        dutyObj["fdescription"] = duty["fdescription"];
                        customerRecordDutyEntry.Add(dutyObj);
                    }
                }

                #endregion

                customerRecordObjs.Add(customerRecordObj);

                // 添加关联
                customerRecord2SaleIntention.Add(customerRecordObj, saleIntentionObj);
            }

            var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_customerrecord", customerRecordObjs, "save", new Dictionary<string, object>());
            result.ThrowIfHasError(true, "自动创建商机失败！");

            // 关联源单
            foreach (var customerRecordObj in customerRecordObjs)
            {
                customerRecord2SaleIntention[customerRecordObj]["fsourcetype"] = "ydj_customerrecord";
                customerRecord2SaleIntention[customerRecordObj]["fsourcenumber"] = customerRecordObj["fbillno"];
            }
        }

        /// <summary>
        /// 更新客户
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void UpdateCustomer(DynamicObject[] dataEntitys)
        {
            var customerIds = dataEntitys?.Where(o => Convert.ToString(o["fcustomerid"]).IsNullOrEmptyOrWhiteSpace() == false).Select(o => Convert.ToString(o["fcustomerid"]))?.Distinct();
            if (customerIds == null || !customerIds.Any()) return;

            var customers = this.Context.LoadBizDataById("ydj_customer", customerIds);
            if (customers == null && !customers.Any()) return;

            List<DynamicObject> saveEntitys = new List<DynamicObject>();
            foreach (var item in customers)
            {
                string cusnature = Convert.ToString(item["fcusnature"]);
                if (cusnature == "cusnature_00")
                {
                    // 更新客户性质为：意向
                    item["fcusnature"] = "cusnature_01";

                    saveEntitys.Add(item);
                }
            }

            if (saveEntitys.Any() == false) return;

            var intentionFrom = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, intentionFrom.GetDynamicObjectType(this.Context));
            dm.Save(saveEntitys);
        }

        /// <summary>
        /// 处理销售机会
        /// 1. 添加跟进记录
        /// </summary>
        /// <param name="dataEntities"></param>
        private void HandleCustomerRecord(DynamicObject[] dataEntities)
        {
            var newDataEntities = dataEntities?.Where(s => s.DataEntityState.FromDatabase == false)?.ToArray();

            // 获取关联商机
            var customerRecordObjs = GetCustomerRecords(newDataEntities);
            if (customerRecordObjs.Any() == false) return; ;

            var followerRecordFormDt = this.MetaModelService.LoadFormModel(this.Context, "ydj_followerrecord").GetDynamicObjectType(this.Context);

            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            string deptId = baseFormProvider.GetMyDepartment(this.Context)?.Id;
            string staffId = baseFormProvider.GetMyStaff(this.Context)?.Id;

            List<DynamicObject> followerRecordObjs = new List<DynamicObject>();

            foreach (var item in customerRecordObjs)
            {
                string customerRecordNo = Convert.ToString(item["fbillno"]);

                var saleIntentionObj = newDataEntities.First(s => Convert.ToString(s["fsourcetype"]).EqualsIgnoreCase("ydj_customerrecord") && Convert.ToString(s["fsourcenumber"]).EqualsIgnoreCase(customerRecordNo));

                #region 添加跟进记录
                var followerRecordObj = new DynamicObject(followerRecordFormDt);

                // 客户、联系人、手机号由意向单获取
                followerRecordObj["fcustomerid"] = saleIntentionObj["fcustomerid"];
                followerRecordObj["fcontacts"] = saleIntentionObj["flinkstaffid"];
                followerRecordObj["fphone"] = saleIntentionObj["fphone_e"];


                followerRecordObj["ffollowtime"] = DateTime.Now;
                followerRecordObj["ffollowerid"] = this.Context.UserId;
                followerRecordObj["fdeptid"] = deptId;
                followerRecordObj["fstaffid"] = staffId;
                followerRecordObj["ftype"] = "6";       // 默认是其他
                followerRecordObj["fdescription"] = $"已签意向单，订单金额￥{saleIntentionObj["ffbillamount"]}";
                followerRecordObj["fobjecttype"] = "objecttype04";  // 意向报价  
                //followerRecordObj["ftranid"] = item["ftranid"];

                // 关联到商机
                followerRecordObj["frelatedbilltype"] = "ydj_customerrecord";
                followerRecordObj["frelatedbillno"] = customerRecordNo;

                // 操作对象是意向单
                followerRecordObj["fobjectid"] = saleIntentionObj["id"];
                followerRecordObj["fobjectno"] = saleIntentionObj["fbillno"];

                followerRecordObjs.Add(followerRecordObj);
                #endregion
            }

            var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_followerrecord", followerRecordObjs, "save", new Dictionary<string, object>());
            result.ThrowIfHasError(true, "添加跟进记录失败！");
        }

        private List<DynamicObject> GetCustomerRecords(DynamicObject[] dataEntitys)
        {
            // 判断源单类型
            var customerRecordNos = dataEntitys?.Where(s => Convert.ToString(s["fsourcetype"]).EqualsIgnoreCase("ydj_customerrecord")).Select(s => Convert.ToString(s["fsourcenumber"]));

            if (customerRecordNos == null || customerRecordNos.Any() == false)
            {
                return new List<DynamicObject>();
            }

            return this.Context.LoadBizDataByNo("ydj_customerrecord", "fbillno", customerRecordNos);
        }
    }
}