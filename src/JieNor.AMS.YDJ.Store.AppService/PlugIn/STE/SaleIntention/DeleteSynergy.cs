using System;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 销售订单：删除采购订单提交过来的协同销售订单
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("DeleteSynergy")]
    public class DeleteSynergy : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            //交易流水号
            string tranId = this.GetQueryOrSimpleParam<string>("tranId");
            if (tranId.IsNullOrEmptyOrWhiteSpace()) return;
            
            //销售订单模型
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            string where = $"fmainorgid=@fmainorgid and ftranid=@ftranid";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("ftranid", System.Data.DbType.String, tranId)
            };
            var dataReader = this.Context.GetPkIdDataReader(this.HtmlForm, where, sqlParam);
            var newEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();

            if (newEntity != null && Convert.ToString(newEntity["fbizstatus"]).EqualsIgnoreCase("business_status_02"))
            {
                //删除
                dm.Delete(newEntity["id"]);

                this.Result.IsSuccess = true;
                this.Result.SimpleMessage = "取消成功！";
            }
        }
    }
}