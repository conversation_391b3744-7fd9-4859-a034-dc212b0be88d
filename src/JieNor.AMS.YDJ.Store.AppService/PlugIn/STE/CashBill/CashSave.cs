using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.YDJService
{
    /// <summary>
    /// 收银单--保存
    /// </summary>
    [InjectService]
    [FormId("ydj_cash")]
    [OperationNo("save")]
    public class CashSave : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fcustomerid"]).NotEmpty().WithMessage("客户不能为空！"));

            string strValidMsg = "";

            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                return ValiAomunt(n, out strValidMsg);
            }).WithMessage("{0}", (billData, pData) => strValidMsg));

            e.Rules.Add(this.RuleFor("fbillhead", d => (decimal)d["fsum"]).GreaterThan(0).WithMessage("单据头“总额”字段不允许小于等于0"));
        }

        private bool ValiAomunt(DynamicObject newData, out string strValidMsg)
        {
            strValidMsg = "";
            bool result = true;

            string fszlx = (newData["fszlx"] ?? "").ToString();//收支类型（settle_direction_001：收    settle_direction_002：退）
            string fusage = (newData["fusage"] ?? "").ToString();//支付用途（usage_type_01：定金    usage_type_02：货款）

            if (fszlx.EqualsIgnoreCase("settle_direction_001")
                && fusage.EqualsIgnoreCase("usage_type_02"))
            {
                decimal settleAmount = Convert.ToDecimal(newData["fsum"]);
                var srcEntryObjs = newData["FLinkEntry"] as DynamicObjectCollection;

                //如果是来源单只有一个订单时，则自动将金额调平
                if (srcEntryObjs.Count == 1)
                {
                    srcEntryObjs[0]["FQty"] = settleAmount;
                }

                decimal entrySettleAmount = srcEntryObjs?
                    .Select(o => Convert.ToDecimal(o["FQty"]))
                    .Sum() ?? 0;

                if (settleAmount != entrySettleAmount
                    && settleAmount > 0)
                {
                    strValidMsg = "结算货款时，结算金额必须与订单上累计结算金额相等！";
                    return false;
                }
            }

            if (fszlx.EqualsIgnoreCase("settle_direction_002")
                && fusage.EqualsIgnoreCase("usage_type_02"))
            {
                decimal settleAmount = Convert.ToDecimal(newData["fsum"]);
                decimal entrySettleAmount = (newData["FLinkEntry"] as DynamicObjectCollection)?
                    .Select(o => Convert.ToDecimal(o["freceivedamount"]))
                    .Sum() ?? 0;

                if (settleAmount > entrySettleAmount
                    && settleAmount > 0)
                {
                    strValidMsg = "退货款时，退款金额必须小于等于订单上累计已收金额！";
                    return false;
                }
            }

            string customerId = (newData["fcustomerid"] ?? "").ToString();//客户ID
            decimal cash = Convert.ToDecimal(newData["fmoney"] ?? 0);//本次待退金额

            //通过客户ID查询储值金额
            if (fszlx.EqualsIgnoreCase("settle_direction_002")
                && fusage.EqualsIgnoreCase("usage_type_01"))
            {
                string sql = @"select fbalance from t_ydj_customer where fid = @customerId";
                List<SqlParam> ss = new List<SqlParam> {
                    new SqlParam ("@customerId", System.Data.DbType.String, customerId)
                };
                decimal customerCash = 0;
                DynamicObjectCollection dd = this.DBService.ExecuteDynamicObject(this.Context, sql, ss);
                if (dd == null || dd.Count() == 0) { return result; }
                customerCash = Convert.ToDecimal(dd[0]["fbalance"] ?? 0);
                if (customerCash < cash)
                {
                    strValidMsg = "退定金时，退款金额不得超过对应客户定金账户余额！";
                    return false;
                }
            }
            return true;
        }


        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            //if (e.DataEntitys == null) return;

            //var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_customer");
            //var dm = this.Container.GetService<IDataManager>();
            //foreach (var dataEntity in e.DataEntitys)
            //{
            //    //判断当前交易方式
            //    var tranType = dataEntity["fusage"] as string;
            //    if (tranType != "usage_type_01") continue;

            //    var isReturned = Convert.ToString(dataEntity["fszlx"]).EqualsIgnoreCase("settle_direction_002");

            //    var customerId = dataEntity["fcustomerid"] as string;
            //    dm.DataEntityType = formMeta.GetDynamicObjectType(this.Context);

            //    var customerObj = dm.Select(customerId) as DynamicObject;

            //    //得到本次交易金额的差量
            //    var dataEntitySnapShot = dataEntity.GetDataEntitySnapshot();
            //    DataEntityPropertySnapshot depositSnapshot = null;
            //    decimal dDiffDeposit = (decimal)dataEntity["fsum"];
            //    if (dataEntitySnapShot?.TryGetValue("fsum", out depositSnapshot) == true)
            //    {
            //        dDiffDeposit = dDiffDeposit - (decimal)depositSnapshot.InitialValue;
            //    }
            //    //注意退定的支持
            //    customerObj["fbalance"] = (decimal)customerObj["fbalance"] + dDiffDeposit * (isReturned ? -1 : 1);
            //    dm.Save(customerObj);

            //}
        }
    }
}
