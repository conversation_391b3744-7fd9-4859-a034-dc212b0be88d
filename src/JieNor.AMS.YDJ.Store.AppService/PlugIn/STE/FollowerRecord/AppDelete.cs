using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.FollowerRecord
{
    /// <summary>
    /// 跟进记录：app删除
    /// </summary>
    [InjectService]
    [FormId("ydj_followerrecord")]
    [OperationNo("appdelete")]
    public class AppDelete : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            string fid = this.GetQueryOrSimpleParam<string>("fid");
            if (fid.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add("参数不能为空！");
                return;
            }
            var dynObj = this.Context.LoadBizDataById("ydj_followerrecord", fid);
            if (dynObj.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add("跟进记录记录不存在！");
                return;
            }
            List<DynamicObject> dynObjs = new List<DynamicObject>();
            dynObjs.Add(dynObj);
            var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_followerrecord", dynObjs, "delete", null);
            this.Result.MergeResult(result);
        }
    }
}
