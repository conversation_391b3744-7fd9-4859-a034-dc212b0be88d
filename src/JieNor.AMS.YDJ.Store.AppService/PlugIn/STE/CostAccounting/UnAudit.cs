using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.IoC;
using JieNor.Framework.Enums;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CostAccounting
{
    [InjectService]
    [FormId("ydj_costaccounting")]
    [OperationNo("UnAudit")]
    public class UnAudit: AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            e.DataEntitys = checkStoreStatement(e.DataEntitys);

            //检索有关于销售合同的成本核算单
            DynamicObject[] costEntities = e.DataEntitys?.Where(x => Convert.ToString(x["fsourcetype"]) == "ydj_order").ToArray();
            if (costEntities == null || costEntities.Length <= 0)
            {
                return;
            }


            var htmlForm = this.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_order");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            //获取源单编号，即销售合同的fbillno
            List<string> fsourcenumbers = costEntities.Select(x => Convert.ToString(x["fsourcenumber"])).ToList();
            //根据fbillno从数据库查询出销售合同
            StringBuilder where = new StringBuilder();
            var sqlParam = new List<SqlParam>();
            //根据fsourcenumbers的个数调整sql语句的查询条件
            if (fsourcenumbers.Count == 1)
            {
                where.Append("fbillno=@fbillno");
                sqlParam.Add(new SqlParam("fbillno", System.Data.DbType.String, fsourcenumbers[0]));
            }
            else
            {
                where.Append(" and fbillno in (");
                for (int i = 0; i < fsourcenumbers.Count; i++)
                {
                    where.Append(i == 0 ? $"@fbillno{i}" : $",@fbillno{i}");
                    sqlParam.Add(new SqlParam($"fbillno{i}", System.Data.DbType.String, fsourcenumbers[i]));
                }
                where.Append(")");
            }
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where.ToString(), sqlParam);
            var orderEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
            //没有检索到销售合同则抛出异常
            if (orderEntities == null || orderEntities.Count <= 0)
            {
                throw new BusinessException("没有找到相关销售合同!");
            }

            //去掉自备料
            foreach(var orderEntity in orderEntities)
            {
                var fentries = orderEntity["fentry"] as DynamicObjectCollection;
                var removeItems = new List<DynamicObject>();

                var costEntity = costEntities.FirstOrDefault(x => Convert.ToString(x["fsourcenumber"]) == Convert.ToString(orderEntity["fbillno"]));

                dealInnerCustomerAccountInfo(orderEntity, costEntity, htmlForm);

                foreach (var fentry in fentries)
                {
                    if (Convert.ToBoolean(fentry["fisself"]))
                    {
                        removeItems.Add(fentry);
                    }
                }

                foreach(var removeItem in removeItems)
                {
                    fentries.Remove(removeItem);
                }
            }

            dm.Save(orderEntities);
        }

        /// <summary>
        /// 检查成本核算单是否加入店面结算对账单明细
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private DynamicObject[] checkStoreStatement(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return dataEntities;
            }

            var costIds = dataEntities.Select(x => Convert.ToString(x["id"])).ToList();
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_storestatement");
            var multiValueQuertyService = this.Container.GetService<IMultiValueQueryService>();
            var where = "fmainorgid=@fmainorgid";
            var sqlParams = new List<SqlParam> { new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company) };
            var storeEntities = multiValueQuertyService.Select(this.Context, where, sqlParams, htmlForm, "fcostid", costIds);
                                                        
            if (storeEntities == null || storeEntities.Count <= 0)
            {
                return dataEntities;
            }

            costIds = storeEntities.SelectMany(x => x["fentry"] as DynamicObjectCollection)
                                   .Select(x => Convert.ToString(x["fcostid"]))
                                   .Where(x => false == string.IsNullOrWhiteSpace(x))
                                   .Distinct()
                                   .ToList();

            if (costIds == null || costIds.Count <= 0)
            {
                return dataEntities;
            }

            var validEntities = new List<DynamicObject>();
            foreach(var dataEntity in dataEntities)
            {
                var costId = Convert.ToString(dataEntity["id"]);
                if (costIds.Contains(costId))
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"编号[{dataEntity["fbillno"]}]的单据已关联{htmlForm.Caption},不允许反审核!");
                    continue;
                }
                validEntities.Add(dataEntity);
            }
            return validEntities.ToArray();
        }

        /// <summary>
        /// 处理加盟商自动充值确认
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="htmlForm"></param>
        private void dealInnerCustomerAccountInfo(DynamicObject dataEntity, DynamicObject costEntity, HtmlForm htmlForm)
        {
            if (dataEntity == null || costEntity == null || htmlForm == null || htmlForm.Id != "ydj_order")
            {
                return;
            }

            string innerCustomerId = Convert.ToString(dataEntity["finnercustomerid"]);
            var fmoney = Convert.ToDecimal(costEntity["fsumsettleamount"]);
            if (string.IsNullOrWhiteSpace(innerCustomerId) || fmoney <= 0)
            {
                return;
            }

            var operationNo = "Recharge";
            var operationName = "充值";
            var metaModelService = this.Container.GetService<IMetaModelService>();
            var inpForm = metaModelService.LoadFormModel(this.Context, "coo_inpourdialog");
            var inpEntity = inpForm.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;

            inpEntity["fmoney"] = fmoney;
            inpEntity["fusagetype"] = "settleaccount_type_01";
            inpEntity["fdate"] = DateTime.Now;
            inpEntity["fdescription"] = string.Format("本记录是由{0}[{1}]相关的{2}[{3}]审核生成的{4}记录!",
                                                       htmlForm.Caption,
                                                       Convert.ToString(dataEntity[htmlForm.NumberFldKey]),
                                                       this.HtmlForm.Caption,
                                                       Convert.ToString(costEntity[this.HtmlForm.NumberFldKey]),
                                                       operationName);

            var result = this.Gateway.InvokeBillOperation(this.Context, inpForm.Id, new[] { inpEntity }, operationNo, new Dictionary<string, object>
            {
                { "fsourceid",innerCustomerId} ,
                { "fsourceformid","ydj_customer"}
            });
            result?.ThrowIfHasError(true, $"加盟商自动{operationName}失败!");

            var incomeDisburseIds = (result.SrvData as Dictionary<string, object>)?["incomeDisburseIds"] as string[];

            if (incomeDisburseIds == null || incomeDisburseIds.Length <= 0)
            {
                throw new BusinessException($"加盟商自动{operationName}失败!没有获取到返回结果信息!");
            }

            var incomeDisburseForm = metaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, incomeDisburseForm.GetDynamicObjectType(this.Context));
            var incomeDisburses = dm.Select(incomeDisburseIds).OfType<DynamicObject>().ToList();

            if (incomeDisburses == null || incomeDisburses.Count <= 0)
            {
                throw new BusinessException($"加盟商自动{operationName}失败!没有获取到相关的{incomeDisburseForm.Caption}!");
            }

            var sysProfile = this.Container.GetService<ISystemProfile>();
            var incomeConfirmAudit = sysProfile.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "fincomeconfirmaudit", false);

            if (incomeConfirmAudit)
            {
                var submitEntities = incomeDisburses.Where(x => Convert.ToString(x["fstatus"]).EqualsIgnoreCase(BillStatus.B.ToString()) ||
                                                                Convert.ToString(x["fstatus"]).EqualsIgnoreCase(BillStatus.C.ToString()))
                                                    .ToList();
                if (submitEntities != null && submitEntities.Count > 0)
                {
                    result = this.Gateway.InvokeBillOperation(this.Context, incomeDisburseForm.Id, incomeDisburses, "Submit", new Dictionary<string, object>());
                    result?.ThrowIfHasError(true, $"加盟商自动{operationName}提交失败!");
                }
                var auditEntities = incomeDisburses.Where(x => Convert.ToString(x["fstatus"]).EqualsIgnoreCase(BillStatus.D.ToString())).ToList();
                if (auditEntities != null && auditEntities.Count > 0)
                {
                    result = this.Gateway.InvokeBillOperation(this.Context, incomeDisburseForm.Id, incomeDisburses, "audit", new Dictionary<string, object>());
                    result?.ThrowIfHasError(true, $"加盟商自动{operationName}审核失败!");
                }

                incomeDisburses = incomeDisburses.Where(x => Convert.ToString(x["fstatus"]).EqualsIgnoreCase(BillStatus.E.ToString())).ToList();

                if (incomeDisburses == null || incomeDisburses.Count <= 0)
                {
                    throw new BusinessException($"加盟商自动{operationName}失败!启用了收支记录确认前需审核，却没有已审核收支记录!");
                }
            }

            result = this.Gateway.InvokeBillOperation(this.Context, incomeDisburseForm.Id, incomeDisburses, "Confirm", new Dictionary<string, object>());
            result?.ThrowIfHasError(true, $"加盟商自动{operationName}确认失败!");

            var logService = this.Container.GetService<ILogService>();
            //调用服务生操作日志
            logService?.WriteLog(this.Context, new LogEntry()
            {
                BillFormId = this.HtmlForm.Id,
                OpCode = this.OperationNo,
                OpName = this.OperationName,
                BillIds = costEntity["id"] as string,
                Level = Enu_LogLevel.Info.ToString(),
                LogType = Enu_LogType.RecordType_02,
                Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete), //这个很关键，必须用这个值，平台才会帮你写入记录表
                Content = $"加盟商自动{operationName}确认成功!相关的{incomeDisburseForm.Caption}的编号为：{string.Join(",", incomeDisburses.Select(x => Convert.ToString(x[incomeDisburseForm.NumberFldKey])))}",
                Detail = "" //这个可以不传，平台上用这个是为了后续便于分析本次请求中的明细信息，以便定位问题所在。
            });
        }
    }
}
