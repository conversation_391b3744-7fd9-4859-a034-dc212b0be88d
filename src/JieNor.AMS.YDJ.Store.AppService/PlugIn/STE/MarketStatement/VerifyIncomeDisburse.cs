using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.MarketStatement
{
    /// <summary>
    /// 卖场对账单：对账选单
    /// </summary>
    [InjectService]
    [FormId("ydj_marketstatement")]
    [OperationNo("verifyincomedisburse")]
    public class VerifyIncomeDisburse : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            this.Result.IsSuccess = false;
            base.BeforeExecuteOperationTransaction(e);

            var incomeIdString = this.GetQueryOrSimpleParam<string>("incomeIds");
            var incomeIds = incomeIdString?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (incomeIds == null || incomeIds.Length <= 0)
            {
                return;
            }

            var incomeForm = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var incomeDm = this.Container.GetService<IDataManager>();
            incomeDm.InitDbContext(this.Context, incomeForm.GetDynamicObjectType(this.Context));
            var incomeEntities = incomeDm.Select(incomeIds).OfType<DynamicObject>().ToArray();
            if (incomeEntities == null || incomeEntities.Length <= 0)
            {
                return;
            }

            var loadReferenceObjectManager = this.Container.GetService<LoadReferenceObjectManager>();
            loadReferenceObjectManager.Load(this.Context, incomeEntities, false, incomeForm, new List<string> { "fdeptid", "fpurpose", "fdirection" });

            var sourceGroups = incomeEntities.Select(x => new
            {
                sourceFormId = Convert.ToString(x["fsourceformid"]),
                sourceId = Convert.ToString(x["fsourceid"])
            })
            .Where(x => false == string.IsNullOrWhiteSpace(x.sourceFormId))
            .Distinct(x => string.Concat(x.sourceFormId, "|", x.sourceId))
            .GroupBy(x => x.sourceFormId)
            .ToList();

            var sourceMaps = new Dictionary<string, DynamicObject[]>();
            foreach(var sourceGroup in sourceGroups)
            {
                var sourceFormId = sourceGroup.Key;
                var sourceForm = this.MetaModelService.LoadFormModel(this.Context, sourceFormId);
                var sourceDm = this.Container.GetService<IDataManager>();
                sourceDm.InitDbContext(this.Context, sourceForm.GetDynamicObjectType(this.Context));
                var sourceEntities = sourceDm.Select(sourceGroup.Select(x => x.sourceId)).OfType<DynamicObject>().ToArray();
                if (sourceEntities != null && sourceEntities.Length > 0)
                {
                    sourceMaps.Add(sourceFormId, sourceEntities);
                }
            }

            var results = new List<Dictionary<string,object>>();
            var uiConvert = this.Container.GetService<IUiDataConverter>();
            var fincomeid = this.HtmlForm.GetField("fincomeid");
            var fentry = this.HtmlForm.GetEntryEntity("fentry");
            var fpurpose = incomeForm.GetField("fpurpose");
            var fdirection = incomeForm.GetField("fdirection");

            foreach (var incomeEntity in incomeEntities)
            {
                var item = new Dictionary<string, object>();
                var dept = incomeEntity["fdeptid_ref"] as DynamicObject;
                var sourceFormId = Convert.ToString(incomeEntity["fsourceformid"]);
                var sourceId = Convert.ToString(incomeEntity["fsourceid"]);
                if (sourceMaps.Any() && sourceMaps.ContainsKey(sourceFormId))
                {
                    var sourceEntities = sourceMaps[sourceFormId];
                    var sourceEntity = sourceEntities?.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(sourceId));
                    if (sourceEntity != null)
                    {
                        item["fmallorderno"] = sourceEntity["fmallorderno"];
                    }
                }
                var entry = fentry.DynamicObjectType.CreateInstance() as DynamicObject;
                entry[fincomeid.Id] = incomeEntity["id"];
                entry[$"{fincomeid.Id}_ref"] = incomeEntity;

                item[fincomeid.Id] = uiConvert.PackageFieldData(this.Context, this.HtmlForm, fincomeid, entry, null, false);
                item[fpurpose.Id] = uiConvert.PackageFieldData(this.Context, incomeForm, fpurpose, incomeEntity, null, false);
                item[fdirection.Id] = uiConvert.PackageFieldData(this.Context, incomeForm, fpurpose, incomeEntity, null, false);
                if (dept != null)
                {
                    item["fdeductrate"] = dept["fdeductrate"];
                    item["fdeptid"] = dept["Id"];
                    item["fdeptid_ref"] = dept;
                }

                item["fsappostno"] = incomeEntity["fsappostno"];

                results.Add(item);
            }

            //控制是否显示提示消息
            var sender = this.GetQueryOrSimpleParam<string>("sender");
            if (sender != null && sender == "initForm")
            {
                this.Result.IsShowMessage = false;
            }

            this.Result.IsSuccess = true;
            this.Result.SrvData = results;
        }
    }
}
