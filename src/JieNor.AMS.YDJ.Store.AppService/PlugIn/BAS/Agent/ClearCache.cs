using JieNor.AMS.YDJ.Core;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Agent
{
    /// <summary>
    /// 经销商：清商品授权缓存
    /// </summary>
    [InjectService]
    [FormId("bas_agent")]
    [OperationNo("ClearCache")]
    public class ClearCache : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }

            //清除缓存 
            var clearOrgIds = e.DataEntitys.Select(s => Convert.ToString(s["id"])).ToList();
            ProductDataIsolateHelper.ClearOrgCache(this.Context, clearOrgIds);
        }
    }


}