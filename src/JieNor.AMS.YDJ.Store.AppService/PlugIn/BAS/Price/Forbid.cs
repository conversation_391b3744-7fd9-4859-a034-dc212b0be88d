using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Price
{
    /// <summary>
    /// 商品价目：禁用
    /// </summary>
    [InjectService]
    [FormId("ydj_price|ydj_selfprice|ydj_reprice|ydj_dealprice")]
    [OperationNo("Forbid")]
    public class Forbid : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(new ValidationOrgOperation("禁用"));
        }



        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            var priceService = Container.GetService<IPriceService>();
            priceService.ClearPriceCache(this.Context, e.DataEntitys, "fproductid");
        }
    }
}
