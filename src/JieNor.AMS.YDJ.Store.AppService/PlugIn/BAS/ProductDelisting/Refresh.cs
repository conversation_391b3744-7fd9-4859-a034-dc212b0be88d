using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.ProductDelisting
{
    /// <summary>
    /// 退市清单：刷新明细
    /// </summary>
    [InjectService]
    [FormId("ydj_productdelisting")]
    [OperationNo("Refresh")]
    public class Refresh : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "afterCreateUIData":
                    this.afterCreateUIData(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 来源单据打包后事件：可对打包后的数据包进行处理或者直接覆盖整个数据包都是可以的
        /// </summary>
        /// <param name="e"></param>
        private void afterCreateUIData(OnCustomServiceEventArgs e)
        {
            var uiData = e.EventData as JObject;
            if (uiData == null) uiData = new JObject();

            var entry = uiData["fentity"] as JArray;
            if (entry == null) entry = new JArray();

            //重新排序
            var sortEntry = entry.OrderByDescending(a => Convert.ToDateTime(a["fhqpushtime"])).ToList();
            var sortArray = new JArray();
            for (int i = 0; i < sortEntry.Count; i++)
            {
                sortEntry[i]["FSeq"] = i + 1;
                sortArray.Add(sortEntry[i]);
            }
            uiData["fentity"] = sortArray;
            e.EventData = uiData;
        }
    }
}
