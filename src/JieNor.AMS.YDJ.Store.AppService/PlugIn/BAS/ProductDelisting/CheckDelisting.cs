using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.ProductDelisting
{
    /// <summary>
    /// 检查退市商品
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder|ydj_order")]
    [OperationNo("CheckDelisting")]
    public class CheckDelisting : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 初始化服务插件时触发的事件
        /// </summary>
        /// <param name="e"></param>
        protected override void InitializeServicePlugIn(OperationContext ctx)
        {
            base.InitializeServicePlugIn(ctx);

            // 此处忽略操作日志（无意义），在转让确认成功时再手工记录操作日志
            ctx.Option.SetIgnoreOpLogFlag();
        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            this.Result.IsSuccess = true;
            var dataEntitys = e.DataEntitys;
            if ((dataEntitys == null || !dataEntitys.Any()) || this.GetQueryOrSimpleParam<bool>("isMiniProgram", false))
            {
                var ids = this.GetQueryOrSimpleParam<string>("ids");
                if (ids.IsNullOrEmptyOrWhiteSpace()) return;

                var allPkIds = ids.Split(',');
                dataEntitys = this.Context.LoadBizDataById(this.HtmlForm.Id, allPkIds).ToArray();
                if (dataEntitys == null || !dataEntitys.Any())
                {
                    return;
                }
            }
            if (this.HtmlForm.Id.EqualsIgnoreCase("ydj_purchaseorder"))
            {
                dataEntitys = dataEntitys.Where(a => Convert.ToInt32(a["fchangestatus"]) == 0).ToArray();
            }
            var domainType = dataEntitys.Count() > 1 ? "list" : "bill";
            var productFieldId = "fmaterialid";
            var entryId = "fentity";
            var currentAgent = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "freminddelistling,fisreseller");
            if (this.HtmlForm.Id.EqualsIgnoreCase("ydj_order"))
            {
                //销售合同需要判断《经销商》档案上的参数
                if (!Convert.ToString(currentAgent?["freminddelistling"]).EqualsIgnoreCase("1"))
                {
                    return;
                }
                productFieldId = "fproductid";
                entryId = "fentry";
            }
            //先加载所有引用数据
            var refManager = this.Container.GetService<LoadReferenceObjectManager>();
            refManager.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), dataEntitys, true);

            //查询所有需要判断的商品档案
            var entryEntities = dataEntitys.SelectMany(t => t[entryId] as DynamicObjectCollection).ToList();
            if (this.HtmlForm.Id.EqualsIgnoreCase("ydj_order"))
            {
                //销售合同需要判断是否出现货
                entryEntities = entryEntities.Where(t => !Convert.ToBoolean(t["fisoutspot"])).ToList();

                var viewAllEntryId = this.GetQueryOrSimpleParam<string>("allEntryId");
                if (string.IsNullOrWhiteSpace(viewAllEntryId)) return;
                var allEntryIds = JsonConvert.DeserializeObject<List<string>>(viewAllEntryId);
                var newEntrys = entryEntities.Where(t => !allEntryIds.Contains(Convert.ToString(t["id"]))).ToList();//新增的行

                List<string> changedRowIds = new List<string>();
                var changedRow = this.GetQueryOrSimpleParam<string>("changedRow");
                if (string.IsNullOrWhiteSpace(changedRow))
                {
                    return;
                }
                var rows = JArray.Parse(changedRow);
                foreach (var row in rows)
                {
                    changedRowIds.Add(row["row"].ToString());
                }

                if (!changedRowIds.FirstOrDefault().EqualsIgnoreCase("all"))
                {
                    entryEntities = entryEntities.Where(t => changedRowIds.Contains(Convert.ToString(t["id"]))).ToList();
                }

                entryEntities.AddRange(newEntrys.Where(t => !entryEntities.Contains(t)));
            }
            var allProductObj = entryEntities.Select(t => t[$"{productFieldId}_ref"] as DynamicObject)
                                .Where(t => Convert.ToBoolean(t["fdelisting"])).ToList();
            if (allProductObj == null || !allProductObj.Any())
            {
                return;
            }

            var topCtx = this.Context.CreateTopOrgDBContext();
            var dbSvc = topCtx.Container.GetService<IDBService>();
            //var mateModelService = topCtx.Container.GetService<IMetaModelService>();
            //var delistForm = mateModelService.LoadFormModel(topCtx, delistFormId);

            var allProductIds = allProductObj.Select(t => Convert.ToString(t["id"])).ToList();
            var allSelTypeIds = allProductObj.Where(t => !Convert.ToString(t["fseltypeid"]).IsNullOrEmptyOrWhiteSpace()).Select(d => Convert.ToString(d["fseltypeid"])).ToList();

            //使用商品内码查询所有匹配的退市清单
            var productMatch = ProductDelistingHelper.GetDelistingProductIdList(topCtx, "fmaterialid", allProductIds);
            //使用型号内码查询所有匹配的退市清单
            var selTypeMatch = ProductDelistingHelper.GetDelistingProductIdList(topCtx, "fseltypeid", allSelTypeIds);

            //加载所有匹配到的退市清单
            var allMatchIds = new List<string>();
            if (productMatch != null && productMatch.Any()) allMatchIds.AddRange(productMatch);
            if (selTypeMatch != null && selTypeMatch.Any()) allMatchIds.AddRange(selTypeMatch);

            var allDelist = topCtx.LoadBizDataById("ydj_productdelisting", allMatchIds, true);
            //对需要判断的明细行逐一判断
            var allValidateEntrys = entryEntities.Where(t => allProductIds.Contains(Convert.ToString(t[productFieldId]))).ToList();
            if (this.HtmlForm.Id.EqualsIgnoreCase("ydj_order"))
            {
                allValidateEntrys = allValidateEntrys.OrderBy(a => Convert.ToString((a["fproductid_ref"] as DynamicObject)["fnumber"])).ToList();
            }
            else
                allValidateEntrys = allValidateEntrys.OrderBy(a => Convert.ToString((a["fmaterialid_ref"] as DynamicObject)["fnumber"])).ToList();
            //取出辅助属性明细
            Dictionary<string, List<Tuple<string, string>>> dicAttrInfo = new Dictionary<string, List<Tuple<string, string>>>();
            var allAttrIds = allValidateEntrys.Where(t => !Convert.ToString(t["fattrinfo_e"]).IsNullOrEmptyOrWhiteSpace()).Select(d => Convert.ToString(d["fattrinfo_e"])).Distinct().ToList();
            if (allAttrIds != null && allAttrIds.Any())
            {
                dicAttrInfo = ProductDelistingHelper.GetAttrInfo(topCtx, allAttrIds);
            }

            Dictionary<string, List<string>> dicPkIds = new Dictionary<string, List<string>>();
            foreach (var data in dataEntitys)
            {
                var entryIds = (data[entryId] as DynamicObjectCollection).Select(t => Convert.ToString(t["id"])).ToList();
                var headId = Convert.ToString(data["id"]);
                dicPkIds.Add(headId, entryIds);
            }
            var linkProductDelistings = ProductDelistingHelper.GetProductDelistingBySpecs(topCtx, allDelist).ToList();
            List<ValidModel> validModels = new List<ValidModel>();
            List<ErrDelistModel> errObjs = new List<ErrDelistModel>();
            foreach (var entry in allValidateEntrys)
            {
                var currProductObj = entry[$"{productFieldId}_ref"] as DynamicObject;
                var currProductId = Convert.ToString(currProductObj["id"]);
                var currSelTypeId = Convert.ToString(currProductObj["fseltypeid"]);
                var currAttrInfoId = Convert.ToString(entry["fattrinfo_e"]);

                var currEntryId = Convert.ToString(entry["id"]);
                var currFid = dicPkIds.FirstOrDefault(t => t.Value.Contains(currEntryId)).Key;
                var currOrderNo = Convert.ToString(dataEntitys.FirstOrDefault(t => Convert.ToString(t["id"]).EqualsIgnoreCase(currFid))["fbillno"]);
                var entrySeq = Convert.ToString(entry["FSeq"]);
                var currQty = Convert.ToDecimal(entry["fbizqty"]);//采购数量


                //49683 【慕思UAT-退市产品提醒需求】按【型号】退市，销售合同调整销售数量后：①调整无关的标准品触发了更新逻辑。②调整对应定制品，未触发更新逻辑（点<初始数据更新重试>无问题）
                //判断商品是否标准品
                if (!Convert.ToBoolean(currProductObj["fispresetprop"])//允许选配
                    && !Convert.ToBoolean(currProductObj["fcustom"])//允许定制
                    && !Convert.ToBoolean(currProductObj["funstdtype"]))//非标产品
                {
                    //标品用商品判断，用类型判断会导致放大范围
                    var matchDelist = allDelist.Where(t =>
                    !Convert.ToString(t["fmaterialid"]).IsNullOrEmptyOrWhiteSpace()
                    && Convert.ToString(t["fmaterialid"]).EqualsIgnoreCase(currProductId)
                    && !Convert.ToBoolean(t["ftype"])
                    ).ToList();
                    if ((matchDelist == null || !matchDelist.Any()) && !currSelTypeId.IsNullOrEmptyOrWhiteSpace())
                    {
                        //当商品找不到，则用型号找
                        //找的条件：型号相同，退市清单是定制
                        matchDelist = allDelist
                            .Where(a => Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(currProductObj["fseltypeid"]))
                            && Convert.ToBoolean(a["ftype"])).ToList();
                    }
                    if (matchDelist != null && matchDelist.Any())
                    {
                        var delistingobj = matchDelist.FirstOrDefault();
                        var productDelistings = linkProductDelistings.Where(a =>
                             Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistingobj["fspecmaterial"])) &&
                             !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistingobj["fhqid"]))).ToList();
                        var _dynamics1 = linkProductDelistings.Where(a =>
                            Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistingobj["fseltypeid"])) &&
                            !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistingobj["fspecmaterial"])) &&
                            //Convert.ToBoolean(a["ftype"]) &&
                            //!(a["fpropentry"] as DynamicObjectCollection).Any() &&
                            !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistingobj["fhqid"]))).ToList();
                        productDelistings.AddRange(_dynamics1);
                        productDelistings.Add(delistingobj);
                        ////按创建时间排序，取日期最新的一个
                        //var delistObj = matchDelist.OrderByDescending(t => Convert.ToDateTime(t["fcreatedate"])).FirstOrDefault();
                        //var delistEntry = (delistObj["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).OrderByDescending(t => Convert.ToDateTime(t["fhqpushtime"])).FirstOrDefault();
                        //ValidModel validModel = new ValidModel();
                        //validModel.htmlForm = this.HtmlForm.Id;
                        //validModel.delistings = matchDelist;
                        //validModel.currQty = currQty;
                        //validModel.billNo = currOrderNo;
                        //validModel.entrySeq = entrySeq;
                        //validModel.propValueName = "";
                        //validModel.matchDelisting = delistObj;
                        //validModel.entryItem = delistEntry;
                        //validModel.entryId = Convert.ToString(delistEntry["Id"]);
                        //validModels.Add(validModel);
                        if (!Valid(this.HtmlForm.Id, allDelist, productDelistings, delistingobj, errObjs, currOrderNo, entrySeq, currQty)) continue;//如果已经检验不通过了，则无需后面的校验了
                    }
                }
                else
                {
                    //非标商品按照型号+辅助属性匹配判断
                    if (!currSelTypeId.IsNullOrEmptyOrWhiteSpace() && !currAttrInfoId.IsNullOrEmptyOrWhiteSpace())
                    {
                        var matchDelist = allDelist.Where(t => !Convert.ToString(t["fseltypeid"]).IsNullOrEmptyOrWhiteSpace() && Convert.ToString(t["fseltypeid"]).EqualsIgnoreCase(currSelTypeId)).ToList();
                        if (matchDelist != null && matchDelist.Any())
                        {
                            List<Tuple<string, string>> attrDetail = new List<Tuple<string, string>>();
                            //匹配辅助属性
                            dicAttrInfo.TryGetValue(currAttrInfoId, out attrDetail);
                            var matchAttrList = new List<DynamicObject>();
                            var colorName = "";
                            foreach (var match in matchDelist)
                            {
                                var propEntry = match["fpropentry"] as DynamicObjectCollection;
                                if (propEntry == null || !propEntry.Any())
                                {
                                    //定制品，没有设属性，匹配到了也算
                                    matchAttrList.Add(match);
                                    continue;
                                }
                                var matchPropEntry = propEntry.Where(t => attrDetail.Any(d => d.Item2.EqualsIgnoreCase(Convert.ToString(t["fpropvalueid"]))));
                                if (matchPropEntry != null && matchPropEntry.Any())
                                {
                                    var propValueObj = matchPropEntry.FirstOrDefault()["fpropvalueid_ref"] as DynamicObject;
                                    colorName = Convert.ToString(propValueObj["fname"]);
                                    matchAttrList.Add(match);
                                }
                            }
                            var _result = new List<DynamicObject>();
                            foreach (var item in matchAttrList)
                            {
                                var _product = (item["fmaterialid_ref"] as DynamicObject);
                                if (_product != null)
                                {
                                    //只查定制
                                    if (Convert.ToBoolean(_product["fispresetprop"])//允许选配
                                       || Convert.ToBoolean(_product["fcustom"])//允许定制
                                       || Convert.ToBoolean(_product["funstdtype"]))//非标产品
                                    {
                                        _result.Add(item);
                                        continue;
                                    }
                                }
                            }
                            matchDelist = _result;

                            if (matchDelist != null && matchDelist.Any())
                            {
                                //按创建时间排序，取日期最新的一个
                                var delistObj = matchDelist.OrderByDescending(t => Convert.ToDateTime(t["fcreatedate"])).FirstOrDefault();
                                var delistEntry = (delistObj["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).OrderByDescending(t => Convert.ToDateTime(t["fhqpushtime"])).FirstOrDefault();
                                ValidModel validModel = new ValidModel();
                                validModel.htmlForm = this.HtmlForm.Id;
                                validModel.delistings = matchDelist;
                                validModel.currQty = currQty;
                                validModel.billNo = currOrderNo;
                                validModel.entrySeq = entrySeq;
                                validModel.propValueName = colorName;
                                validModel.matchDelisting = delistObj;
                                validModel.entryItem = delistEntry;
                                validModel.entryId = Convert.ToString(delistEntry["Id"]);
                                if (!Convert.ToBoolean(delistObj["ftype"]))
                                {
                                    if (Convert.ToDecimal(delistEntry["fsumpurqty"]) >= Convert.ToDecimal(delistEntry["fpurcanplaceorderqty"]))
                                    {
                                        continue;
                                    }
                                }
                                //validModels.Add(validModel);
                                Valid(this.HtmlForm.Id, allDelist, matchDelist, errObjs, currOrderNo, entrySeq, currQty, colorName);
                            }
                        }
                    }
                }
            }
            //List<KeyValuePair<string, decimal>> purcanplaceorderqty = new List<KeyValuePair<string, decimal>>();
            //foreach (var item in validModels)
            //{
            //    //按创建时间排序，取日期最新的一个
            //    //var delistObj = item.delistings.OrderByDescending(t => Convert.ToDateTime(t["fcreatedate"])).FirstOrDefault();
            //    //var delistEntry = (delistObj["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).OrderByDescending(t => Convert.ToDateTime(t["fhqpushtime"])).FirstOrDefault();
            //    //var fpurcanplaceorderqty = Convert.ToDecimal(delistEntry["fpurcanplaceorderqty"]);//采购可下单数量
            //    //purcanplaceorderqty.Add(new KeyValuePair<string, decimal>(Convert.ToString(delistEntry["id"]), fpurcanplaceorderqty));
            //    //var keyItem = purcanplaceorderqty.Where(a => a.Key.Equals(Convert.ToString(delistEntry["id"]))).FirstOrDefault();
            //    //delistEntry["fpurcanplaceorderqty"] = keyItem.Value;
            //    var fEntryItem = validModels.Where(a => a.entryId == item.entryId).FirstOrDefault();

            //    Valid1(item.htmlForm, fEntryItem.matchDelisting, fEntryItem.entryItem, errObjs, item.billNo, item.entrySeq, item.currQty, item.propValueName);
            //    fEntryItem.entryItem["fpurcanplaceorderqty"] = Convert.ToDecimal(fEntryItem.entryItem["fpurcanplaceorderqty"]) - item.currQty;
            //}
            if (errObjs.Any())
            {
                this.Result.IsSuccess = false;
                if (this.GetQueryOrSimpleParam<bool>("isMiniProgram", false))
                {
                    //this.Result.SrvData = errObjs;
                    this.Result.SrvData = ShowMiniException(currentAgent, errObjs, domainType);
                }
                else
                    ShowExceptionForm(currentAgent, errObjs, domainType);
            }
        }

        /// <summary>
        /// 定制品校验，定制品只需要扣减自己
        /// </summary>
        /// <param name="formId">来源单据ID</param>
        /// <param name="matchDelist">所有匹配的退市商品清单</param>
        /// <param name="errObjs">用于记录校验不通过的数据信息</param>
        /// <param name="billNo">单据编号</param>
        /// <param name="entrySeq">明细行号</param>
        /// <param name="currQty">本次业务数量</param>
        /// <param name="propValueName">非标商品时的属性值名称</param>
        /// <returns></returns>
        private bool Valid(string formId, List<DynamicObject> allDelist, List<DynamicObject> matchDelist, List<ErrDelistModel> errObjs, string billNo, string entrySeq, decimal currQty, string propValueName = "")
        {
            bool flag = true;
            foreach (var delistObj in matchDelist)
            {
                var delistEntry = (delistObj["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).OrderByDescending(t => Convert.ToDateTime(t["fhqpushtime"])).FirstOrDefault();
                var fsalcanplaceorderqty = Convert.ToDecimal(delistEntry["fsalcanplaceorderqty"]);//销售可下单数量
                var fpurcanplaceorderqty = Convert.ToDecimal(delistEntry["fpurcanplaceorderqty"]);//采购可下单数量
                var fsumpurqty = Convert.ToDecimal(delistEntry["fsumpurqty"]);//可使用型号采购可下单数量
                var fwarnqty = Convert.ToDecimal(delistEntry["fwarnqty"]);//预警数量

                var productObj = delistObj["fmaterialid_ref"] as DynamicObject;
                var selTypeObj = delistObj["fseltypeid_ref"] as DynamicObject;

                var errObj = new ErrDelistModel()
                {
                    BillNo = billNo,
                    BillSeq = entrySeq,
                    ProductNo = productObj == null ? "" : Convert.ToString(productObj["fnumber"]),
                    ProductName = productObj == null ? "" : Convert.ToString(productObj["fname"]),
                    SelType = selTypeObj == null ? "" : Convert.ToString(selTypeObj["fname"]),
                    PropValueName = propValueName,
                    WarnQty = fwarnqty.ToString("N2"),
                    CanSaleQty = fsalcanplaceorderqty.ToString("N2"),
                    PurchaseQty = currQty.ToString("N2"),
                    CanPurchaseQty = fpurcanplaceorderqty.ToString("N2")
                };

                if (formId.EqualsIgnoreCase("ydj_order"))
                {
                    if (fpurcanplaceorderqty <= 0)
                    {
                        //delistEntry["fpurcanplaceorderqty"] = fpurcanplaceorderqty - currQty;
                        errObj.ErrLevel = 3;
                        errObjs.Add(errObj);
                        return flag;
                    }
                    else if (fpurcanplaceorderqty <= fwarnqty)
                    {
                        //delistEntry["fpurcanplaceorderqty"] = fpurcanplaceorderqty - currQty;
                        errObj.ErrLevel = 2;
                        errObjs.Add(errObj);
                        return flag;
                    }
                    //else if (fpurcanplaceorderqty > fwarnqty)
                    //{
                    //    errObj.ErrLevel = 1;
                    //    errObjs.Add(errObj);
                    //    return false;
                    //}
                }
                else
                {
                    if (fpurcanplaceorderqty >= currQty)
                    {
                        var _sumqty = fpurcanplaceorderqty - currQty;
                        delistEntry["fpurcanplaceorderqty"] = _sumqty;

                        //定制的，直接改，标准的，需要先减少可使用型号再加回来可使用型号
                        matchDelist.Where(a => !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"])))
                            .ForEach(_ =>
                            {
                                var allDelistItem = allDelist.Where(c => Convert.ToString(c["fhqid"]).Equals(Convert.ToString(_["fhqid"]))).FirstOrDefault();

                                if (allDelistItem != null)
                                {
                                    (allDelistItem["fentity"] as DynamicObjectCollection).ForEach(a =>
                                    {
                                        if (Convert.ToBoolean(_["ftype"]))
                                        {
                                            //型号
                                            a["fpurcanplaceorderqty"] = _sumqty;
                                        }
                                        else
                                        {
                                            a["fpurcanplaceorderqty"] = Convert.ToDecimal(a["fpurcanplaceorderqty"]) - Convert.ToDecimal(a["fsumpurqty"]) + _sumqty;
                                            a["fsumpurqty"] = _sumqty;
                                        }
                                    });
                                }
                                if (Convert.ToBoolean(_["ftype"]))
                                {
                                    (_["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).ForEach(a =>
                                    {
                                        //型号
                                        a["fpurcanplaceorderqty"] = _sumqty;
                                    });
                                }
                                else
                                {
                                    (_["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).ForEach(a =>
                                    {
                                        a["fpurcanplaceorderqty"] = Convert.ToDecimal(a["fpurcanplaceorderqty"]) - Convert.ToDecimal(a["fsumpurqty"]) + _sumqty;
                                        //型号
                                        a["fsumpurqty"] = _sumqty;
                                    });
                                }
                            });
                        //当前满足下单，直接过
                        return true;
                    }

                    if (fpurcanplaceorderqty <= 0)
                    {
                        //delistEntry["fpurcanplaceorderqty"] = fpurcanplaceorderqty - currQty;
                        errObj.ErrLevel = 3;
                        errObjs.Add(errObj);
                        return flag;
                    }
                    else if (currQty > fpurcanplaceorderqty)
                    {
                        //delistEntry["fpurcanplaceorderqty"] = fpurcanplaceorderqty - currQty;
                        errObj.ErrLevel = 2;
                        errObjs.Add(errObj);
                        return flag;
                    }
                }
                //return flag;
            }
            return flag;
        }


        /// <summary>
        /// 标品校验
        /// </summary>
        /// <param name="formId">来源单据ID</param>
        /// <param name="matchDelist">所有匹配的退市商品清单</param>
        /// <param name="delistObj">当前退市清单数据</param>
        /// <param name="errObjs">用于记录校验不通过的数据信息</param>
        /// <param name="billNo">单据编号</param>
        /// <param name="entrySeq">明细行号</param>
        /// <param name="currQty">本次业务数量</param>
        /// <param name="propValueName">非标商品时的属性值名称</param>
        /// <returns></returns>
        private bool Valid(string formId, List<DynamicObject> allDelist, List<DynamicObject> matchDelist, DynamicObject delistObj, List<ErrDelistModel> errObjs, string billNo, string entrySeq, decimal currQty, string propValueName = "")
        {
            //按创建时间排序，取日期最新的一个
            //var delistObj = matchDelist.OrderByDescending(t => Convert.ToDateTime(t["fcreatedate"])).FirstOrDefault();
            var delistEntry = (delistObj["fentity"] as DynamicObjectCollection)
                .Where(b => Convert.ToBoolean(b["fenable"]))
                .OrderByDescending(t => Convert.ToDateTime(t["fhqpushtime"])).FirstOrDefault();
            var fsalcanplaceorderqty = Convert.ToDecimal(delistEntry["fsalcanplaceorderqty"]);//销售可下单数量
            var fpurcanplaceorderqty = Convert.ToDecimal(delistEntry["fpurcanplaceorderqty"]);//采购可下单数量
            var fsumpurqty = Convert.ToDecimal(delistEntry["fsumpurqty"]) < 0 ? 0 : Convert.ToDecimal(delistEntry["fsumpurqty"]);//可使用型号采购可下单数量
            var fwarnqty = Convert.ToDecimal(delistEntry["fwarnqty"]);//预警数量

            //matchDelist = matchDelist
            //    .Where(a => Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"]))
            //    && Convert.ToBoolean(a["ftype"])).ToList();
            var productObj = delistObj["fmaterialid_ref"] as DynamicObject;
            var selTypeObj = delistObj["fseltypeid_ref"] as DynamicObject;

            var errObj = new ErrDelistModel()
            {
                BillNo = billNo,
                BillSeq = entrySeq,
                ProductNo = productObj == null ? "" : Convert.ToString(productObj["fnumber"]),
                ProductName = productObj == null ? "" : Convert.ToString(productObj["fname"]),
                SelType = selTypeObj == null ? "" : Convert.ToString(selTypeObj["fname"]),
                PropValueName = propValueName,
                WarnQty = fwarnqty.ToString("N2"),
                CanSaleQty = fsalcanplaceorderqty.ToString("N2"),
                PurchaseQty = currQty.ToString("N2"),
                CanPurchaseQty = fpurcanplaceorderqty.ToString("N2")
            };
            //更新关联的退市清单
            //当当前的数量够的时候
            bool flag = true;

            if (formId.EqualsIgnoreCase("ydj_order"))
            {
                if (fpurcanplaceorderqty <= 0)
                {
                    //delistEntry["fpurcanplaceorderqty"] = fpurcanplaceorderqty - currQty;
                    errObj.ErrLevel = 3;
                    errObjs.Add(errObj);
                    flag = false;
                }
                else if (fpurcanplaceorderqty <= fwarnqty)
                {
                    //delistEntry["fpurcanplaceorderqty"] = fpurcanplaceorderqty - currQty;
                    errObj.ErrLevel = 2;
                    errObjs.Add(errObj);
                    flag = false;
                }
            }
            else
            {
                if ((fpurcanplaceorderqty - fsumpurqty) >= currQty)
                {
                    //当前满足下单，直接过
                    return true;
                }
                else if ((fpurcanplaceorderqty - fsumpurqty) > 0 && (fpurcanplaceorderqty - fsumpurqty) < currQty)
                {
                    //当前有余量但是不够
                    var _sumqty = fsumpurqty - (currQty - (fpurcanplaceorderqty - fsumpurqty));
                    delistEntry["fpurcanplaceorderqty"] = _sumqty;
                    delistEntry["fsumpurqty"] = _sumqty;
                    //关联的退市清单，要同步更新
                    matchDelist.Where(a => !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"])))
                        .ForEach(_ =>
                        {
                            var allDelistItem = allDelist.Where(c => Convert.ToString(c["fhqid"]).Equals(Convert.ToString(_["fhqid"]))).FirstOrDefault();

                            if (allDelistItem != null)
                            {
                                (allDelistItem["fentity"] as DynamicObjectCollection).ForEach(a =>
                                {
                                    if (Convert.ToBoolean(_["ftype"]))
                                    {
                                        //型号
                                        a["fpurcanplaceorderqty"] = _sumqty;
                                    }
                                    else
                                    {
                                        a["fsumpurqty"] = _sumqty;
                                        a["fpurcanplaceorderqty"] = Convert.ToDecimal(a["fpurcanplaceorderqty"]) - Convert.ToDecimal(a["fsumpurqty"]) + _sumqty;
                                    }
                                });
                            }
                            if (Convert.ToBoolean(_["ftype"]))
                            {
                                (_["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).ForEach(a =>
                                {
                                    //型号
                                    a["fpurcanplaceorderqty"] = _sumqty;
                                });
                            }
                            else
                            {
                                (_["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).ForEach(a =>
                                {
                                    //型号
                                    a["fsumpurqty"] = _sumqty;
                                    a["fpurcanplaceorderqty"] = Convert.ToDecimal(a["fpurcanplaceorderqty"]) - Convert.ToDecimal(a["fsumpurqty"]) + _sumqty;
                                });
                            }
                        });
                }
                else
                {
                    //当前的全都是占用
                    //当前有余量但是不够
                    var _sumqty = fsumpurqty - currQty;
                    delistEntry["fpurcanplaceorderqty"] = _sumqty;
                    delistEntry["fsumpurqty"] = _sumqty;
                    //关联的退市清单，要同步更新
                    //定制的，直接改，标准的，需要先减少可使用型号再加回来可使用型号
                    matchDelist.Where(a => !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"])))
                        .ForEach(_ =>
                        {
                            var allDelistItem = allDelist.Where(c => Convert.ToString(c["fhqid"]).Equals(Convert.ToString(_["fhqid"]))).FirstOrDefault();

                            if (allDelistItem != null)
                            {
                                (allDelistItem["fentity"] as DynamicObjectCollection).ForEach(a =>
                                {
                                    if (Convert.ToBoolean(_["ftype"]))
                                    {
                                        //型号
                                        a["fpurcanplaceorderqty"] = _sumqty;
                                    }
                                    else
                                    {
                                        a["fpurcanplaceorderqty"] = Convert.ToDecimal(a["fpurcanplaceorderqty"]) - Convert.ToDecimal(a["fsumpurqty"]) + _sumqty;
                                        a["fsumpurqty"] = _sumqty;
                                    }
                                });
                            }
                            if (Convert.ToBoolean(_["ftype"]))
                            {
                                (_["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).ForEach(a =>
                                {
                                    //型号
                                    a["fpurcanplaceorderqty"] = _sumqty;
                                });
                            }
                            else
                            {
                                (_["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).ForEach(a =>
                                {
                                    a["fpurcanplaceorderqty"] = Convert.ToDecimal(a["fpurcanplaceorderqty"]) - Convert.ToDecimal(a["fsumpurqty"]) + _sumqty;
                                    //型号
                                    a["fsumpurqty"] = _sumqty;
                                });
                            }
                        });
                }
                if (fpurcanplaceorderqty <= 0)
                {
                    //delistEntry["fpurcanplaceorderqty"] = fpurcanplaceorderqty - currQty;
                    errObj.ErrLevel = 3;
                    errObjs.Add(errObj);
                    flag = false;
                }
                else if (currQty > fpurcanplaceorderqty)
                {
                    //delistEntry["fpurcanplaceorderqty"] = fpurcanplaceorderqty - currQty;
                    errObj.ErrLevel = 2;
                    errObjs.Add(errObj);
                    flag = false;
                }
            }
            return flag;
        }

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="formId">来源单据ID</param>
        /// <param name="matchDelist">所有匹配的退市商品清单</param>
        /// <param name="errObjs">用于记录校验不通过的数据信息</param>
        /// <param name="billNo">单据编号</param>
        /// <param name="entrySeq">明细行号</param>
        /// <param name="currQty">本次业务数量</param>
        /// <param name="propValueName">非标商品时的属性值名称</param>
        /// <returns></returns>
        private bool Valid1(string formId, DynamicObject delistObj, DynamicObject delistEntry, List<ErrDelistModel> errObjs, string billNo, string entrySeq, decimal currQty, string propValueName = "")
        {
            var fsalcanplaceorderqty = Convert.ToDecimal(delistEntry["fsalcanplaceorderqty"]);//销售可下单数量
            var fpurcanplaceorderqty = Convert.ToDecimal(delistEntry["fpurcanplaceorderqty"]);//采购可下单数量
            var fwarnqty = Convert.ToDecimal(delistEntry["fwarnqty"]);//预警数量

            var productObj = delistObj["fmaterialid_ref"] as DynamicObject;
            var selTypeObj = delistObj["fseltypeid_ref"] as DynamicObject;

            var errObj = new ErrDelistModel()
            {
                BillNo = billNo,
                BillSeq = entrySeq,
                ProductNo = productObj == null ? "" : Convert.ToString(productObj["fnumber"]),
                ProductName = productObj == null ? "" : Convert.ToString(productObj["fname"]),
                SelType = selTypeObj == null ? "" : Convert.ToString(selTypeObj["fname"]),
                PropValueName = propValueName,
                WarnQty = fwarnqty.ToString("N2"),
                CanSaleQty = fsalcanplaceorderqty.ToString("N2"),
                PurchaseQty = currQty.ToString("N2"),
                CanPurchaseQty = fpurcanplaceorderqty.ToString("N2")
            };

            if (formId.EqualsIgnoreCase("ydj_order"))
            {
                if (fpurcanplaceorderqty <= 0)
                {
                    errObj.ErrLevel = 3;
                    errObjs.Add(errObj);
                    return false;
                }
                else if (fpurcanplaceorderqty <= fwarnqty)
                {
                    errObj.ErrLevel = 2;
                    errObjs.Add(errObj);
                    return false;
                }
                //else if (fpurcanplaceorderqty > fwarnqty)
                //{
                //    errObj.ErrLevel = 1;
                //    errObjs.Add(errObj);
                //    return false;
                //}
            }
            else
            {
                if (fpurcanplaceorderqty <= 0)
                {
                    errObj.ErrLevel = 3;
                    errObjs.Add(errObj);
                    return false;
                }
                else if (currQty > fpurcanplaceorderqty)
                {
                    errObj.ErrLevel = 2;
                    errObjs.Add(errObj);
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 前端显示退市产品校验提示框
        /// </summary>
        /// <param name="currAgent">当前经销商档案</param>
        /// <param name="errObjs">提示数据</param>
        private void ShowExceptionForm(DynamicObject currAgent, List<ErrDelistModel> errObjs, string domainType)
        {
            errObjs = errObjs.OrderBy(t => t.BillNo).ThenBy(d => d.BillSeq).ToList();

            var parentPageId = this.CurrentPageId.IsNullOrEmptyOrWhiteSpace() ? Guid.NewGuid().ToString("N") : this.CurrentPageId;
            var dialogForm = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "ydj_productdelistingdialog");

            var errMsg = "";
            if (this.HtmlForm.Id.EqualsIgnoreCase("ydj_order"))
            {
                //销售合同【保存】
                if (Convert.ToString(currAgent["fisreseller"]).EqualsIgnoreCase("0"))
                {
                    //一级经销商
                    if (errObjs.Any(t => t.ErrLevel == 3))
                    {
                        errMsg = "采购可下单数量为0，请尽快与总部跟单人员确认，避免无法向总部下订单！";
                    }
                    else if (errObjs.Any(t => t.ErrLevel == 2))
                    {
                        errMsg = "本次销售的部分商品总部已下市，可采购数量已小于预警数量，请尽快与总部跟单人员确认，避免无法向总部下订单！";
                    }
                    else
                    {
                        errMsg = "本次销售的部分商品总部已下市，可订货数量有限，请确认是否销售，如继续销售，请尽快向总部提交订货，避免被其他经销商占用！";
                    }
                    errMsg = "以下商品已退市，总部库存已低于预警数，如需采购请尽快与总部跟单确认并提交采购单至总部!";
                }
                else
                {
                    //二级分销商
                    if (errObjs.Any(t => t.ErrLevel == 3))
                    {
                        errMsg = "本次销售的部分商品总部已下市，采购可下单数量为0，请尽快联系一级经销商，让一级经销商与总部跟单人员确认，避免无法向总部下订单！";
                    }
                    else if (errObjs.Any(t => t.ErrLevel == 2))
                    {
                        errMsg = "本次销售的部分商品总部已下市，采购可下单数量已小于预警数量，请尽快联系一级经销商，让一级经销商与总部跟单人员确认，避免无法向总部下订单！";
                    }
                    else
                    {
                        errMsg = "本次销售的部分商品总部已下市，可订货数量有限，请确认是否销售，如继续销售，请尽快向一级经销商提交订货，避免被其他经销商占用！";
                    }
                    errMsg = "以下商品已退市，总部库存已低于预警数，如需采购请尽快联系一级经销商与总部跟单确认，并提交采购单至总部!";
                }
            }
            else
            {
                //采购订单【提交总部】
                if (errObjs.Any(t => t.ErrLevel == 3))
                {
                    errMsg = "本次采购的部分商品总部已下市，采购可下单数量为0，不允许向总部采购，请更换同系列其他商品！";
                }
                else
                {
                    errMsg = "本次采购的部分商品总部已下市，采购数量已超过采购可下单数量，请将采购数量修改为小于等于采购可下单数量再提交总部！";
                }
            }

            var dialogObj = dialogForm.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
            dialogObj["fmessage"] = errMsg;

            dialogObj["fdomaintype"] = domainType;
            dialogObj["fparentformid"] = this.HtmlForm.Id;

            var detailEntry = dialogObj["fdetailentry"] as DynamicObjectCollection;
            foreach (var detail in errObjs)
            {
                var detailItem = detailEntry.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                detailItem["fsalbillno"] = detail.BillNo;
                detailItem["fpurbillno"] = detail.BillNo;
                detailItem["fentryseq"] = $"第{detail.BillSeq}行";
                detailItem["fproductno"] = detail.ProductNo;
                detailItem["fproductname"] = detail.ProductName;
                detailItem["fseltype"] = detail.SelType;
                detailItem["fcolor"] = detail.PropValueName;
                detailItem["fwarnqty"] = detail.WarnQty;
                detailItem["fcansalqty"] = detail.CanSaleQty;
                detailItem["fpurqty"] = detail.PurchaseQty;
                detailItem["fcanpurqty"] = detail.CanPurchaseQty;

                detailEntry.Add(detailItem);
            }

            //var action = this.Context.ShowSpecialForm(dialogForm, dialogObj, false, parentPageId, Enu_OpenStyle.Modal, Enu_DomainType.Bill);
            //this.Result.HtmlActions.Add(action);

            var delistException = new InteractionException("delistcheck{0}".Fmt(this.Context.UserId), errMsg);
            delistException.InteractionData.CustomFormParameter = new Dictionary<string, string>();
            delistException.InteractionData.CustomFormId = "ydj_productdelistingdialog";

            var uiConverter = this.Container.GetService<IUiDataConverter>();
            var billJsonData = uiConverter.CreateUIDataObject(this.Context, dialogForm, dialogObj);
            var uiData = billJsonData.GetJsonValue<JObject>("uiData", new JObject()).ToString();

            delistException.InteractionData.CustomFormParameter.Add("uiData", uiData);
            throw delistException;
        }

        private Dictionary<string, string> ShowMiniException(DynamicObject currAgent, List<ErrDelistModel> errObjs, string domainType)
        {
            Dictionary<string, string> keyValues = new Dictionary<string, string>();
            //var errMsg = "";
            var title = "";
            //StringBuilder errMsg = new StringBuilder();
            List<string> errMsgs = new List<string>();
            if (this.HtmlForm.Id.EqualsIgnoreCase("ydj_order"))
            {
                //销售合同【保存】
                if (Convert.ToString(currAgent["fisreseller"]).EqualsIgnoreCase("0"))
                {
                    title = "以下商品已退市，总部库存已低于预警数，如需采购请尽快与总部跟单确认并提交采购单至总部!";
                    foreach (var detail in errObjs)
                    {
                        errMsgs.Add($@"{errObjs.IndexOf(detail) + 1}、第{detail.BillSeq}行商品[{detail.ProductNo}],名称[{detail.ProductName}],可采购数{detail.CanPurchaseQty},预警数量{detail.WarnQty},可销售数{detail.CanSaleQty}。");
                        //errMsg.Append($@"{errObjs.IndexOf(detail) + 1}、第{detail.BillSeq}行商品[{detail.ProductNo}],名称[{detail.ProductName}],采购可下单数量{detail.PurchaseQty},预警数量{detail.WarnQty},销售可下单数量{detail.CanSaleQty}。");
                    }
                }
                else
                {
                    title = "以下商品已退市，总部库存已低于预警数，如需采购请尽快联系一级经销商与总部跟单确认，并提交采购单至总部!";
                    foreach (var detail in errObjs)
                    {
                        errMsgs.Add($@"{errObjs.IndexOf(detail) + 1}、第{detail.BillSeq}行商品[{detail.ProductNo}],名称[{detail.ProductName}],可采购数{detail.CanPurchaseQty},预警数量{detail.WarnQty},可销售数{detail.CanSaleQty}。");
                        //二级分销商
                        //errMsg.Append($@"{errObjs.IndexOf(detail) + 1}、第{detail.BillSeq}行商品[{detail.ProductNo}],名称[{detail.ProductName}],采购可下单数量{detail.PurchaseQty},预警数量{detail.WarnQty},销售可下单数量{detail.CanSaleQty}。");
                    }
                }
            }

            keyValues.Add("title", title);
            keyValues.Add("errmes", JsonConvert.SerializeObject(errMsgs));
            return keyValues;
        }
    }

    /// <summary>
    /// 用于检验不通过时，前端展示
    /// </summary>
    public class ErrDelistModel
    {
        /// <summary>
        /// 单据编号
        /// </summary>
        public string BillNo { get; set; }

        /// <summary>
        /// 明细行号
        /// </summary>
        public string BillSeq { get; set; }

        /// <summary>
        /// 商品编码
        /// </summary>
        public string ProductNo { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string SelType { get; set; }

        /// <summary>
        /// 属性值名称（前期固定为颜色的属性值名称）
        /// </summary>
        public string PropValueName { get; set; }

        /// <summary>
        /// 预警数量
        /// </summary>
        public string WarnQty { get; set; }

        /// <summary>
        /// 销售可下单数量
        /// </summary>
        public string CanSaleQty { get; set; }

        /// <summary>
        /// 采购数量
        /// </summary>
        public string PurchaseQty { get; set; }

        /// <summary>
        /// 采购可下单数量
        /// </summary>
        public string CanPurchaseQty { get; set; }

        /// <summary>
        /// 错误级别（对应不同提示文案）
        /// 2：本次采购的部分商品总部已下市，采购可下单数量为0，不允许向总部采购，请更换同系列其他商品！
        /// 3：本次采购的部分商品总部已下市，采购数量已超过采购可下单数量，请将采购数量修改为小于等于采购可下单数量再提交总部！
        /// </summary>
        public int ErrLevel { get; set; }
    }

    public class ValidModel
    {
        public string entryId { get; set; }
        public string htmlForm { get; set; }
        public DynamicObject matchDelisting { get; set; }
        public DynamicObject entryItem { get; set; }
        public List<DynamicObject> delistings { get; set; }
        public string billNo { get; set; }
        public string entrySeq { get; set; }
        public decimal currQty { get; set; }
        public string propValueName { get; set; }
    }
}
