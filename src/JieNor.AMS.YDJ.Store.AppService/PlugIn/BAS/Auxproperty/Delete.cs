using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework;
using System.Data;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auxproperty
{
    /// <summary>
    /// 删除辅助属性
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("deleteauxprop")]
    public class Delete: AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            this.Result.IsSuccess = false;
            string materialId = this.GetQueryOrSimpleParam<string>("materialId");
            string auxPropIdStr = this.GetQueryOrSimpleParam<string>("auxpropId");
            string[] auxPropIds = auxPropIdStr?.Split(new[] { ',', '，', ';', '；' }, StringSplitOptions.RemoveEmptyEntries).Distinct().ToArray();

            if (string.IsNullOrWhiteSpace(materialId))
            {
                throw new BusinessException("materialId参数不能为空!");
            }

            if (auxPropIds == null || auxPropIds.Length <= 0)
            {
                throw new BusinessException("auxpropId参数不能为空!");
            }

            //在组合值表中判断辅助属性是不是被使用过
            string sql = @"select ve.fauxpropid from [T_BD_AUXPROPVALUE] v
inner join [T_BD_AUXPROPVALUEENTRY] ve on v.fid=ve.fid
where v.fmaterialid=@p and ve.fauxpropid{0} and v.fmainorgid=@c";

            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@c", DbType.String, this.Context.Company),
                new SqlParam("@p",DbType.String,materialId)
            };

            StringBuilder where = new StringBuilder();

            if (auxPropIds.Length == 1)
            {
                where.Append(" = @a");
                sqlParams.Add(new SqlParam("@a", DbType.String, auxPropIds[0]));
            }
            else
            {
                where.Append(" in (");
                where.Append(string.Join(",", auxPropIds.Select((x, i) => string.Format("@a{0}", i))));
                where.Append(")");
                sqlParams.AddRange(auxPropIds.Select((x, i) => new SqlParam(string.Format("@a{0}", i), DbType.String, x)));
            }

            sql = string.Format(sql, where.ToString());

            List<string> auxPropList = new List<string>();

            using (var dataReader = this.DBService.ExecuteReader(this.Context, sql, sqlParams))
            {
                if (dataReader.Read())
                {
                    auxPropList.Add(Convert.ToString(dataReader[0]));
                }
            }

            //获取辅助属性的信息
            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "bd_auxpropvaluemap");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            where = new StringBuilder("fmaterialid=@fmaterialid and fmainorgid=@fmainorgid");
            sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmaterialid", DbType.String, materialId),
                new SqlParam("@fmainorgid",DbType.String,this.Context.Company)
            };

            var dbReader = this.Context.GetPkIdDataReader(htmlForm, where.ToString(), sqlParams);
            var dbEntities = dm.SelectBy(dbReader).OfType<DynamicObject>().ToList();

            //辅助属性类别没有被当前物料引用，可以直接删除
            if (dbEntities == null || dbEntities.Count <= 0)
            {
                this.Result.SrvData = new
                {
                    ids = auxPropIds
                };
                this.Result.IsSuccess = true;
                return;
            }

            //当前可以被删除的辅助属性类别
            var deleteItems = dbEntities.Where(x =>
             {
                 string auxPropId = Convert.ToString(x["fauxpropid"]);
                 return auxPropIds.Contains(auxPropId) && !auxPropList.Contains(auxPropId);
             }).ToList();

            if (deleteItems == null || deleteItems.Count <= 0)
            {
                this.Result.ComplexMessage.ErrorMessages.Add("辅助属性类别已被引用，不允许删除！");
                this.Result.IsSuccess = false;
                return;
            }

            var deleteIds = deleteItems.Select(x => Convert.ToString(x["Id"])).ToList();
            var dbIds = dbEntities.Select(x => Convert.ToString("Id")).ToList();
            var deleteAuxPropId = deleteItems.Select(x => Convert.ToString(x["fauxpropid"])).ToList();

            dm.Delete(deleteIds);

            //如果所有辅助属性都被删除，那么则要更新物料fispresetprop属性为false
            if (dbIds.All(x => deleteIds.Contains(x)))
            {
                htmlForm = metaModelService.LoadFormModel(this.Context, "ydj_product");
                dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

                var productEntity = dm.Select(materialId) as DynamicObject;
                if (productEntity == null)
                {
                    this.Result.ComplexMessage.ErrorMessages.Add("当前操作关联的商品不存在，删除失败！");
                    this.Result.IsSuccess = false;
                    return;
                }

                productEntity["fispresetprop"] = false;
                dm.Save(productEntity);
            }

            this.Result.SrvData = new
            {
                ids = deleteAuxPropId
            };
            this.Result.ComplexMessage.SuccessMessages.Add("辅助属性类别删除成功！");
            this.Result.IsSuccess = true;
        }
    }
}
