using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework;
using JieNor.AMS.YDJ.Store.AppService.Validation.BAS.Agent;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Attachlist
{
    [InjectService]
    [FormId("bd_attachlist")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() <= 0) return;

            var salorders = e.DataEntitys.Where(a => Convert.ToString(a["flinkformid"]).Equals("ydj_order")).Select(a => Convert.ToString(a["flinkbillinterid"])).ToList();
            if (salorders.Count > 0)
            {
                IMuSiService muSiService = this.Container.GetService<IMuSiService>();
                var orderObjs = this.Context.LoadBizDataById("ydj_order", salorders, true);
                var swjOrders = orderObjs.Where(a => Convert.ToString((a["fbilltype_ref"] as DynamicObject)?["fname"]).Equals("v6定制柜合同"));
                if (swjOrders != null && swjOrders.Count() > 0)
                {
                    List<KeyValuePair<string, bool>> keyValuePairs = new List<KeyValuePair<string, bool>>();
                    foreach (var item in e.DataEntitys)
                    {
                        var drawentity = item["fdrawentity"] as DynamicObjectCollection;
                        foreach (var drawentityItem in drawentity)
                        {
                            if (!string.IsNullOrWhiteSpace(Convert.ToString(drawentityItem["ffilegrouping"])))
                            {
                                if (Convert.ToInt32(drawentityItem["funstdtypedoc"]) == 1)
                                    keyValuePairs.Add(new KeyValuePair<string, bool>(Convert.ToString(drawentityItem["ffilegrouping"]), true));
                                else
                                    keyValuePairs.Add(new KeyValuePair<string, bool>(Convert.ToString(drawentityItem["ffilegrouping"]), false));
                            }
                        }
                    }
                    if (keyValuePairs.Count > 0)
                    {
                        var entry = orderObjs[0]["fentry"] as DynamicObjectCollection;
                        foreach (var item in entry)
                        {
                            var factorybillno = Convert.ToString(item["ffactorybillno"]);
                            //存在一个为true的，那销售合同明细的【工厂单非标】则为true
                            if (keyValuePairs.Where(a => a.Key == factorybillno).Any(a => a.Value == true))
                            {
                                item["funstdtypefactory"] = 1;
                            }
                            else
                            {
                                if (keyValuePairs.Any(a => a.Key == factorybillno))
                                {
                                    //都为false，那销售合同明细的【工厂单非标】则为false
                                    if (keyValuePairs.Where(a => a.Key == factorybillno).Where(a => a.Value == false).Count() == keyValuePairs.Count)
                                    {
                                        item["funstdtypefactory"] = 0;
                                    }
                                }
                                else
                                {
                                    //附件里都不存在，就主动清空一次。
                                    item["funstdtypefactory"] = 0;
                                }
                            }
                        }

                        var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                        var orderFormType = orderForm.GetDynamicObjectType(this.Context);
                        var dataPropDm = this.GetDataManager();
                        dataPropDm.InitDbContext(this.Context, orderForm.GetDynamicObjectType(this.Context));
                        dataPropDm.Save(orderObjs);

                    }

                    var orderFormMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                    //实时调用接口将数据传到定制OMS中台。
                    muSiService.OMSSyncAsync(this.Context, orderFormMeta, orderObjs);
                }
            }

        }
    }
}
