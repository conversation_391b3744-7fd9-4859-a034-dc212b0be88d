using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 商品：发布、重新发布
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("setProductPublish")]
    public class SetProductPublish : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            //强制重新发布
            var forceRepublish = this.GetQueryOrSimpleParam<bool>("forceRepublish");

            //对于未发布的不能执行重新发布
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (forceRepublish && Convert.ToString(newData["fsendstatus"]).EqualsIgnoreCase("未发布"))
                {
                    return false;
                }
                return true;
            }).WithMessage("商品【{0}】未发布，无法重新发布，请先点击【发布】操作！", (billObj, propObj) => propObj["fname"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data["fsendstatus"] as string)
                .IsTrue((data, pValue) => forceRepublish || !forceRepublish && !pValue.EqualsIgnoreCase("已发布"))
                .WithMessage("商品【{0}】已发布，不用重复发布，确实需要更新发布请点击【重新发布】操作！", (data, pValue) => data["fname"]));
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            string param = this.GetQueryOrSimpleParam<string>("id");

            if (e.DataEntitys == null
                || e.DataEntitys.Length <= 0)
            {
                if (this.Result.ComplexMessage.HasMessage == false)
                    throw new BusinessException("请至少选择一行未发布商品资料后再进行操作！");
                return;
            }
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            this.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, dm.DataEntityType, e.DataEntitys, false);

            List<Dictionary<string, string>> push_list = new List<Dictionary<string, string>>();
            List<string> error = new List<string>();
            foreach (var item in e.DataEntitys)
            {
                item["fsendstatus"] = "已发布";
                item["fsenddate"] = DateTime.Now;

                Dictionary<string, string> push = new Dictionary<string, string>();
                push.Add("fname", item["fname"] as string);
                push.Add("fcategory", (item["fcategoryid_ref"] as DynamicObject)?["fname"] as string);
                push.Add("fimage", item["fimage"] as string);
                push.Add("funit", (item["funitid_ref"] as DynamicObject)?["fname"] as string);
                push.Add("fbrand", (item["fbrandid_ref"] as DynamicObject)?["fname"] as string);
                push.Add("fpublishcompany", this.Context.Companys.Where(t => t.CompanyId == this.Context.Company).FirstOrDefault().CompanyName);
                push.Add("fpublishcompanyid", this.Context.Company);
                push.Add("fcontent", item["fcontent"] as string);
                push.Add("fproductid", item["id"] as string);
                push.Add("fpublishstatus", "已上架");
                push.Add("fpublishdate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                push_list.Add(push);
            }
            dm.Save(e.DataEntitys);
            this.Result.SimpleMessage = "发布成功";
            this.Result.IsSuccess = true;

            #region 向EIS站点发送协同商品数据包
            var gateWay = this.Container.GetService<IHttpServiceInvoker>();
            var ToEIS = new CommonBillDTO()
                .SetBillData(push_list.ToJson())
                .SetFormId("syn_product")
                .SetOperationNo("productSynPublish")
                .SetOptionFlag((long)Enu_OpFlags.RequestToReply)
                .Runbackground();
            var resp = gateWay.Invoke(this.Context, TargetSEP.EisService, ToEIS);
            if (resp is DynamicDTOResponse)
            {
                //this.Result.MergeResult((resp as DynamicDTOResponse).OperationResult);
            }
            #endregion        
        }


        public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        {
            base.PrepareBusinessServices(e);
            e.Services.Add(new FormServiceDesc()
            {
                ServiceAlias = "filesyn",
                Condition = "",
                ParamString = new
                {
                    companyFieldKey = "#gw#",
                }.ToJson(),
            });
        }
    }
}