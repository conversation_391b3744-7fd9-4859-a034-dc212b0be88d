using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.TransferInTask
{
    /// <summary>
    /// 调入扫描任务：列表查询数据插件
    /// </summary>
    [InjectService]
    [FormId("bcm_transferintask")]
    [OperationNo("querydata")]
    public class QueryData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 自定义服务端事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.PrepareQueryBuilderParameter:
                    this.PrepareQueryBuilderParameter(e);
                    break;
            }
        }

        /// <summary>
        /// 列表准备查询过滤参数事件
        /// </summary>
        /// <param name="e"></param>
        public void PrepareQueryBuilderParameter(OnCustomServiceEventArgs e)
        {
            var param = e.EventData as SqlBuilderParameter;
            if (param == null) return;
            param.FilterString = param.FilterString.JoinFilterString(" ftask_type='transferin' ");

        }
    }
}
