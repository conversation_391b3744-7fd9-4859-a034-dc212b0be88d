using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.ScanResult
{
    /// <summary>
    /// 条码扫描记录：根据条码加载扫描记录
    /// </summary>
    [InjectService]
    [FormId("bcm_scanresult")]
    [OperationNo("LoadScanResult")]
    public class LoadScanResult : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 准备操作选项时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            e.OpCtlParam.IgnoreOpMessage = true;
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var fnumber = this.GetQueryOrSimpleParam<string>("fnumber");
            if (fnumber.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数 fnumber 为空，请检查！");
            }

            var sourceformid = this.GetQueryOrSimpleParam<string>("sourceformid");
            if (!sourceformid.IsNullOrEmptyOrWhiteSpace())
            {
                //查询出收货/发货/盘点扫描任务的条码扫描记录
                if (ISScanTask(sourceformid))
                {
                    ScanTaskQuery(sourceformid, fnumber);
                    return;
                }
                else
                {
                    throw new BusinessException("未知的 sourceformid ，请检查！");
                }
            }    

            var categoryObj = this.Context.LoadBizDataByNo(this.HtmlForm.Id, "fbarcode", new[] { fnumber });
            if (categoryObj == null) return;

            //加载引用数据
            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), categoryObj, false);

            this.Result.SrvData = categoryObj.OrderBy(s => Convert.ToDateTime(s["fopdatetime"]));
            this.Result.IsSuccess = true;
        }


        /// <summary>
        /// 查询单据是否是收货/发货/盘点扫描任务
        /// </summary>
        /// <returns></returns>
        private bool ISScanTask(string fromId = "")
        {
            return new List<string>() { "bcm_receptionscantask", "bcm_deliveryscantask", "bcm_countscantask" }.Contains(fromId.ToLower());
        }

        /// <summary>
        /// 查询出收货/发货/盘点扫描任务的条码扫描记录
        /// </summary>
        /// <param name="fromid"></param>
        /// <param name="fnumber"></param>
        private void ScanTaskQuery(string fromid, string fnumber)
        {

            var filterString = " fsourceformid=@fsourceformid and fsourcebillno=@fsourcebillno ";
            var parmList = new List<SqlParam>() { new SqlParam("@fsourceformid", DbType.String, fromid), new SqlParam("@fsourcebillno", DbType.String, fnumber) };

            var categoryObj = this.Context.LoadBizDataByFilter(this.HtmlForm.Id, filterString, true, parmList);
            if (categoryObj == null) return;

            //加载引用数据
            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), categoryObj, false);

            this.Result.SrvData = categoryObj.OrderBy(s => Convert.ToDateTime(s["fopdatetime"]));
            this.Result.IsSuccess = true;
        }

    }
}
