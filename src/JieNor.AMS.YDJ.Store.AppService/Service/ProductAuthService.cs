using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using ServiceStack;

namespace JieNor.AMS.YDJ.Store.AppService.Service
{
    /// <summary>
    /// 商品授权清单服务
    /// </summary>
    [InjectService]
    public class ProductAuthService : IProductAuthService
    {
        /// <summary>
        /// 添加默认授权行（经销商授权、送达方授权、门店授权接口固定增加"通配品牌"与"慕思助眠"授权）
        /// 需要手动保存
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productAuths"></param>
        public void AddDefault(UserContext userCtx, IEnumerable<DynamicObject> productAuths)
        {
            if (productAuths.IsNullOrEmpty()) return;

            var storeService = userCtx.Container.GetService<IStoreService>();

            var dbService = userCtx.Container.GetService<IDBService>();

            // 增加 2行授权, 一行为【系列】="Z1" (通配品牌), 另一行为【品牌】="R"(慕思助眠)对应行【系列】="M1" (慕思助眠)

            string sql = $@"
select b.fid as fbrandid, b.fnumber as fbrandnumber, b.fname as fbrandname, s.fid as fserieid, s.fnumber as fserienumber, s.fname as fseriename 
from t_ydj_series s with(nolock) 
left join t_ydj_brand b with(nolock) on s.fbrandid=b.fid 
where s.fnumber in ('Z1', 'M1') and s.fforbidstatus='0';
";

            var seriesZ1AndM1 = dbService.ExecuteDynamicObject(userCtx, sql);
            //去门店id
            var storeids = productAuths.Where(o => Convert.ToString(o["forgtype"]).EqualsIgnoreCase("5")).Select(o => Convert.ToString(o["forgid"])).ToList();
            // 获取《门店与系列》
            // 扩展获取父级系列（即业绩品牌）
            sql = $@"
                    select ss.fstoreid as fstoretranid, ss.fmodifydate, s.fid as fserieid, s.fname as fseriename, s.fbrandid,  p.fid as fparentserieid,p.fname as fparentseriename, ss.fforbidstatus,st.fid as fstoreid
                    ,isnull(bd.fauto_M1,0) as fauto_M1 ,ISNULL(bd.fauto_Z1,0) as fauto_Z1 ,ISNULL(bd.fmusibrand,0) as fmusibrand from t_ms_store_series ss with(nolock) 
                    inner join t_bas_store as st with(nolock) on st.ftranid = ss.fstoreid                    
                    inner join t_ydj_series s with(nolock) on ss.fseriesnumber = s.fnumber
                    left join t_ydj_series p with(nolock) on s.fparentid=p.fid
                    left join t_ydj_brand bd with(nolock) on bd.fid = s.fbrandid
                    where st.fid in ('{string.Join("','", storeids)}')";
            var storeSerieses = dbService.ExecuteDynamicObject(userCtx, sql).ToList();
            storeSerieses = storeService.DistinctStoreSerieses(storeSerieses);

            foreach (var productAuth in productAuths)
            {
                var storeid = Convert.ToString(productAuth["forgid"]);

                //新逻辑：fmusibrand=1 表示慕思品牌，是慕思品牌的才会添加到门店的商品授权清单中。
                var matchSeries = storeSerieses
                    .Where(s => Convert.ToString(s["fstoreid"]).EqualsIgnoreCase(storeid) && Convert.ToString(s["fmusibrand"]).EqualsIgnoreCase("1"));

                //门店找到多个品牌时，取并集，只要存在一个勾选了自动授权通配 那门店授权就要添加通配
                var fauto_M1 = matchSeries.Any(o => Convert.ToString(o["fauto_M1"]).EqualsIgnoreCase("1"));
                var fauto_Z1 = matchSeries.Any(o => Convert.ToString(o["fauto_Z1"]).EqualsIgnoreCase("1"));

                // 授权品牌/系列
                var entrys = (DynamicObjectCollection)productAuth["fproductauthbs"];

                // 判断【系列】="Z1" (通配品牌)是否存在
                var z1 = seriesZ1AndM1.FirstOrDefault(s =>
                    Convert.ToString(s["fserienumber"]).EqualsIgnoreCase("Z1") &&
                    Convert.ToString(s["fbrandid"]).IsNullOrEmptyOrWhiteSpace());
                if (z1 != null)
                {
                    var entry = entrys.FirstOrDefault(s =>
                        Convert.ToString(s["fbrandid"]).IsNullOrEmptyOrWhiteSpace()
                        && Convert.ToString(s["fserieid"]).EqualsIgnoreCase(Convert.ToString(z1["fserieid"])));
                    if (entry == null)
                    {
                        //如果勾选了才预置通配
                        if (fauto_Z1)
                        {
                            entry = (DynamicObject)entrys.DynamicCollectionItemPropertyType.CreateInstance();
                            //entry["fbrandid"] = Convert.ToString(z1["fbrandid"]);
                            entry["fserieid"] = z1["fserieid"];
                            entry["fserieid_txt"] = z1["fseriename"];

                            entrys.Add(entry);
                        }
                    }
                }

                // 判断【品牌】="R"(慕思助眠)对应行【系列】="M1" (慕思助眠)是否存在
                var m1 = seriesZ1AndM1.FirstOrDefault(s =>
                    Convert.ToString(s["fserienumber"]).EqualsIgnoreCase("M1") &&
                    Convert.ToString(s["fbrandnumber"]).EqualsIgnoreCase("R"));
                if (m1 != null)
                {
                    // 判断品牌R的行是否存在
                    var entry = entrys.FirstOrDefault(s => Convert.ToString(s["fbrandid"]).EqualsIgnoreCase(Convert.ToString(m1["fbrandid"])));
                    // 不存在，则新增行
                    if (entry == null)
                    {
                        //如果勾选了才预置助眠
                        if (!fauto_M1) continue;
                        entry = (DynamicObject)entrys.DynamicCollectionItemPropertyType.CreateInstance();
                        entry["fbrandid"] = m1["fbrandid"];
                        entry["fserieid"] = m1["fserieid"];
                        entry["fserieid_txt"] = m1["fseriename"];

                        entrys.Add(entry);
                    }
                    // 不包括此系列，就增加系列
                    else if (!Convert.ToString(entry["fserieid"]).Contains(Convert.ToString(m1["fserieid"])))
                    {
                        if (fauto_M1)
                        {
                            if (Convert.ToString(entry["fserieid"]).IsNullOrEmptyOrWhiteSpace())
                            {
                                entry["fserieid"] = m1["fserieid"];
                                entry["fserieid_txt"] = m1["fseriename"];
                            }
                            else
                            {
                                entry["fserieid"] += $",{m1["fserieid"]}";
                                entry["fserieid_txt"] += $",{m1["fseriename"]}";
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 添加【专供标记】商品到单据体-例外商品
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productAuths"></param>
        /// <param name="autoSave">是否自动保存</param>
        public void AddSpecial(UserContext userCtx, IEnumerable<DynamicObject> productAuths, bool autoSave)
        {
            if (productAuths.IsNullOrEmpty()) return;

            var sql = $@"select fid from t_bd_material where fspecialflag=1";
            var specialProductIds = userCtx.ExecuteDynamicObject(sql, new List<SqlParam>()).Select(s => Convert.ToString(s["fid"]));

            AddSpecial(userCtx, productAuths, autoSave, specialProductIds);
        }

        /// <summary>
        /// 添加【专供标记】商品到单据体-例外商品
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productAuths"></param>
        /// <param name="autoSave">是否自动保存</param>
        /// <param name="specialProductIds">指定专供标记商品ids</param>
        public void AddSpecial(UserContext userCtx, IEnumerable<DynamicObject> productAuths, bool autoSave,
               IEnumerable<string> specialProductIds)
        {
            if (specialProductIds.IsNullOrEmpty()) return;

            var savedProductAuths = new List<DynamicObject>();
            var savedProductAuths_ot = new List<DynamicObject>();
            var ids = productAuths.Select(o => Convert.ToString(o["id"])).ToList();
            var productAuths_ots = userCtx.LoadBizDataByNo("ydj_productauth_other", "fsoureid", ids);
            // 遍历所有《商品授权清单》
            foreach (var productAuth in productAuths)
            {
                var fproductauthexclude = (DynamicObjectCollection)productAuth["fproductauthexclude"];
                bool needSave = false;

                foreach (var productId in specialProductIds)
                {
                    // 例外中没有，添加
                    if (!fproductauthexclude.Any(
                        s => Convert.ToString(s["fproductid_o"]).EqualsIgnoreCase(productId)))
                    {
                        needSave = true;
                        var entry = (DynamicObject)fproductauthexclude.DynamicCollectionItemPropertyType.CreateInstance();
                        entry["fproductid_o"] = productId;

                        fproductauthexclude.Add(entry);
                    }
                }

                // 有变更，需要保存则添加到集合中
                if (needSave)
                {
                    savedProductAuths.Add(productAuth);
                }
            }

            foreach (var productAuth_ot in productAuths_ots)
            {
                var fproductauthexclude_ot = (DynamicObjectCollection)productAuth_ot?["fproductauthexclude_ot"];
                bool needSave = false;
                foreach (var productId in specialProductIds)
                {
                    if (!fproductauthexclude_ot.Any(
                        s => Convert.ToString(s["fproductid_o"]).EqualsIgnoreCase(productId)))
                    {
                        needSave = true;
                        var entry = (DynamicObject)fproductauthexclude_ot.DynamicCollectionItemPropertyType.CreateInstance();
                        entry["fproductid_o"] = productId;

                        fproductauthexclude_ot.Add(entry);
                    }
                }

                // 有变更，需要保存则添加到集合中
                if (needSave)
                {
                    savedProductAuths_ot.Add(productAuth_ot);
                }
            }
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            IOperationResult result = new OperationResult();
            if (autoSave && savedProductAuths.Any())
            {
                //var opResult = gateway.InvokeBillOperation(userCtx, "ydj_productauth", savedProductAuths, "draft", new Dictionary<string, object>());
                //result.MergeResult(opResult);
                userCtx.SaveBizData("ydj_productauth", savedProductAuths);

                userCtx.Container.GetService<IProductAuthService>().ClearPrdAuthCache(userCtx, savedProductAuths, null, "AddSpecial");
            }
            if (autoSave && savedProductAuths_ot.Any())
            {
                //var opResult = gateway.InvokeBillOperation(userCtx, "ydj_productauth_other", savedProductAuths_ot, "draft", new Dictionary<string, object>());
                //result.MergeResult(opResult);
                userCtx.SaveBizData("ydj_productauth_other", savedProductAuths_ot);
            }

        }
        public void AddSpecialBySql(UserContext userCtx, IEnumerable<string> specialProductIds)
        {
            var sql = new List<string>();
            if (specialProductIds.IsNullOrEmpty()) return;
            foreach (var id in specialProductIds)
            {
                sql.Add($@"/*dialect*/
                            insert into t_ydj_productauthbs_prd(fentryid,fseq,fproductid_o,fid)
                            select newid() as fentryid,isnull((select max(fseq)+1 from t_ydj_productauthbs_prd where fid = at.fid ) ,1)as fseq,'{id}' as fproductid_o ,at.fid as fid
                            from t_ydj_productauth as at(nolock) 
                            where exists( select 1 from t_bas_organization with(nolock) where forgid = t_bas_organization.fid and forgtype='4') 
                            and not exists(select 1 from t_ydj_productauthbs_prd as ot(nolock) where ot.fid = at.fid and ot.fproductid_o = '{id}');
                    ");

                sql.Add($@"/*dialect*/
                            insert into t_ydj_productauthbs_prd_ot(fentryid,fseq,fproductid_o,fid)
                            select newid() as fentryid,isnull((select max(fseq)+1 from t_ydj_productauthbs_prd_ot where fid = at.fid ),1) as fseq,'{id}' as fproductid_o ,at.fid as fid 
                            from t_ydj_productauth_other as at(nolock) 
                            where exists( select 1 from t_bas_organization with(nolock) where forgid = t_bas_organization.fid and forgtype='4') 
                             and exists (select 1 from  t_bd_materialsaleorg b  where b.fid='{id}' and b.fsaleorgid=at.fsaleorgid)                          
                             and not exists(select 1 from t_ydj_productauthbs_prd_ot as ot(nolock) where ot.fid = at.fid and ot.fproductid_o = '{id}');
                    ");
            }

            if (sql.Count > 0)
            {
                var dbSvc = userCtx.Container.GetService<IDBServiceEx>();
                dbSvc.ExecuteBatch(userCtx, sql);
            }
        }
        /// <summary>
        /// 所有【授权组织类型】= "经销商"的《商品授权清单》将【专供标记】商品到单据体-例外商品删除
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="specialProductIds"></param>
        public void RemoveSpecial(UserContext userCtx, IEnumerable<string> specialProductIds)
        {
            if (specialProductIds.IsNullOrEmpty()) return;

            // 直接删除
            string sql = $@"
delete from t_ydj_productauthbs_prd 
where fproductid_o in ({specialProductIds.JoinEx(",", true)}) 
and fid in 
(
    select fid from t_ydj_productauth 
    where forgid in 
    (
        select fid from t_bas_organization where forgtype='4'   -- 经销商
    )
);";
            sql += $@"
delete from t_ydj_productauthbs_prd_ot 
where fproductid_o in ({specialProductIds.JoinEx(",", true)}) 
and fid in 
(
    select fid from t_ydj_productauth_other 
    where forgid in 
    (
        select fid from t_bas_organization where forgtype='4'   -- 经销商
    )
);";

            userCtx.Container.GetService<IDBServiceEx>().Execute(userCtx, sql);
        }

        /// <summary>
        /// 禁用
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orgIds">组织ids</param>
        /// <returns></returns>
        public IOperationResult Forbid(UserContext userCtx, HtmlForm formMate, IEnumerable<string> orgIds, OperateOption option = null)
        {
            IOperationResult result = new OperationResult();
            result.IsSuccess = true;

            if (orgIds.IsNullOrEmpty())
            {
                return result;
            }

            var prdAuths = userCtx.LoadBizDataByNo("ydj_productauth", "forgid", orgIds);
            if (prdAuths.IsNullOrEmpty())
            {
                return result;
            }

            // 待禁用：禁用状态=否
            var forbidingPrdAuths = prdAuths.Where(s => !Convert.ToBoolean(s["fforbidstatus"])).ToList();
            if (forbidingPrdAuths.IsNullOrEmpty())
            {
                return result;
            }

            IOperationResult opResult = null;
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();

            // 反审核
            var unauditPrdAuths = forbidingPrdAuths
                .Where(s => Convert.ToString(s["fstatus"]).EqualsIgnoreCase("E"))
                .ToList();
            if (unauditPrdAuths.Any())
            {
                opResult = gateway.InvokeBillOperation(userCtx, "ydj_productauth", unauditPrdAuths, "UnAudit",
                    new Dictionary<string, object>());
                result.MergeResult(opResult);
            }

            // 撤消提交
            var unsubmitPrdAuths = forbidingPrdAuths
                .Where(s => Convert.ToString(s["fstatus"]).EqualsIgnoreCase("D"))
                .ToList();
            if (unsubmitPrdAuths.Any())
            {
                opResult = gateway.InvokeBillOperation(userCtx, "ydj_productauth", unsubmitPrdAuths, "UnSubmit",
                    new Dictionary<string, object>());
                result.MergeResult(opResult);
            }

            foreach (var prdAuth in forbidingPrdAuths)
            {
                prdAuth["fdescription"] = $"{formMate.Caption}禁用，自动禁用该商品授权清单";
                var entrys = (DynamicObjectCollection)prdAuth["fproductauthbs"];
                entrys.Clear();
            }
            opResult = gateway.InvokeBillOperation(userCtx, "ydj_productauth", forbidingPrdAuths, "save", option?.ToDictionary(s => s.Key, s => s.Value) ?? new Dictionary<string, object>());
            result.MergeResult(opResult);

            opResult = gateway.InvokeBillOperation(userCtx, "ydj_productauth", forbidingPrdAuths, "Forbid", new Dictionary<string, object>());
            result.MergeResult(opResult);

            //经销商关联送达方的品牌系列都清空
            var delivers = userCtx.LoadBizDataByFilter("bas_deliver", $" fagentid in ('{string.Join("','", orgIds)}')");
            //禁用完需要清空送达方品牌系列、商品授权系列的品牌系列
            if (delivers.IsNullOrEmpty()) return result;

            var dbService = userCtx.Container.GetService<IDBService>();
            foreach (var deliver in delivers)
            {
                // 授权品牌/系列
                var entrys = (DynamicObjectCollection)deliver["fentry"];
                entrys.Clear();
            }
            opResult = gateway.InvokeBillOperation(userCtx, "bas_deliver", delivers, "save", new Dictionary<string, object>());
            result.MergeResult(opResult);

            return result;
        }

        /// <summary>
        /// 反禁用
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orgIds">组织ids</param>
        /// <returns></returns>
        public IOperationResult Unforbid(UserContext userCtx, HtmlForm formMate, IEnumerable<string> orgIds, OperateOption option = null)
        {
            IOperationResult result = new OperationResult();
            result.IsSuccess = true;

            if (orgIds.IsNullOrEmpty())
            {
                return result;
            }

            var prdAuths = userCtx.LoadBizDataByNo("ydj_productauth", "forgid", orgIds);
            if (prdAuths.IsNullOrEmpty())
            {
                return result;
            }

            // 待反禁用：禁用状态=是
            var unforbidingPrdAuths = prdAuths.Where(s => Convert.ToBoolean(s["fforbidstatus"])).ToList();
            if (unforbidingPrdAuths.IsNullOrEmpty())
            {
                return result;
            }

            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();

            foreach (var prdAuth in unforbidingPrdAuths)
            {
                prdAuth["fdescription"] = ""; //$"{formMate.Caption}反禁用，自动反禁用该商品授权清单";
            }

            var opResult = gateway.InvokeBillOperation(userCtx, "ydj_productauth", unforbidingPrdAuths, "UnForbid", new Dictionary<string, object>());
            result.MergeResult(opResult);

            //根据【送达方编码+城市】在《送达方与系列》关系表中找到系列信息，然后更新【送达方】的品牌信息；最后根据【送达方】的品牌信息全量更新商品授权清单
            var delivers = userCtx.LoadBizDataByFilter("bas_deliver", $" fagentid in ('{string.Join("','", orgIds)}')");
            //禁用完需要清空送达方品牌系列、商品授权系列的品牌系列
            if (delivers.IsNullOrEmpty()) return result;

            // 更新送达方-品牌单据体
            var deliverService = userCtx.Container.GetService<IDeliverService>();
            deliverService.UpdateBrandEntry(userCtx, delivers);
            userCtx.SaveBizData("bas_deliver", delivers);
            deliverService.AddOrUpdateProductAuth(userCtx, delivers, true, option);

            return result;
        }



        /// <summary>
        /// 清空商品授权缓存数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productAuths"></param>
        public void ClearPrdAuthCache(UserContext userCtx, IEnumerable<DynamicObject> productAuths, HtmlForm htmlForm = null, string operationNo = null)
        {

            if (productAuths == null || !productAuths.Any())
            {
                return;
            }

            // 经销商组织类型，门店组织类型
            string agentOrgType = "4", storeOrgType = "5";

            var agentIds = productAuths
                .Where(s => Convert.ToString(s["forgtype"]).EqualsIgnoreCase(agentOrgType))
                .Select(s => Convert.ToString(s["forgid"]))
                .Where(s => !s.IsNullOrEmptyOrWhiteSpace())
                .Distinct()
                .ToList();

            var storeIds = productAuths
                .Where(s => Convert.ToString(s["forgtype"]).EqualsIgnoreCase(storeOrgType))
                .Select(s => Convert.ToString(s["forgid"]))
                .Where(s => !s.IsNullOrEmptyOrWhiteSpace())
                .Distinct()
                .ToList();

            if (storeIds.Any())
            {
                string sql =
                    $"select distinct fagentid from t_bas_store with(nolock) where fid in ({storeIds.JoinEx(",", true)})";

                var storeAgentIds = userCtx.ExecuteDynamicObject(sql, new List<SqlParam>())
                    .Select(s => Convert.ToString(s["fagentid"])).ToList();

                agentIds = agentIds.Union(storeAgentIds).Distinct().ToList();
            }

            // 判断商品授权清单的授予组织类型，如果是门店，就要找到对应的经销商清缓存
            if (agentIds.Any())
            {
                //清除缓存 
                ProductDataIsolateHelper.ClearCacheByBiz(userCtx, new PrdDataIsolateChannelMessage
                {
                    Message = $"{htmlForm?.Caption}-{operationNo}",
                    TopCompanyId = userCtx.TopCompanyId
                }, agentIds);
            }
        }


        #region  清理二级分销商商品授权清单
        /// <summary>
        /// 处清除二级分销商商品授权清单数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productAuths">此次操作的一级商品授权清单</param>
        /// <param name="addexcludeProdIds">添加专供的商品</param>
        /// <param name="removeexcludeProdIds">移除专供商品</param>
        /// <param name="htmlForm"></param>
        /// <param name="operationNo"></param>
        public void ClearSecondAgentAuth(UserContext userCtx, List<DynamicObject> productAuths,
            List<string> addexcludeProdIds, List<string> removeexcludeProdIds,List<string> removeAuthProdIds,
            HtmlForm htmlForm = null, string operationNo = null, string operationName = null)
        {
            var logService = userCtx.Container.GetService<ILogService>();
            StringBuilder dataLog = new StringBuilder();
            dataLog.Append($"ydj_productauth:{operationNo}->ClearSecondAgentAuth-begin:{DateTime.Now.ToString("yyyyMMddHHmmss")}");
            if (!userCtx.IsTopOrg || !productAuths.Any()) {
                dataLog.AppendLine($"ydj_productauth:{operationNo}->ClearSecondAgentAuth-end:{DateTime.Now.ToString("yyyyMMddHHmmss")},IsTopOrg:{userCtx.IsTopOrg}，productAuths:{productAuths.Count}");
                logService.WriteLogToFile(dataLog.ToString(), "二级经销商授权清理清单日志Log");
                return;
            }
            if (addexcludeProdIds == null) { addexcludeProdIds = new List<string>(); }
            if (removeexcludeProdIds == null) { removeexcludeProdIds = new List<string>(); }
            if (removeAuthProdIds == null) { removeAuthProdIds = new List<string>(); }
            var storeService = userCtx.Container.GetService<IStoreService>();
            var agentService = userCtx.Container.GetService<IAgentService>();
          
            var deliverService = userCtx.Container.GetService<IDeliverService>();

            #region 获取数据
            var finalAllorgid = productAuths.Select(o => Convert.ToString(o["forgid"])).ToList();
            //1. 获取所有主子经销商集合（包含主经销商及其关联子经销商）
            Dictionary<string, List<string>> agentGroups = agentService.GetMasterSubAgentsBatchAsync(userCtx, new HashSet<string>(finalAllorgid));
            var parentIds = agentGroups.Values.SelectMany(list => list).ToList();
            //2. 查找所有二级经销商（分销商且上级在主子经销商集合中
            List<DynamicObject> secondAgents = agentService.GetSecondLevelAgentsBatchAsync(userCtx, parentIds);
            if (!secondAgents.Any()) {
                dataLog.AppendLine($"ydj_productauth:{operationNo}->ClearSecondAgentAuth-end:{DateTime.Now.ToString("yyyyMMddHHmmss")},未获取到二级经销商,finalAllorgid：{string.Join(",", finalAllorgid)},secondAgents:{secondAgents.Count}");
                logService.WriteLogToFile(dataLog.ToString(), "二级经销商授权清理清单日志Log");
                return;
            }
            var agentIds = secondAgents.Select(p => Convert.ToString(p["fid"])).ToList();
            //获取二级门店
            List<DynamicObject> secondStore = storeService.GetStoreList(userCtx, agentIds);
            var storeIds = secondStore.Select(p => Convert.ToString(p["fid"])).ToList();
            //3. 获取一级授权清单（主子经销商的有效授权）
            List<AgentProductAuthInfor> parentIdProductAuths = GetAgentProductAuths(userCtx, parentIds);
            // 4.获取二级经销商以及二级门店授权清单
            List<DynamicObject> secondProductAuths = GetProductAuths(userCtx, new HashSet<string>(agentIds.Concat(storeIds)).ToList());
            //5. 获取二级送达方数据
            List<DynamicObject> delivers = deliverService.GetDelivers(userCtx, agentIds);
            #endregion 

            List<DynamicObject> saveProductAuths = new List<DynamicObject>();
            List<DynamicObject> savedelivers = new List<DynamicObject>();
            List<LogEntry> savelog = new List<LogEntry>();

            try
            {
                Parallel.ForEach(agentGroups, new ParallelOptions { MaxDegreeOfParallelism = 4 }, item =>
                {
                    var childIdsHash = new HashSet<string>(item.Value);
                    List<string> _secondAgentIds = secondAgents.Where(p => childIdsHash.Contains(p["forgid"])).Select(p => Convert.ToString(p["fid"])).ToList();
                    List<AgentProductAuthInfor> _parentAuths = parentIdProductAuths.Where(p => childIdsHash.Contains(p.forgid)).ToList();
                    List<DynamicObject> _clearSecondAuths = secondProductAuths.Where(p => _secondAgentIds.Contains(p["forgid"]) || _secondAgentIds.Contains((p["forgid_ref"] as DynamicObject)?["fparentid"])).ToList();
                    List<DynamicObject> _clearSecondDelivers = delivers.Where(p => _secondAgentIds.Contains(p["fagentid"])).ToList();
                    if (_clearSecondAuths.Any())
                    {
                        DealSecondAgentAuth(userCtx, _parentAuths, _clearSecondAuths, _clearSecondDelivers, addexcludeProdIds, removeexcludeProdIds, removeAuthProdIds, savelog, htmlForm, operationNo, operationName);
                        saveProductAuths.AddRange(_clearSecondAuths);
                        savedelivers.AddRange(_clearSecondDelivers);
                    }
                });
                userCtx.SaveBizData("ydj_productauth", saveProductAuths);
                userCtx.SaveBizData("bas_deliver", savedelivers);
                foreach (var item in savelog)
                {
                    logService.WriteLog(userCtx, item);
                }
                ClearPrdAuthCache(userCtx, saveProductAuths, htmlForm, operationNo);
                ProductDataIsolateHelper.ClearCacheByBiz(userCtx, new PrdDataIsolateChannelMessage
                {
                    Message = $"{htmlForm?.Caption}-{operationNo}",
                    TopCompanyId = userCtx.TopCompanyId
                });
                
            }
            catch (Exception ex)
            {
                dataLog.AppendLine($"清理二级出错,二级经销商:{string.Join(",", agentIds)},二级门店:{string.Join(",", storeIds)}清理二级分销商商品授权清单，{ex.Message}");

            }
            finally {
                dataLog.AppendLine($"ydj_productauth:{operationNo}->ClearSecondAgentAuth-end:{DateTime.Now.ToString("yyyyMMddHHmmss")}");
                logService.WriteLogToFile(dataLog.ToString(), "二级经销商授权清理清单日志Log");
            }
        }

        private void DealSecondAgentAuth(UserContext userCtx, List<AgentProductAuthInfor> parentAuths, 
            List<DynamicObject> secondAuths, List<DynamicObject> secondDelivers, List<string> addexcludeProdIds, List<string> removeexcludeProdIds, List<string> removeAuthProdIds,
            List<LogEntry> savelog, HtmlForm htmlForm = null, string operationNo = null, string operationName = null)
        {
          
            ParseBrandAndSerieInfo(parentAuths, out var brandIds, out var seriIds, out var excludeProdIds, out var includeProdIds);
            ParseBrandAndSerieInfo(secondAuths.ConvertAll(item => (AgentProductAuthInfor)item),
            out var secondbrandIds, out var secondseriIds, out var secondexcludeProdIds, out var secondincludeProdIds);
            Dictionary<string, DynamicObject> productDic = new Dictionary<string, DynamicObject>();
            Dictionary<string, string> brandNumDict = new Dictionary<string, string>();
            Dictionary<string, string> seriNumDict = new Dictionary<string, string>();
            Dictionary<string, string> seriOrgict = new Dictionary<string, string>();
            HashSet<string> brandIdsToLoad = new  HashSet<string>();
            if (secondseriIds.Count > 0) {
                var seriItems =userCtx.LoadBizDataByFilter("ydj_series", $" fid in ({ string.Join(",", secondseriIds.Select(id => $"'{id}'"))})", "fid,fnumber,fbrandid,fmainorgid");
                brandIdsToLoad = new HashSet<string>(seriItems.Select(p => Convert.ToString(p["fbrandid"])).Concat(brandIds));
                seriNumDict=seriItems.ToDictionary(p => Convert.ToString(p["fbillhead_id"]), p => Convert.ToString(p["fnumber"]));
                seriOrgict = seriItems.ToDictionary(p => Convert.ToString(p["fbillhead_id"]), p => Convert.ToString(p["fmainorgid"]));
            }

            if (brandIdsToLoad.Count > 0) {
                brandNumDict= userCtx.LoadBizDataByFilter("ydj_brand", $" fid in ({ string.Join(",", brandIdsToLoad.Select(id => $"'{id}'"))})", "fid,fnumber").ToDictionary(
                 o => Convert.ToString(o["fbillhead_id"]),
                 o => Convert.ToString(o["fnumber"]));
            }

            var allProducts = new HashSet<string>(
               excludeProdIds.Concat(secondexcludeProdIds).Concat(removeexcludeProdIds)
                             .Concat(addexcludeProdIds).Concat(removeAuthProdIds));
            if (allProducts.Count > 0)
            {
                var productList = userCtx.LoadBizDataByFilter("ydj_product", $" fid in ({ string.Join(",", allProducts.Select(id => $"'{id}'"))})", "fid,fnumber,fmainorgid").ToList();
                productDic = productList.GroupBy(item => Convert.ToString(item["fbillhead_id"])).Select(group => group.Last()).ToDictionary(item => item["fbillhead_id"].ToString(), item => item);
            }
            Parallel.ForEach(secondAuths, new ParallelOptions { MaxDegreeOfParallelism = 4 }, item =>
            {
                var fproductauthbs = item["fproductauthbs"] as DynamicObjectCollection; //授权品牌/系列
                var fproductauthexclude = item["fproductauthexclude"] as DynamicObjectCollection;  //-例外商品（不授予权限的商品）
                var fproductauthlist = item["fproductauthlist"] as DynamicObjectCollection; //商品授权
                var _refparent = item["forgid_ref"] as DynamicObject;
              
                //1. 清理系列
                List<string> removeSeries = new List<string>();
                foreach (var _seri in fproductauthbs)
                {
                    var validSeries = new List<string>();
                    var validSeriesNames = new List<string>();
                    var seriesIds = _seri["fserieid"].ToString().SplitKey();
                    var seriesNames = _seri["fserieid_txt"].ToString().SplitKey();
                    for (int i = 0; i < seriesIds.Count; i++)
                    {
                        var _ = seriOrgict.TryGetValue(seriesIds[i], out string seriOrg);
                        //只清理总部系列
                        if (seriIds.Contains(seriesIds[i])||(!seriOrg.EqualsIgnoreCase(userCtx.TopCompanyId)))
                        {
                            validSeries.Add(seriesIds[i]);
                            validSeriesNames.Add(seriesNames[i]);
                        }
                        else
                        {
                            removeSeries.Add(seriesIds[i]);
                        }
                    }
                    _seri["fserieid"] = string.Join(",", validSeries);
                    _seri["fserieid_txt"] = string.Join(",", validSeriesNames);
                }
                if (removeSeries.Count > 0)
                {
                    var delivers = secondDelivers.Where(p => p["fagentid"].ToString() == item["forgid"].ToString()).ToList();
                    foreach (var deliveritem in delivers)
                    {
                        var deliverentry = deliveritem["fentry"] as DynamicObjectCollection;
                        var _clearseris = new List<string>();
                        foreach (var _de in deliverentry)
                        {
                            if (removeSeries.Contains(_de["fserieid"].ToString()))
                            {
                                _de["fenable"] = "0";
                                var seris = _de["fserieid_ref"] as DynamicObject;
                                _clearseris.Add(seris?["fnumber"].ToString());

                            }
                        }
                        if (_clearseris.Count > 0)
                        {
                            string content = $"操作来源：{htmlForm?.Caption}({htmlForm?.Id})=>{operationName}({operationNo}){string.Join(",", _clearseris)}系列因总部取消一级代理，导致二级同步取消。";
                            savelog.Add(new LogEntry
                            {
                                BillIds = deliveritem["id"] as string,
                                BillNos = deliveritem["fnumber"] as string,
                                BillFormId = "bas_deliver",
                                OpName = "清理一级授权",
                                OpCode = "clearSecondAuth",
                                Content = content,
                                DebugData = content,
                                Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                                Level = Enu_LogLevel.Info.ToString(),
                                LogType = Enu_LogType.RecordType_03,
                            });
                        }
                    }

                }

                //2.清理只有品牌但系列为空的数据
                List<string> removeBrand = new List<string>();
                var brandRemoves = fproductauthbs.Where(p => Convert.ToString(p["fserieid"]).IsNullOrEmptyOrWhiteSpace()).ToList();
                foreach (var _brand in brandRemoves)
                {
                    removeBrand.Add(_brand["fbrandid"].ToString());
                    fproductauthbs.Remove(_brand);
                }
               

                //3.新增例外商品
                List<string> addexProducts = new List<string>();
                if (addexcludeProdIds?.Count > 0)
                {
                    var _exdata = new HashSet<string>(addexcludeProdIds);
                    foreach (var excludeProduct in _exdata)
                    {
                        if (!string.IsNullOrEmpty(excludeProduct))
                        {
                            //如果商品授权清单不存在则需要同步到商品授权清单中
                            if (!fproductauthexclude.Any(o => Convert.ToString(o["fproductid_o"]).EqualsIgnoreCase(excludeProduct)))
                            {
                                var detail_old = fproductauthexclude.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                                detail_old["fproductid_o"] = excludeProduct;
                                fproductauthexclude.Add(detail_old);
                                addexProducts.Add(excludeProduct);
                            }
                        }
                    }
                }

                //4.清理例外商品
                List<string> removeexProducts = new List<string>();
                if (removeexcludeProdIds?.Count > 0)
                {
                    var _auth = parentAuths.Where(p => p.fcityid == Convert.ToString(item["fcityid"])).ToList();
                    if (_auth != null&&_auth.Count>0)
                    {
                       // var _thatexclude = _auth.fproductauthexclude;  //-例外商品（不授予权限的商品）
                        var _makeexclude = new List<ProductAuthExclude>();
                        var _removeexdata = new HashSet<string>(removeexcludeProdIds);
                        foreach (var excludeProduct in _removeexdata)
                        {
                            if (!string.IsNullOrEmpty(excludeProduct))
                            {
                                productDic.TryGetValue(excludeProduct, out DynamicObject  product);
                                //若商品不是总部商品,直接跳过
                                if (product != null && !product["fmainorgid"].ToString().EqualsIgnoreCase(userCtx.TopCompanyId))
                                    continue;
                                var _exits= _auth.FirstOrDefault(t => t.fproductauthexclude == null || !t.fproductauthexclude.Any(child => child.fproductid_o == excludeProduct));
                                if (_exits == null)
                                    continue;
                                //如果商品授权清单存在则需要同步到商品授权清单中
                                if (fproductauthexclude.Any(o => Convert.ToString(o["fproductid_o"]).EqualsIgnoreCase(excludeProduct)))
                                {
                                    var removeDatas = fproductauthexclude.Where(_ => Convert.ToString(_["fproductid_o"]) == excludeProduct).ToList();
                                    foreach (var _removeitem in removeDatas)
                                    {
                                        removeexProducts.Add(excludeProduct);
                                        fproductauthexclude.Remove(_removeitem);
                                    }
                                }
                            }
                        }
                    }
                }

                //5 清理商品授权数据
                var removeProducts = new List<string>();
                if (removeAuthProdIds?.Count > 0) {
                    foreach (var _produt in removeAuthProdIds)
                    {
                        if (includeProdIds.Contains(_produt)) continue;
                        var removeDatas = fproductauthlist.Where(_ => Convert.ToString(_["fproductid"]) == _produt).ToList();
                        if (removeDatas.Count > 0) {
                            removeProducts.Add(_produt);
                            foreach (var _removeitem in removeDatas)
                            {
                                fproductauthlist.Remove(_removeitem);
                            }
                        }
                    }
                }
             
                var brandNumLst = removeBrand.Where(brandNumDict.ContainsKey) .Select(b => brandNumDict[b]).ToList();
                var seriesNumLst = removeSeries.Where(seriNumDict.ContainsKey) .Select(s => seriNumDict[s]) .ToList();
                var removeexproductNumLst = removeexProducts .Where(productDic.ContainsKey) .Select(p => Convert.ToString(productDic[p]["fnumber"])).ToList();
                var addexproductNumLst = addexProducts.Where(productDic.ContainsKey) .Select(p => Convert.ToString(productDic[p]["fnumber"])) .ToList();
                var removeproductNumLst = removeProducts .Where(productDic.ContainsKey).Select(p => Convert.ToString(productDic[p]["fnumber"])).ToList();

                string str = $"操作来源：{htmlForm?.Caption}({htmlForm?.Id})=>{operationName}({operationNo}),清除系列:{(seriesNumLst.Count > 0 ? string.Join(",", seriesNumLst) : "无")}；清除品牌:{(brandNumLst.Count > 0 ? string.Join(",", brandNumLst) : "无")}；清除例外商品:{(removeexproductNumLst.Count > 0 ? string.Join(",", removeexproductNumLst) : "无")};清除商品:{(removeproductNumLst.Count > 0 ? string.Join(",", removeproductNumLst) : "无")}；添加例外商品:{(addexproductNumLst?.Count > 0 ? string.Join(",", addexproductNumLst ?? new List<string>()) : "无")}";
                savelog.Add(new LogEntry
                {
                    BillIds = item["id"] as string,
                    BillNos = item["fnumber"] as string,
                    BillFormId = "ydj_productauth",
                    OpName = "清理一级授权",
                    OpCode = "clearSecondAuth",
                    Content = str,
                    DebugData = str,
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03,
                });
            });
        }

        /// <summary>
        /// 获取经销商商品授权清单数据(组织隔离)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agentIds"></param>
        /// <returns></returns>
        private List<AgentProductAuthInfor> GetAgentProductAuths(UserContext userCtx, List<string> agentIds)
        {
            List<AgentProductAuthInfor> result = new List<AgentProductAuthInfor>();
            List<DynamicObject> datas = userCtx.LoadBizDataByFilter("ydj_productauth", $" forgid in ({ string.Join(",", agentIds.Select(id => $"'{id}'")) }) and  fforbidstatus = 0",true);
            foreach (var item in datas)
            {
                result.Add(item);
            }
            return result;
        }
        /// <summary>
        /// 获取经销商商品授权清单数据(无组织隔离)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agentIds"></param>
        /// <returns></returns>
        private List<DynamicObject> GetProductAuths(UserContext userCtx, List<string> agentIds)
        {
            if (agentIds.Count <= 0) return new List<DynamicObject>();
            var dbService = userCtx.Container.GetService<IDBService>();
            var meta = HtmlParser.LoadFormMetaFromCache("ydj_productauth", userCtx);
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, meta.GetDynamicObjectType(userCtx));

            string sql = $@"select fid from t_ydj_productauth  with(nolock) 
                 where forgid in ({string.Join(",", agentIds.Select(id => $"'{id}'"))}) and fforbidstatus='0'";

            var reader = dbService.ExecuteReader(userCtx, sql, new List<SqlParam>());
            var datas = dm.SelectBy(reader)?.OfType<DynamicObject>()?.ToList();
            userCtx.Container.GetService<LoadReferenceObjectManager>().Load(userCtx, meta.GetDynamicObjectType(userCtx), datas, false);
            return datas;
        }
        /// <summary>
        /// 格式化系列品牌授权信息
        /// </summary>
        /// <param name="prdAuths"></param>
        /// <param name="brandIds"></param>
        /// <param name="seriIds"></param>
        /// <param name="excludeProdIds"></param>
        /// <param name="includeProdIds"></param>
        private void ParseBrandAndSerieInfo(List<AgentProductAuthInfor> prdAuths, out HashSet<string> brandIds, out HashSet<string> seriIds, out HashSet<string> excludeProdIds, out HashSet<string> includeProdIds)
        {
            brandIds = new HashSet<string>();
            seriIds = new HashSet<string>();
            excludeProdIds = new HashSet<string>();
            includeProdIds = new HashSet<string>();
            var brandSeries = prdAuths.SelectMany(f => f.fproductauthbs).ToList();
            foreach (var item in brandSeries)
            {
                var fbrandid = item.fbrandid;
                var fserieid = item.fserieid;
                if (fbrandid.IsNullOrEmptyOrWhiteSpace())
                {
                    if (!fserieid.IsNullOrEmptyOrWhiteSpace())
                    {
                        var _seriIds = fserieid.SplitKey();
                        foreach (var seriId in _seriIds)
                        {
                            seriIds.Add(seriId);
                        }
                    }
                }
                else
                {
                    if (fserieid.IsNullOrEmptyOrWhiteSpace())
                    {
                        brandIds.Add(fbrandid);
                    }
                    else
                    {
                        var _seriIds = fserieid.SplitKey();
                        foreach (var seriId in _seriIds)
                        {
                            seriIds.Add(seriId);
                        }
                    }
                }
            }

            var brandSeriesX = prdAuths.SelectMany(f => f.fproductauthexclude).ToList();

            // 商品授权清单
            var productAuthKv = new Dictionary<string, HashSet<string>>();
            if (brandSeriesX.Count > 0)
            {
                if (prdAuths.Count > 1)
                {
                    foreach (var item in prdAuths)
                    {
                        foreach (var items in item.fproductauthexclude.Select(o => o.fproductid_o))
                        {
                            excludeProdIds.Add(items);
                        }
                    }
                }
            }
            var products = prdAuths.SelectMany(f => f.fproductauthlist).ToList();
            foreach (var item in products)
            {
                var fproductid = item.fproductid;
                if (fproductid.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                var canPur = item.fnopurchase;
                includeProdIds.Add(fproductid);
            }
            brandSeries?.Clear();
            brandSeries = null;
            brandSeriesX?.Clear();
            brandSeriesX = null;
            products?.Clear();
            products = null;
            foreach (var item in productAuthKv)
            {
                item.Value?.Clear();
            }
            productAuthKv.Clear();
            productAuthKv = null;
        }
        #endregion
    }
}
