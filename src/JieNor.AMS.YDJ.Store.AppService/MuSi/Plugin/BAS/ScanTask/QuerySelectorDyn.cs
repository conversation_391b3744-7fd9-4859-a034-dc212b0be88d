using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.IoC;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.BAS.ScanTask
{
    /// <summary>
    /// 属性选配：动态列基础资料字段弹窗查询操作
    /// </summary>
    [InjectService]
    [FormId("bcm_countscantask|bcm_deliveryscantask|bcm_receptionscantask")]
    [OperationNo("QuerySelector")]
    public class QuerySelectorDyn : AbstractQueryDyn
    {
        //该插件功能逻辑在基类实现
    }
}