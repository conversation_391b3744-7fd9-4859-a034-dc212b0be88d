using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Pur;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.MS.API.Utils;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.Core.Helpers;
using static JieNor.AMS.YDJ.Core.Helpers.ProductDelistingHelper;
using JieNor.AMS.YDJ.Store.AppService.Service;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.PUR.PurchaseOrder
{
    /// <summary>
    /// 采购订单：提交总部
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("SubmitHQ")]
    public class SubmitHQ : AbstractOperationServicePlugIn
    {
        private List<DynamicObject> auditBeforeOrder { get; set; }

        private List<DynamicObject> dataEntitiesHq { get; set; }
        private List<DynamicObject> dataEntity_CHGHq { get; set; }

        public static bool isTopOper = false;

        /// <summary>
        /// 库存水位线计算服务
        /// </summary>
        [InjectProperty]
        protected PurchaseOrderStockWaterlineService _stockWaterlineService { get; set; }
        public override void CreateObjectIdemotency(CreateObjectIdemotencyEventArgs e)
        {
            base.CreateObjectIdemotency(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            e.IdemotencyIds = new List<string>();

            foreach (var dataEntity in e.DataEntitys)
            {
                e.IdemotencyIds.Add($"{this.HtmlForm.Id}_{this.OperationNo}_{dataEntity["fbillno"]}");
            }
        }

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fmaterialid" });
        }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
                return;
            //总部采购管理参数未开启【库存水位线控制功能（计算+校验）】
            var profileService = this.Container.GetService<ISystemProfile>();
            var topCtx = this.Context.CreateTopOrgDBContext();
            var finventorywaterlevel = profileService.GetSystemParameter(topCtx, "pur_systemparam", "finventorywaterlevel", false);
            if (finventorywaterlevel)
            {
                // 1. 批量计算所有订单的库存水位线状态
                _stockWaterlineService.BatchCalculateOrdersStockWaterline(this.Context, e.DataEntitys);
                // 2. 批量校验红线商品
                var validationResult = _stockWaterlineService.ValidateStockWaterlineComplete(this.Context, e.DataEntitys);
                this.Context.SaveBizData(this.HtmlForm.Id, e.DataEntitys);
                if (validationResult.Count() > 0)
                {
                    this.Result.IsSuccess = false;
                    this.Result.ComplexMessage.ErrorMessages.AddRange(validationResult);
                    e.Cancel = true;
                    this.AddRefreshPageAction();
                }
            }
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            //是否是《采购单重新提交总部》菜单过来的操作，此菜单允许重新下推数据至中台
            isTopOper = Convert.ToString(this.GetQueryOrSimpleParam<string>("istopoper")) == "1";

            var formPara = this.PageSession?.FormParameter;
            var errorMsg = "";
            Enu_DomainType domainType = Enu_DomainType.Bill;
            if (formPara is FormParameter)
            {
                domainType = ((FormParameter)formPara).DomainType;
            }

            e.Rules.Add(new SubmitHQValidation(domainType, isTopOper));

            e.Rules.Add(new Validation_CheckPromotion(domainType));

            string entryErrorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (this.Context.IsSecondOrg)
                {
                    return false;
                }
                return true;
            }).WithMessage("当前用户是二级分销商, 不允许提交总部！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var frenewalflag = Convert.ToBoolean(newData["frenewalflag"]);

                return !frenewalflag;

            }).WithMessage("采购订单【{0}】属于焕新采购订单，不允许<提交总部>操作，谢谢！", (billObj, propObj) => billObj["fbillno"]));

            //http://dmp.jienor.com:81/zentao/task-view-34489.html 变更中的可以提交到总部
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string status = Convert.ToString(newData["fstatus"]);
                return (status.EqualsIgnoreCase("E") || Convert.ToString(newData["fchangestatus"]) == "1");
            }).WithMessage("采购订单【{0}】未审核不允许提交至总部 !", (billObj, propObj) => propObj["fbillno"]));

            //如果【变更状态】= "变更中" 且 【总部合同状态】不等于 "已终审" 时 , 要报错提示 "当前采购订单未提交过总部, 不允许向总部进行变更 !"
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return !(Convert.ToString(newData["fhqderstatus"]) != "03" && Convert.ToString(newData["fchangestatus"]) == "1");
            }).WithMessage("采购订单【{0}】未提交过总部, 不允许向总部进行变更!", (billObj, propObj) => propObj["fbillno"]));

            //增加判断如果 【总部变更状态】="提交至总部"或"驳回"时, 点击<提交变更>要报错提示"总部变更状态等于"提交至总部"或"驳回", 不允许提交变更!"
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMsg = string.Empty;
                var entry = newData["fentity"] as DynamicObjectCollection;
                //判断明细 是否存在 【总部变更状态】="提交至总部"或"驳回"
                bool exist = entry.Any(o => Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("01") || Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("03"));
                if (!isTopOper && exist)
                {
                    errorMsg += $"总部变更状态等于'提交至总部'或'驳回', 不允许提交变更!";
                    return false;
                }
                else
                {
                    return true;
                }
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string fhqderstatus = Convert.ToString(newData["fhqderstatus"]);
                string fchangestatus = Convert.ToString(newData["fchangestatus"]);
                //string fhqderchgstatus= Convert.ToString(newData["fhqderchgstatus"]);
                if (/*isTopOper||*/fhqderstatus.IsNullOrEmptyOrWhiteSpace())
                {
                    return true;
                }
                // 【总部合同状态】=【驳回】可【提交总部】
                //注意：下处条件改动注意更改BeginOperationTransaction中dataEntity_CHG中where判断条件
                // 变更状态=变更中 且【总部合同状态】=【已终审】且
                // （fhqderchgstatus【总部变更状态】为空 或 等于"驳回"(03) 或 等于“关闭”（02）时，后续需求已要求放开，这里暂屏蔽总部变更状态判断）可【提交总部】，因为变更单触发也是在此
                if ((isTopOper && fhqderstatus.EqualsIgnoreCase("02")
                //&& (fhqderchgstatus.IsNullOrEmptyOrWhiteSpace() || fhqderchgstatus.EqualsIgnoreCase("03") || fhqderchgstatus.EqualsIgnoreCase("02"))
                )
                || (fhqderstatus.EqualsIgnoreCase("05") || (fchangestatus.EqualsIgnoreCase("1") && fhqderstatus.EqualsIgnoreCase("03"))))
                {
                    return true;
                }

                return false;
            }).WithMessage("采购订单【{0}】已提交至总部, 不允许重复提交!", (billObj, propObj) => propObj["fbillno"]));

            // 41307 采购订单的供应商与送达方不匹配，提交至总部校验，提示：当前供应商与送达方不匹配，请修改后再提交
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                IPurchaseOrderService purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();
                var flag = true;
                // 根据送达方带出供应商
                string deliverId = Convert.ToString(newData["fdeliverid"]);
                string fsupplierid = Convert.ToString(newData["fsupplierid"]);

                var supplierId = purchaseOrderService.GetSupplierIdByDeliverId(Context, deliverId);

                if (fsupplierid != supplierId)
                {
                    // http://pmp.jienor.com:9999/www/index.php?m=task&f=view&taskID=73230 优化采购订单变更校验 / 优化采购订单变更校验-开发
                    //判断送达方销售组织；-调整不校验
                    bool isNewDataPurChg = GetPurchaseOrderChgUtil.CheckPurchaseOrderIsChg(newData, isTopOper);
                    if (isNewDataPurChg)
                    {
                        flag = isNewDataPurChg;
                    }
                    else
                    {
                        flag = false;
                    }
                }

                return flag;
            }).WithMessage("当前经销商与送达方不匹配，请修改后再提交总部!"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //当采购订单【第三方来源】=“三维家”且【第三方单号】不为空时，则点击<提交总部>按钮时，
                //需要校验（【启用定制OMS】=“是”）或者（【启用定制OMS】=“否”且【门店已审核】=TRUE）才允许提交总部，
                //否则需提示：“当前采购订单三维家中未进行门店审核，不允许提交总部。”
                if (!newData["fthirdsource"].IsNullOrEmptyOrWhiteSpace() && newData["fthirdsource"].Equals("三维家") && !newData["fthirdbillno"].IsNullOrEmptyOrWhiteSpace())
                {
                    if (!Convert.ToBoolean(newData["fomsservice"]) && !Convert.ToBoolean(newData["fswjorderstate"]))
                    {
                        return false;
                    }
                }
                return true;
            }).WithMessage("当前采购订单三维家中未进行门店审核，不允许提交总部。"));
        }

        /// <summary>
        /// 判断 采购订单审核后不提交至总部，直接进行变更将采购订单数量修改成0，点击【提交变更】审核后再提交总部，跳过了采购订单变更前需先提交总部的校验
        /// http://dmp.jienor.com:81/zentao/bug-view-29857.html
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool HasChangedOrder(DynamicObject newData)
        {
            //问题场景此时的变更完成状态：变更完成
            if (Convert.ToString(newData["fchangestatus"]) == "2")
            {
                var fbillno = Convert.ToString(newData?["fbillno"]);
                var PurOrderObj = this.Context.LoadBizDataByACLFilter("ydj_purchaseorder_chg", $" fsourcenumber ='{fbillno}'").OrderByDescending(o => o["fcreatedate"]).FirstOrDefault();
                //看最新的一个变更是否审核通过，审核通过说明是问题场景，和正常的变更中的变更单提交至总部场景区分开
                if (Convert.ToString(PurOrderObj["fstatus"]).EqualsIgnoreCase("E"))
                {
                    return true;
                }
            }
            return false;
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;
            //是否是《采购单重新提交总部》菜单过来的操作，此菜单允许重新下推数据至中台
            //var isTopOper = Convert.ToString(this.GetQueryOrSimpleParam<string>("istopoper"))=="1";

            var checkResult = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, e.DataEntitys, "CheckDelisting", null);
            checkResult.ThrowIfHasError(true, $"{this.HtmlForm.Caption}{this.OperationName}失败！");

            this.auditBeforeOrder = ProductDelistingHelper.GetOrderData(this.Context, e.DataEntitys.ToList(), this.HtmlForm);
           

            //注意：BeginOperationTransaction中dataEntity_CHG中where判断条件改变后记得更改上述标注注意项，搜索PrepareValidationRules中BeginOperationTransaction校验项同步条件
            //变更中  fchangestatus【变更状态】= "变更中" 且 fhqderstatus【总部合同状态】= "已终审"
            //且 fhqderchgstatus【总部变更状态】为空 或 等于"驳回"(03) 或 等于“关闭”（02）时，后续需求已要求放开，这里暂屏蔽总部变更状态判断
            List<DynamicObject> dataEntity_CHG = e.DataEntitys.Where(o => Convert.ToString(o["fchangestatus"]) == "1"
                 && (Convert.ToString(o["fhqderstatus"]).EqualsIgnoreCase("03") || (Convert.ToString(o["fhqderstatus"]).EqualsIgnoreCase("02") && isTopOper))
                //&& (Convert.ToString(o["fhqderchgstatus"]).IsNullOrEmptyOrWhiteSpace() || Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("03") || Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("02"))
                ).ToList();

            //取差集
            var dataEntities = e.DataEntitys.Except(dataEntity_CHG);

            //走提交变更接口的话就是改总部变更状态 为 ’提交至总部‘
            foreach (var dataEntity_chg in dataEntity_CHG)
            {
                // 更新【总部变更状态】=提交至总部
                //dataEntity_chg["fhqderchgstatus"] = "01";
                if (dataEntity_chg["fhqderdate"].IsNullOrEmptyOrWhiteSpace())
                {
                    //无值才更新，有值表示已提交过，日期不做更新
                    dataEntity_chg["fhqderdate"] = DateTime.Now;
                }
                //var entrys = dataEntity_chg["fentity"] as DynamicObjectCollection;
                //foreach (var entry in entrys) {
                //    entry["fhqderchgstatus"] = "01";
                //}
            }

            //走原先的提交总部逻辑
            foreach (var dataEntity in dataEntities)
            {
                // 更新【总部合同状态】=提交至总部
                dataEntity["fhqderstatus"] = "02";
                //没有变更的情况下不管驳回几次 都以最新提交总部的时间去覆盖上一次的
                dataEntity["fhqderdate"] = DateTime.Now;
            }

            //var dm = this.GetDataManager();
            //dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            //dm.Save(e.DataEntitys);
            //List<string> combinemessage = new List<string>();
            if (e.DataEntitys.Length > 0)
            {
                //提交总部时获取的明细未根据行号排序所以导致传到采购订单保存时提示错误
                foreach (var datatentity in e.DataEntitys)
                {
                    var entity = datatentity["fentity"] as DynamicObjectCollection;
                    List<DynamicObject> entity_sort = entity.OrderBy(o => Convert.ToInt32(o["Fseq"])).ToList();
                    if (CheckIsexistLargeSuitNumber(entity))
                    {
                       
                        entity_sort = entity.OrderBy(x => x["fsofacombnumber"])
                            .ThenByDescending(x => x["fcombinenumber"])  
                            .ThenBy(x => x["fpartscombnumber"])
                            .ThenByDescending(x => x["fiscombmain"])
                            .ThenBy(x => x["fsuitcombnumber"])
                            .ThenByDescending(x => (x["fmaterialid_ref"] as DynamicObject)?["fsuiteflag"])
                             .Select((row, index) =>
                               {
                                   row["Fseq"] = index + 1;
                                   return row;
                               }).ToList();
                       
                    }
                     entity_sort = entity_sort.OrderBy(o => Convert.ToInt32(o["Fseq"])).ToList();
                     entity.Clear();
                    foreach (var fentry in entity_sort)
                    {
                        entity.Add(fentry);
                    }
                }

                //如果提交总部的时候，活动结束了，那就要清空采购订单的促销的字段

                //var date = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 00:00:00"));
                //foreach (var datatentity in e.DataEntitys)
                //{
                //    var entity = datatentity["fentity"] as DynamicObjectCollection;
                //    var combinenumberList = entity.Select(c => c["fcombinenumber"]).Distinct().ToList();
                //    if (combinenumberList != null && combinenumberList.Count() > 0)
                //    {
                //        var promotioncombine = this.Context.LoadBizDataByFilter("bas_promotioncombine", $"fnumber in('{string.Join("','", combinenumberList)}')");
                //        if (promotioncombine != null && promotioncombine.Count() > 0)
                //        {
                //            foreach (var fentry in entity)
                //            {
                //                var combine = promotioncombine.Where(c => c["fnumber"].ToString() == fentry["fcombinenumber"].ToString()).ToList();
                //                if (combine != null && combine.Count() > 0)
                //                {
                //                    var begindate = DateTime.Parse(combine[0]["fbegindate"].ToString());
                //                    var enddate = DateTime.Parse(combine[0]["fenddate"].ToString());
                //                    //套餐活动结束
                //                    if (date < begindate || date > enddate)
                //                    {
                //                        fentry["fpromotion"] = "";
                //                        fentry["fcombinenumber"] = "";
                //                        fentry["fcombineremark"] = "";
                //                        fentry["fcombineqty"] = 0;
                //                        fentry["fcombinerate"] = 0;
                //                        if (!combinemessage.Contains(combine[0]["fdescription"]))
                //                        {
                //                            combinemessage.Add(combine[0]["fdescription"].ToString());
                //                        }
                //                    }
                //                }
                //            }
                //        }
                //    }
                //}

                //foreach (var datatentity in e.DataEntitys)
                //{
                //    var entity = datatentity["fentity"] as DynamicObjectCollection;
                //    UpdatePurOrderProd(entity, combinemessage);
                //}
            }
            this.dataEntitiesHq = dataEntities.ToList();
           
            if (dataEntities.Count() > 0)
            {
                this.dataEntitiesHq = dataEntities.ToList();
                //SyncPurchaseOrder(dataEntities);
                ////SyncPurchaseOrderToAI(dataEntities);
                //this.Result.SimpleMessage = "采购订单已提交至慕思总部！";
            }

            //如果是变更中的采购订单，提交总部则去走 采购变更单的接口
            if (dataEntity_CHG.Count() > 0)
            {
                this.dataEntity_CHGHq = dataEntity_CHG;
                //SyncPurchaseOrderChg(dataEntity_CHG);
                ////如果是采购订单提交变更后 审核，需要通过 提交总部的校验 然后生成采购订单变更单并同步到中台
                ////var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_purchaseorder", DataEntity_chg, "SubmitHQ_Chg", null);
                ////result.ThrowIfHasError(true, $"{this.HtmlForm.Caption}{this.OperationName}失败！");
                //this.Result.SimpleMessage = "采购订单变更单已提交至慕思总部！";
            }

            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, dataEntities);

            this.Result.IsSuccess = true;

            this.AddRefreshPageAction();
        }

        /// <summary>
        ///新增需求 --要优先判断活动商品是否有效，如果有效就执行原逻辑，
        //存在过期的商品，如果还满足子组号的比例，则把原过期的商品去掉活动标签，
        //不存在过期的商品，则还是执行原逻辑。
        /// </summary>
        /// <param name="billno"></param>
        /// <returns></returns>
        public void UpdatePurOrderProd(DynamicObjectCollection fentry, List<string> combinemessage)
        {
            //var fentry = purchaseorder[0]["fentity"] as DynamicObjectCollection;
            var combinenumberList = fentry.Select(c => Convert.ToString(c["fcombinenumber"]).Trim()).Distinct();//已经匹配到套餐
            if (combinenumberList == null || combinenumberList.Count() <= 0) return;

            var promotioncombine = this.Context.LoadBizDataByFilter("bas_promotioncombine", $"fnumber in('{string.Join("','", combinenumberList)}')");
            var date = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));
            //记录过期商品集合

            foreach (var item in combinenumberList)
            {
                List<string> expireprod = new List<string>();
                var combine = promotioncombine.Where(c => c["fnumber"].ToString() == item).FirstOrDefault();
                if (combine == null) continue;
                //套餐明细商品
                var fcombineentry = combine["fcombineentry"] as DynamicObjectCollection;
                //订单明细商品
                var orderprod = fentry.Where(c => fcombineentry.Select(m => m["fproductid"].ToString()).Contains(c["fmaterialid"].ToString()));
                //1 先查看套餐商品是否有过期时间
                foreach (var com in fcombineentry)
                {
                    if (date < DateTime.Parse(com["fproductbegindate"].ToString()) || date >= DateTime.Parse(com["fproductenddate"].ToString()))
                    {
                        expireprod.Add(com["fproductid"].ToString());
                    }
                }
                //没有过期商品,说明套餐正常，进行下次循环判断
                if (expireprod.Count <= 0) continue;

                //有过期商品的话，则需要
                //1 判断子组号是否满足套餐，不满足套餐就要去掉套餐标签
                var combinefeoupnumber = fcombineentry.Select(c => c["fgroupnumber"].ToString()).Distinct();
                //过期商品的子组号
                //var expireprodgroupnumber = fcombineentry.Where(c => expireprod.Contains(c["fproductid"].ToString())).Select(c => c["fgroupnumber"].ToString()).Distinct();

                var orderentry = fentry.Where(c => c["fcombinenumber"].ToString() == item);
                //判断订单里面打标签的商品去掉过期商品 是否还满足套餐
                var orderotherprod = orderprod.Where(c => !expireprod.Contains(c["fmaterialid"].ToString()));
                var expireprodgroupnumber = fcombineentry.Where(c => orderotherprod.Select(m => m["fmaterialid"].ToString()).Contains(c["fproductid"].ToString())).Select(c => c["fgroupnumber"].ToString()).Distinct();

                if (combinefeoupnumber.Count() != expireprodgroupnumber.Count())
                {
                    foreach (var order in orderentry)
                    {
                        //促销活动
                        order["fpromotion"] = "";
                        //组合促销编号
                        order["fcombinenumber"] = "";
                        //描述
                        order["fcombineremark"] = "";
                        //折扣率
                        order["fcombinerate"] = 0;
                        //套餐组合基数
                        order["fcombineqty"] = 0;
                    }
                    combinemessage.Add(combine["fdescription"].ToString());
                }
                //2 满足子组号的话，则需要去掉过期商品的套餐标签
                else
                {
                    foreach (var expire in expireprod)
                    {
                        var model = orderentry.Where(c => c["fmaterialid"].ToString() == expire).FirstOrDefault();
                        if (model != null)
                        {
                            //促销活动
                            model["fpromotion"] = "";
                            //组合促销编号
                            model["fcombinenumber"] = "";
                            //描述
                            model["fcombineremark"] = "";
                            //折扣率
                            model["fcombinerate"] = 0;
                            //套餐组合基数
                            model["fcombineqty"] = 0;
                        }
                    }
                }
            }
        }


        private void SyncPurchaseOrderChg(IEnumerable<DynamicObject> purchaseOrders)
        {
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_purchaseorder_chg");
            var fieldMapObjs = this.Container.GetService<IMuSiBizObjMapService>()
                .GetBizObjMaps(this.Context, htmlForm, Enu_MuSiSyncDir.CurrentToMuSi, Enu_MuSiSyncTimePoint.SyncManual);

            if (fieldMapObjs.IsNullOrEmpty())
            {
                throw new BusinessException($"未配置{this.HtmlForm.Caption}的同步配置！");
            }
            //是否是《采购单重新提交总部》菜单过来的操作，此菜单允许重新下推数据至中台
            //var isTopOper = Convert.ToString(this.GetQueryOrSimpleParam<string>("istopoper")) == "1";

            List<DynamicObject> purchaseOrdersChg = new List<DynamicObject>();
            foreach (var purchaseOrder in purchaseOrders)
            {
                var entitys = purchaseOrder["fentity"] as DynamicObjectCollection;
                //获取最新生成的采购订单变更单
                var fbillno = Convert.ToString(purchaseOrder?["fbillno"]);
                var PurOrderObj = this.Context.LoadBizDataByACLFilter("ydj_purchaseorder_chg", $" fsourcenumber ='{fbillno}' ").OrderByDescending(o => o["fcreatedate"]).FirstOrDefault();
                if (PurOrderObj != null)
                {
                    //采购变更单提交总部是前台传的变更原因
                    var changeReason = this.GetQueryOrSimpleParam<string>("changeReason");
                    //如果是总部提交的这里不需要重新给原因了，用以前的即可
                    if (!isTopOper)
                    {
                        PurOrderObj["fchangereason"] = changeReason;
                    }

                    //to do 如果 《采购订单变更单》单据体-商品明细  【行关闭状态(新)】等于"手工关闭"  或 【变更状态】等于"删除" 时, 则给到中台的 【采购数量(新)】 为 "0"
                    var entrys = PurOrderObj["fentity"] as DynamicObjectCollection;
                    var PurOrderObjEntry = entrys.ToList();
                    entrys.Clear();

                    var productIds = PurOrderObjEntry
                                    ?.Select(o => Convert.ToString(o["fmaterialid"]))
                                    ?.Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                                    ?.Distinct()
                                    ?.ToList();

                    var productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", productIds, "fname,fsuiteflag");

                    var sofacombnumberLst = PurOrderObjEntry
                                            .Where(o => !Convert.ToString(o["fsofacombnumber"]).IsNullOrEmptyOrWhiteSpace())
                                            .Select(o => Convert.ToString(o["fsofacombnumber"])).Distinct().ToList<string>();

                    List<int> FseqFirstSofa = new List<int>();
                    foreach (var sofacombnumber in sofacombnumberLst)
                    {
                        int FseqFirst = PurOrderObjEntry.Where(f => sofacombnumber == Convert.ToString(f["fsofacombnumber"]))
                                                        .Select(o => Convert.ToInt32(o["fseq_e"])).ToList<int>().Min();
                        //找到每组沙发中 行序号最小的那个行【商品】即为沙发头
                        FseqFirstSofa.Add(FseqFirst);
                    }


                    foreach (var entry in PurOrderObjEntry)
                    {
                        var fclosestatus_e_chg = Convert.ToString(entry?["fclosestatus_e_chg"]);
                        var fclosestatus_e = Convert.ToString(entry?["fclosestatus_e"]);
                        //变更状态 entrychange_01（更新）、entrychange_02（新增）、entrychange_03（删除）
                        var fentrychange = Convert.ToString(entry?["fentrychange"]);
                        var fbizqty_chg = Convert.ToDecimal(entry["fbizqty_chg"]);
                        var fbizqty = Convert.ToDecimal(entry["fbizqty"]);

                        //http://dmp.jienor.com:81/zentao/bug-view-23982.html 忽略已经关闭或者未变动的商品行（如果只是fentrychange更新不同步过去）
                        if ((fentrychange == "entrychange_01" && fclosestatus_e_chg != "4" && fbizqty_chg == fbizqty)
                            || (fclosestatus_e_chg == "4" && fclosestatus_e == "4")) continue;
                        if (fclosestatus_e_chg == "4" || fentrychange == "entrychange_03")
                        {
                            entry["fbizqty_chg"] = 0;
                            //如果是关闭状态的明细 则把变更状态 更新为 删除，接口就会把alterFlag 传2 即为取消
                            entry["fentrychange"] = "entrychange_03";
                        }
                        //to do 明细  (【行关闭状态(新)】等于"手工关闭"  且 【行关闭状态(新)】 不等于 【行关闭状态】)
                        //                  或  (【变更状态】等于"删除" 或 "新增"  )
                        //                  或 (【采购数量(新)】 不等于 【采购数量】)
                        //  满足以上条件的商品才传给前台 因为中台只需要有变更的数据, 没变更的不要给中台
                        if ((fclosestatus_e_chg == "4" && fclosestatus_e_chg != fclosestatus_e)
                            || (fentrychange == "entrychange_02" || fentrychange == "entrychange_03")
                            || (fbizqty_chg != fbizqty))
                        {
                            ////是否套件主商品
                            //bool isSuit =Convert.ToString(productObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(entry["fmaterialid"])))?["fsuiteflag"]).EqualsIgnoreCase("1");
                            ////如果套件组合号不为空且并非套件头则 不传到中台
                            //if (!Convert.ToString(entry?["fsuitcombnumber"]).IsNullOrEmptyOrWhiteSpace() && !isSuit) 
                            //{
                            //    continue;
                            //}
                            ////如果沙发组合号不为空且并非每组沙发中的最小序号（即不为沙发头） 不传到中台
                            //if (!Convert.ToString(entry?["fsofacombnumber"]).IsNullOrEmptyOrWhiteSpace() && !FseqFirstSofa.Contains(Convert.ToInt32(entry["fseq"]))) 
                            //{
                            //    continue;
                            //}
                            //只需要将沙发头或者套件头传过去
                            entrys.Add(entry);
                        }
                    }

                    foreach (var entity in entitys)
                    {
                        if (entrys.Any(o => Convert.ToString(entity["ftranid"]).EqualsIgnoreCase(Convert.ToString(o["ftranid"]))))
                        {
                            //更新明细 总部变更行状态
                            entity["fhqderchgstatus"] = "01";
                        }
                    }
                    //处理套件、配件、沙发、促销总部变更行状态
                    ProductUtil.DoCombinesNumber(this.Context, entitys);
                    purchaseOrdersChg.Add(PurOrderObj);
                }
            }

            if (purchaseOrdersChg.Any(a => (a["fentity"] as DynamicObjectCollection).Count == 0))
            {
                throw new BusinessException($"采购订单商品明细无变化，不允许提交至总部!");
            }

            //避免后续同步失败后丢失变更单的变更原因等内容
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            dm.Save(purchaseOrdersChg);
            //目的是反写采购订单 总部变更行状态（只有减少数量或者关闭行才提交到总部）
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(purchaseOrders);


            //只传 套件头、配件头逻辑在保存完变更单号再过滤，避免变更单丢失子件的变更记录
            foreach (var purchaseOrderChg in purchaseOrdersChg)
            {
                var entrys = purchaseOrderChg["fentity"] as DynamicObjectCollection;
                var PurOrderObjEntry = entrys.ToList();
                entrys.Clear();
                var productIds = PurOrderObjEntry
                ?.Select(o => Convert.ToString(o["fmaterialid"]))
                ?.Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                ?.Distinct()
                ?.ToList();

                var sofacombnumberLst = PurOrderObjEntry
                        .Where(o => !Convert.ToString(o["fsofacombnumber"]).IsNullOrEmptyOrWhiteSpace())
                        .Select(o => Convert.ToString(o["fsofacombnumber"])).Distinct().ToList<string>();

                List<int> FseqFirstSofa = new List<int>();
                foreach (var sofacombnumber in sofacombnumberLst)
                {
                    int FseqFirst = PurOrderObjEntry.Where(f => sofacombnumber == Convert.ToString(f["fsofacombnumber"]))
                                                    .Select(o => Convert.ToInt32(o["fseq_e"])).ToList<int>().Min();
                    //找到每组沙发中 行序号最小的那个行【商品】即为沙发头
                    FseqFirstSofa.Add(FseqFirst);
                }

                var productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", productIds, "fname,fsuiteflag");
                foreach (var entry in PurOrderObjEntry)
                {
                    //是否套件主商品
                    bool isSuit = Convert.ToString(productObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(entry["fmaterialid"])))?["fsuiteflag"]).EqualsIgnoreCase("1");
                    //如果套件组合号不为空且并非套件头则 不传到中台
                    if (!Convert.ToString(entry?["fsuitcombnumber"]).IsNullOrEmptyOrWhiteSpace() && !isSuit)
                    {
                        continue;
                    }
                    //如果沙发组合号不为空且并非每组沙发中的最小序号（即不为沙发头） 不传到中台
                    if (!Convert.ToString(entry?["fsofacombnumber"]).IsNullOrEmptyOrWhiteSpace() && !FseqFirstSofa.Contains(Convert.ToInt32(entry["fseq_e"])))
                    {
                        continue;
                    }
                    //只需要将沙发头或者套件头传过去
                    entrys.Add(entry);
                }
            }
            IMuSiService muSiService = this.Container.GetService<IMuSiService>();

            var omsOrders = purchaseOrdersChg.Where(a => Convert.ToBoolean(a["fomsservice"])).ToList();
            if (omsOrders.Count > 0)
            {
                //启用了的话，需要走OMS的同步
                muSiService.OMSSyncAsync(this.Context, htmlForm, purchaseOrdersChg.Where(a => Convert.ToBoolean(a["fomsservice"])).ToList());
            }
            else
            {
                //列表同步多个采购订单到中台，如果接口发生错误中台处理不了，所以改成单个去推中台
                muSiService.SyncAsync(this.Context, htmlForm, purchaseOrdersChg.Where(a => !Convert.ToBoolean(a["fomsservice"])).ToList());
            }

        }

        /// <summary>
        /// 同步采购订单到中台
        /// </summary>
        /// <param name="purchaseOrders">采购订单</param>
        private void SyncPurchaseOrder(IEnumerable<DynamicObject> purchaseOrders)
        {
            var fieldMapObjs = this.Container.GetService<IMuSiBizObjMapService>()
                .GetBizObjMaps(this.Context, this.HtmlForm, Enu_MuSiSyncDir.CurrentToMuSi, Enu_MuSiSyncTimePoint.SyncManual);

            if (fieldMapObjs.IsNullOrEmpty())
            {
                throw new BusinessException($"未配置{this.HtmlForm.Caption}的同步配置！");
            }

            foreach (var purchaseOrder in purchaseOrders)
            {
                string fhqderstatus = Convert.ToString(purchaseOrder["fhqderstatus"]);
                //提交总部后将 fseq_e 都更新 目的是让采购订单中台驳回再审核拿到的是新的顺序
                DynamicObjectCollection entitys = purchaseOrder["fentity"] as DynamicObjectCollection;
                foreach (var entity in entitys)
                {
                    //如果是重新提交总部 且 总部合同状态为“提交至总部”且 fseq_e 不为空，则不更新固定行号，避免重复提交总部行号更新后和中台不一致。
                    if (isTopOper && fhqderstatus.EqualsIgnoreCase("02") && !Convert.ToString(entity["fseq_e"]).IsNullOrEmptyOrWhiteSpace()) continue;

                    var fseq = Convert.ToInt32(entity["fseq"]);
                    entity["fseq_e"] = fseq;
                }
            }
            foreach (var purchaseOrder in purchaseOrders)
            {
                string fhqderstatus = Convert.ToString(purchaseOrder["fhqderstatus"]);
                //提交总部后将 fseq_e 都更新 目的是让采购订单中台驳回再审核拿到的是新的顺序
                DynamicObjectCollection entitys = purchaseOrder["fentity"] as DynamicObjectCollection;
                foreach (var entity in entitys)
                {
                    if (fhqderstatus.EqualsIgnoreCase("02"))
                    {
                        var fseq = Convert.ToInt32(entity["fseq"]);
                        var fseq_e = Convert.ToInt32(entity["fseq_e"]);
                        if (fseq_e == 0)
                        {
                            entity["fseq_e"] = fseq;
                        }
                    }
                }
            }

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(purchaseOrders);

            IMuSiService muSiService = this.Container.GetService<IMuSiService>();
            //保存到中间表
            muSiService.SaveToSubMitDataTable(this.Context, this.HtmlForm.Id, purchaseOrders, !isTopOper);
            //保存后再把过滤的数据提交给中台
            foreach (var purchaseOrder in purchaseOrders)
            {
                bool has = HasChangedOrder(purchaseOrder);
                //提交总部后将 fseq_e 都更新 目的是让采购订单中台驳回再审核拿到的是新的顺序
                DynamicObjectCollection entitys_mid = purchaseOrder["fentity"] as DynamicObjectCollection;
                var entity_sort = entitys_mid.OrderBy(o => Convert.ToInt32(o["Fseq"])).ToList();
                entitys_mid.Clear();
                foreach (var entity in entity_sort)
                {
                    //如果是 采购订单审核后不提交至总部，直接进行变更将采购订单数量修改成0，点击【提交变更】审核后再提交总部，需要过滤掉数量为0的明细行
                    if (has && Convert.ToDecimal(entity["fqty"]) == 0M)
                    {
                        continue;
                    }
                    entitys_mid.Add(entity);
                }
                bool omsservice = Convert.ToBoolean(purchaseOrder["fomsservice"]);
                if (omsservice)
                {
                    //启用了的话，需要走OMS的同步
                    muSiService.OMSSyncAsync(this.Context, this.HtmlForm, new DynamicObject[] { purchaseOrder });
                }
                else
                {
                    //列表同步多个采购订单到中台，如果接口发生错误中台处理不了，所以改成单个去推中台
                    muSiService.SyncAsync(this.Context, this.HtmlForm, new DynamicObject[] { purchaseOrder });
                }
            }

        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            if (this.dataEntitiesHq != null && this.dataEntitiesHq.Count() > 0) {
                SyncPurchaseOrder(this.dataEntitiesHq);
                this.Result.SimpleMessage = "采购订单已提交至慕思总部！";
            }
            if (this.dataEntity_CHGHq != null && this.dataEntity_CHGHq.Count() > 0)
            {
                SyncPurchaseOrderChg(this.dataEntity_CHGHq);
                this.Result.SimpleMessage = "采购订单变更单已提交至慕思总部！";
            }
            //迁移到事务后反写字段，避免反写时报错导致整个事务回滚，但是接口又发到中台了。
            //反写销售合同【已提交总部或终审采购数量】
            OrderQtyWriteBackHelper.WriteBackHQPurQty(this.Context, this.HtmlForm, e.DataEntitys);
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            ProductDelistingHelper.DealDelistingDataByPurOrder(this.Context, this.HtmlForm, this.auditBeforeOrder, e.DataEntitys.ToList(), this.OperationNo);
            var fromDirectStoreSampleOption = this.GetQueryOrSimpleParam<string>("__fromdirectordersubmithqstoresimple__", "");
            SetWhenBillTypeIsStoreSampleDirectOrderSubmitHq(this.Context, fromDirectStoreSampleOption, e.DataEntitys);
        }


        /// <summary>
        /// 是否存在同一组套件商品中，子件在套件之前的情况
        /// </summary>
        /// <returns></returns>
        private bool CheckIsexistLargeSuitNumber(DynamicObjectCollection fentry)
        {
            var productIds = fentry
                            ?.Select(o => Convert.ToString(o["fmaterialid"]))
                            ?.Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                            ?.Distinct()
                            ?.ToList();
            if (productIds == null || !productIds.Any()) return false;

            var productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", productIds, "fname,fsuiteflag");
            if (productObjs == null || !productObjs.Any()) return false;

            //套件组合号
            var suitcombnumberLst = fentry
                .Where(f => Convert.ToString(productObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(f["fmaterialid"])))?["fsuiteflag"]).EqualsIgnoreCase("1"))
                .Select(o => Convert.ToString(o["fsuitcombnumber"])).Distinct().ToList<string>();

            //配件组合号
            var partscombnumberLst = fentry.Where(o => !Convert.ToString(o["fpartscombnumber"]).IsNullOrEmptyOrWhiteSpace() && string.IsNullOrWhiteSpace(Convert.ToString(o["fcombinenumber"]))).Select(o => Convert.ToString(o["fpartscombnumber"])).Distinct().ToList<string>();
            //沙发组合号
            var sofacombnumberLst = fentry.Where(o => !Convert.ToString(o["fsofacombnumber"]).IsNullOrEmptyOrWhiteSpace() && string.IsNullOrWhiteSpace(Convert.ToString(o["fcombinenumber"]))).Select(o => Convert.ToString(o["fsofacombnumber"])).Distinct().ToList<string>();

            int Mainseq_part = int.MaxValue;
            foreach (var partscombnumber in partscombnumberLst)
            {
                //获取配件主商品
                Mainseq_part = fentry.Where(f => Convert.ToBoolean(f["fiscombmain"]) && partscombnumber == Convert.ToString(f["fpartscombnumber"]))
                .Select(o => Convert.ToInt32(o["fseq"])).FirstOrDefault();

                var FseqLst = fentry.Where(f => partscombnumber == Convert.ToString(f["fpartscombnumber"]))
                     .Select(o => Convert.ToInt32(o["fseq"])).ToList<int>();
                //判断配件组合号是否连续
                if (!IsContinuous(FseqLst)) return true;

                foreach (var entry in fentry)
                {
                    if (partscombnumber.EqualsIgnoreCase(Convert.ToString(entry["fpartscombnumber"])))
                    {
                        var fseq = Convert.ToInt32(entry["fseq"]);
                        if (fseq < Mainseq_part)
                        {
                            return true;
                        }
                    }
                }
            }

            foreach (var sofacombnumber in sofacombnumberLst)
            {
                var FseqLst = fentry.Where(f => sofacombnumber == Convert.ToString(f["fsofacombnumber"]))
                                        .Select(o => Convert.ToInt32(o["fseq"])).ToList<int>();
                //判断沙发组合号是否连续
                if (!IsContinuous(FseqLst)) return true;
            }

            int Mainseq = int.MaxValue;
            foreach (var suitcombnumber in suitcombnumberLst)
            {
                Mainseq = fentry
                        .Where(f => Convert.ToString(productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(f["fmaterialid"])))?["fsuiteflag"]).EqualsIgnoreCase("1") && suitcombnumber == Convert.ToString(f["fsuitcombnumber"]))
                        .Select(o => Convert.ToInt32(o["fseq"])).FirstOrDefault();


                var FseqLst = fentry.Where(f => suitcombnumber == Convert.ToString(f["fsuitcombnumber"]))
                    .Select(o => Convert.ToInt32(o["fseq"])).ToList<int>();

                //判断是否连续
                if (!IsContinuous(FseqLst)) return true;

                foreach (var entry in fentry)
                {
                    if (suitcombnumber.EqualsIgnoreCase(Convert.ToString(entry["fsuitcombnumber"])))
                    {
                        var fseq = Convert.ToInt32(entry["fseq"]);
                        if (fseq < Mainseq)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
        /// <summary>
        /// 判断数字集合是否是连续的
        /// </summary>
        /// <returns></returns>
        private bool IsContinuous(List<int> numList)
        {
            if (numList.Count() == 1) return true;

            numList.Sort((x, y) => -x.CompareTo(y));//降序
            bool result = false;

            for (int i = 0; i < numList.Count() - 1; i++)
            {
                if (numList[i] - numList[i + 1] == 1)
                    result = true;
                else
                {
                    result = false;
                    break;
                }
            }
            return result;
        }
        ///// <summary>
        ///// 同步采购订单到AI云
        ///// </summary>
        ///// <param name="purchaseOrders"></param>
        //private void SyncPurchaseOrderToAI(IEnumerable<DynamicObject> purchaseOrders)
        //{
        //    this.Container.GetService<IMuSiAIService>().SyncToAI(this.Context, this.HtmlForm, purchaseOrders, this.Option);
        //}

        /// <summary>
        /// 设置直营销售订单单据类型为【门店上样】，需要转采购，然后再提交总部，然后把采购订单上的时间给反写到销售订单上
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="optionStr"></param>
        /// <param name="purchaseOrderDys"></param>
        public void SetWhenBillTypeIsStoreSampleDirectOrderSubmitHq(UserContext userCtx,string optionStr,DynamicObject [] purchaseOrderDys)
        {
            if(purchaseOrderDys == null || !purchaseOrderDys.Any()) return;
            if (userCtx.IsDirectSale && optionStr.IsNullOrEmptyOrWhiteSpace())
            {
                var findSourceIsOrderDys = purchaseOrderDys.Where(x=> !Convert.ToString(x["fsourcenumber"]).IsNullOrEmptyOrWhiteSpace() && Convert.ToString(x["fsourcetype"]).EqualsIgnoreCase("ydj_order"));
                if(findSourceIsOrderDys == null || !findSourceIsOrderDys.Any()) return;
                var billTypeIdList = findSourceIsOrderDys.Select(x => Convert.ToString(x["fbilltypeid"])).Distinct().ToList();
                var billTypeDys = userCtx.LoadBizBillHeadDataById("bd_billtype", billTypeIdList, "fname");
                //销售合同
                //找到单据类型为【摆场订单】的采购订单
                var tempPurchaseOrderDys = findSourceIsOrderDys.Where(x => billTypeDys.Any(y =>
                    Convert.ToString(y["fname"]).EqualsIgnoreCase("摆场订单")
                    && Convert.ToString(y["id"]).EqualsIgnoreCase(Convert.ToString(x["fbilltypeid"])))).ToList();
                if(tempPurchaseOrderDys == null || !tempPurchaseOrderDys.Any()) return;

                var purchaseOrderEntrys = tempPurchaseOrderDys.SelectMany(x=>x["fentity"] as DynamicObjectCollection).ToList();

                var orderIds = purchaseOrderEntrys.Where(x=>Convert.ToString(x["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                                                                                            && !Convert.ToString(x["fsourcebillno"]).IsNullOrEmptyOrWhiteSpace())
                                                                .Select(x=>Convert.ToString(x["fsourceinterid"]))
                                                                .Distinct()
                                                                .ToList();
                if(orderIds == null || !orderIds.Any()) return;
                var firstPurchaseOrderDy = tempPurchaseOrderDys.FirstOrDefault();
                var orgId = Convert.ToString(firstPurchaseOrderDy["fmainorgid"]);
                var agentDy = userCtx.LoadBizBillHeadDataById("bas_agent", orgId, "ftoppiecesendtag,fdirectsalesgiveawaynotzero");

                if (agentDy != null)
                {
                    // 当【创建该单据的经销商.经营类型=直营】，并且【经销商档案.一件代发标记≠勾选】，并且【销售合同.单据类型=门店上样】，手动点击<采购>下推生成创建态采购订单（无需反写SAP状态）
                    var isPieceSendTag = Convert.ToBoolean(Convert.ToInt32(agentDy["ftoppiecesendtag"]));
                    if (!isPieceSendTag)
                    {
                        return;
                    }
                }
                
                var findOrderDys = userCtx.LoadBizDataById("ydj_order", orderIds).ToList();

                foreach (var findOrderDy in findOrderDys)
                {
                    var findOrderId = Convert.ToString(findOrderDy["id"]);
                    var findPurchaseOrderEntry = purchaseOrderEntrys.FirstOrDefault(x=>Convert.ToString(x["fsourceinterid"]).EqualsIgnoreCase(findOrderId));
                    if (findPurchaseOrderEntry != null)
                    {
                        var headPurchaseOrderDy = findPurchaseOrderEntry.Parent as DynamicObject;
                        if (headPurchaseOrderDy != null)
                        {
                            //采购订单总部合同状态 '01':'新建','02':'提交至总部','03':'已终审','04':'排产中','05':'驳回'
                            var hqderStatus = Convert.ToString(headPurchaseOrderDy["fhqderstatus"]);
                            
                            //采购订单提交总部时间
                            var hqderDate = Convert.ToDateTime(headPurchaseOrderDy["fhqderdate"]);
                            
                            findOrderDy["fsubmithtime"] = hqderDate;

                            // '1':'已提交总部','2':'已驳回','3':'已终审'
                            findOrderDy["fchstatus"] = "1";
                        }
                    }
                }
                
                userCtx.SaveBizData("ydj_order", findOrderDys);
                
            }
        }
    }
}
