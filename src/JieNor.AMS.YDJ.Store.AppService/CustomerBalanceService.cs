using JieNor.AMS.YDJ.Core.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 客户帐户余额处理服务
    /// </summary>
    [InjectService]
    public class CustomerBalanceService : ICustomerBalanceService
    {
        [InjectProperty]
        protected IDBService DBService { get; set; }

        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        [InjectProperty]
        protected IMetaModelService MetaModelService { get; set; }

        public void CorrectAccountBalance(UserContext userCtx, IEnumerable<string> lstCustIds)
        {
            if (lstCustIds == null || lstCustIds.Any() == false) return;

            using (var tran = userCtx.CreateTransaction())
            {
                //todo:实现指定客户账户余额重算逻辑
                var tempTableName = this.DBService.CreateTempTableWithDataList(userCtx, lstCustIds,false);

                var strCorrectSql = $@"
                                update T_YDJ_CUSTOMER as u1 set (fbalance)=(
	                                select (frecvamount-fretamount-fconsumeamount) fblance from
		                                (
			                                select temp.fid
					                                ,isnull(u1.frecvamount,0) frecvamount
					                                ,isnull(u2.fretamount,0) fretamount
					                                ,isnull(u3.fconsumeamount,0) fconsumeamount
			                                from {tempTableName} temp 
                                            left join (select t0.fcustomerid,sum(isnull(t0.fmoney,0)) frecvamount from T_YDJ_CASH t0 where t0.fcancelstatus='0' and t0.fusage='usage_type_01' and t0.fszlx='settle_direction_001' group by t0.fcustomerid) u1
				                                on temp.fid=u1.fcustomerid
			                                left join (select t1.fcustomerid,sum(isnull(t1.fmoney,0)) fretamount from T_YDJ_CASH t1 where t1.fcancelstatus='0' and t1.fusage='usage_type_01' and t1.fszlx='settle_direction_002' group by t1.fcustomerid) u2
				                                on temp.fid=u2.fcustomerid
			                                left join (select t2.fcustomerid,sum(isnull(t2.fdepositpay,0)) fconsumeamount from T_YDJ_CASH t2 where t2.fcancelstatus='0' and t2.fusage='usage_type_02' and t2.fszlx='settle_direction_001' group by t2.fcustomerid) u3
				                                on temp.fid=u3.fcustomerid
		                                ) u2  where u1.fid=u2.fid
	                                )
                                ";
                var ret = this.DBServiceEx.Execute(userCtx, strCorrectSql);

                tran.Complete();

                if (tempTableName.IsNullOrEmptyOrWhiteSpace() == false)
                {
                    DBService.DeleteTempTableByName(userCtx, tempTableName, false);
                }
            }
        }

        public void SetAccountBalance(UserContext userCtx, Dictionary<string, decimal> dctCustAmountData, bool isOverwrite = false)
        {
            throw new NotImplementedException();
        }

        public void SetAccountBalance(UserContext userCtx, string strCustId, decimal dAmount, bool isOverwrite = false)
        {
            throw new NotImplementedException();
        }



    }
}
