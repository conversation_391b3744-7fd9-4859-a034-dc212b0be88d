using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework;
using System.Data;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.StockParam
{
    /// <summary>
    /// 获取库存信息
    /// </summary>
    [InjectService]
    [FormId("stk_stockparam")]
    [OperationNo("getstockparam")]
    public class GetStockParam : AbstractOperationServicePlugIn
    {
        protected UserContext AgentContext { get; set; }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var agentId = this.GetQueryOrSimpleParam<string>("agentid");//经销商ID
            this.AgentContext = this.Context.CreateAgentDBContext(agentId);

            //库存管理参数
            var systemProfileService = this.AgentContext.Container.BeginLifetimeScope(Guid.NewGuid().ToString()).GetService<ISystemProfile>();
            var dynamicObj = systemProfileService.GetSystemParameter(this.AgentContext, "stk_stockparam");

            var defaultstoreid = Convert.ToString(dynamicObj?["fdefaultstoreid"]);//默认仓库
            var defaultlocationid = Convert.ToString(dynamicObj?["fdefaultlocationid"]);//默认仓位
            var reserveday = Convert.ToInt32(dynamicObj?["freserveday"]);//产品预留日期=合同交货日期+
            var stocksynthesizecacheseconds = Convert.ToInt32(dynamicObj?["fstocksynthesizecacheseconds"]);//库存综合查询缓存时间
            var wmsenabledate = Convert.ToString(dynamicObj?["fwmsenabledate"]);//同步总部交货单起始日期
            var sortrule = Convert.ToString(dynamicObj?["fsortrule"]);//扫码枪商品排序规则
            var sortruleName = "默认";
            if (sortrule == "storehouse")
            {
                sortruleName = "按仓库优先级";
            }
            else if (sortrule == "storehouseloc")
            {
                sortruleName = "按仓位优先";
            }

            var allowsalescontrol = Convert.ToBoolean(dynamicObj?["fallowsalescontrol"]);//开启仓库可用控制
            var enablebarcode = Convert.ToBoolean(dynamicObj?["fenablebarcode"]);//启用条码管理系统
            var autostockinorder = Convert.ToBoolean(dynamicObj?["fautostockinorder"]);//总部发货后自动生成入库单
            var enableautostkinout = Convert.ToBoolean(dynamicObj?["fenableautostkinout"]);//一级经销出库后自动出入库
            var verifyprice = Convert.ToBoolean(dynamicObj?["fverifyprice"]);//盘点单成本默认取采购价
            var ischeckstorehouseloc = Convert.ToBoolean(dynamicObj?["fischeckstorehouseloc"]);//扫码出库启用仓位校验
            var allowinventoryclose = Convert.ToBoolean(dynamicObj?["fallowinventoryclose"]);//负库存允许库存关账
            var prohibitpdafinish = Convert.ToBoolean(dynamicObj?["fprohibitpdafinish"]);//禁止PDA盘点完成
            var pdacheckwarehousestatus = Convert.ToBoolean(dynamicObj?["fpdacheckwarehousestatus"]);//盘点启用条码仓库及状态校验

            var pdaInStockConfig = Convert.ToString(dynamicObj?["fpdainstockconfig"]);//PDA调拨入库仓库配置
            var pdaInStockConfigName = "默认调拨单仓库，不可更改";
            if (pdaInStockConfig== "allowchange")
            {
                pdaInStockConfigName = "默认调拨单仓库，可更改";
            }

            var srvData = new
            {
                DefaultStore = defaultstoreid,
                DefaultLocation = defaultlocationid,
                ReserveDay = reserveday,
                StockSynthesizecacheSeconds = stocksynthesizecacheseconds,
                WmsEnableDate = wmsenabledate,
                SortRule = new { id = sortrule, name = sortruleName },
                AllowSalesControl = allowsalescontrol,
                EnableBarcode = enablebarcode,
                AutoStockinOrder = autostockinorder,
                EnableAutoStkinOut = enableautostkinout,
                VerifyPrice = verifyprice,
                IsCheckStoreHouse = ischeckstorehouseloc,
                AllowInventoryClose = allowinventoryclose,
                ProhibitPdaFinish = prohibitpdafinish,
                PdaInStockConfig = new { id = pdaInStockConfig, name = pdaInStockConfigName },
                PdaCheckWarehouseStatus = pdacheckwarehousestatus,
            };
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage.SuccessMessages.Add("成功!");
            this.Result.SrvData = srvData;
        }
    }
}
