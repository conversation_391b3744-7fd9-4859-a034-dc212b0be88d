using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Report.IndbQty
{
    /// <summary>
    /// 准备报表动态列模型
    /// </summary>
    [InjectService]
    [FormId("rpt_indbqty")]
    [OperationNo("QueryListReport")]
    public class QueryListReport : AbstractReportServicePlugIn
    {

        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        protected override void OnIniHtmlForm(HtmlForm htmlForm)
        {
            base.OnIniHtmlForm(htmlForm);
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            if (this.PageSession == null)
            {
                throw new BusinessException("页面缓存区已回收，请关闭本页面后重新打开！");
            }
            var stockId = this.GetQueryOrSimpleParam<string>("stockId");
            var fattrinfo = this.GetQueryOrSimpleParam<string>("fattrinfo");
            var fattrinfo_e = this.GetQueryOrSimpleParam<string>("fattrinfo_e");
            var fcustomdesc = this.GetQueryOrSimpleParam<string>("fcustomdesc");
            var fmaterialid = this.GetQueryOrSimpleParam<string>("fmaterialid");
            var fstorehouseid = this.GetQueryOrSimpleParam<string>("fstorehouseid");
            var fstorelocationid = this.GetQueryOrSimpleParam<string>("fstorelocationid");
            this.PageSession.stockId = stockId;
            this.PageSession.fattrinfo = fattrinfo;
            this.PageSession.fattrinfo_e = fattrinfo_e;
            this.PageSession.fcustomdesc = fcustomdesc;
            this.PageSession.fmaterialid = fmaterialid;
            this.PageSession.fstorehouseid = fstorehouseid;
            this.PageSession.fstorelocationid = fstorelocationid;
        }
    }
}
