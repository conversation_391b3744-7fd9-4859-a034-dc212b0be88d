using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Report.OrdertoBy
{
    /// <summary>
    /// 父页面向子页面传经销商参数
    /// </summary>
    [InjectService]
    [FormId("rpt_ordertobuy")]
    [OperationNo("QueryListReport")]
    public class QueryListReport : AbstractReportServicePlugIn
    {

        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        protected override void OnIniHtmlForm(HtmlForm htmlForm)
        {
            base.OnIniHtmlForm(htmlForm);
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            //if (this.PageSession == null)
            //{
            //    throw new BusinessException("页面缓存区已回收，请关闭本页面后重新打开！");
            //}
            //var fproductIds = this.GetQueryOrSimpleParam<string>("fproductIds");
            //if (!fproductIds.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.fproductIds = fproductIds;
            //}
            //else
            //{
            //    this.PageSession.fproductIds = null;
            //}
            //var fproductIds_name = this.GetQueryOrSimpleParam<string>("fproductIds_name");
            //if (!fproductIds_name.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.fproductIds_name = fproductIds_name;
            //}
            //else
            //{
            //    this.PageSession.fproductIds_name = null;
            //}
            //var fdatefrom = this.GetQueryOrSimpleParam<string>("fdatefrom");
            //if (!fdatefrom.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.fdatefrom = fdatefrom;
            //}
            //else
            //{
            //    this.PageSession.fdatefrom = null;
            //}

            //var fdateto = this.GetQueryOrSimpleParam<string>("fdateto");
            //if (!fdateto.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.fdateto = fdateto;
            //}
            //else
            //{
            //    this.PageSession.fdateto = null;
            //}

            //var fwarehousetype = this.GetQueryOrSimpleParam<string>("fwarehousetype");//仓库类型
            //if (!fwarehousetype.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.fwarehousetype = fwarehousetype;
            //}
            //else
            //{
            //    this.PageSession.fwarehousetype = null;
            //}


            //var forderstatus = this.GetQueryOrSimpleParam<string>("forderstatus");//订单数据状态
            //if (!forderstatus.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.forderstatus = forderstatus;
            //}
            //else
            //{
            //    this.PageSession.forderstatus = null;
            //}

            //var fisbz = this.GetQueryOrSimpleParam<string>("fisbz");//非标商品
            //if (!fisbz.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.fisbz = fisbz;
            //}
            //else
            //{
            //    this.PageSession.fisbz = null;
            //}

            //var fisdz = this.GetQueryOrSimpleParam<string>("fisdz");//定制商品
            //if (!fisdz.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.fisdz = fisdz;
            //}
            //else
            //{
            //    this.PageSession.fisdz = null;
            //}

            //var fcategoryid = this.GetQueryOrSimpleParam<string>("fcategoryid");//商品类别
            //if (!fcategoryid.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.fcategoryid = fcategoryid;
            //}
            //else
            //{
            //    this.PageSession.fcategoryid = null;
            //}
            //var fcategoryid_txt = this.GetQueryOrSimpleParam<string>("fcategoryid_txt");//商品类别
            //if (!fcategoryid_txt.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.fcategoryid_txt = fcategoryid_txt;
            //}
            //else
            //{
            //    this.PageSession.fcategoryid_txt = null;
            //}

            //var fisshow = this.GetQueryOrSimpleParam<string>("fisshow");//无建议采购量不显示
            //if (!fisshow.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.fisshow = fisshow;
            //}
            //else
            //{
            //    this.PageSession.fisshow = null;
            //}

            //var fsupplierid = this.GetQueryOrSimpleParam<string>("fsupplierid");//供应商
            //if (!fsupplierid.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.fsupplierid = fsupplierid;
            //}
            //else
            //{
            //    this.PageSession.fsupplierid = null;
            //}
            //var fsupplierid_txt = this.GetQueryOrSimpleParam<string>("fsupplierid_txt");//供应商
            //if (!fsupplierid.IsNullOrEmptyOrWhiteSpace())
            //{
            //    this.PageSession.fsupplierid_txt = fsupplierid_txt;
            //}
            //else
            //{
            //    this.PageSession.fsupplierid_txt = null;
            //}

        }
    }
}
