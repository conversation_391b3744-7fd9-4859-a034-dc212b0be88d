using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.DataTransferObject;

namespace JieNor.AMS.YDJ.Store.AppService.Report.PurchaseExecute
{
    /// <summary>
    /// 采购订单执行情况报表：准备报表动态列模型
    /// </summary>
    [InjectService]
    [FormId("rpt_purchaseexecute")]
    [OperationNo("QueryListReport")]
    public class QueryListReport : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            //向当前报表模型中增加一个自定义字段
            //this.GetOrAddReportField("ftestfield", "测试字段", HtmlElementType.HtmlField_TextField);
        }
    }
}