/**
 * 数据同步结果
 * @ sourceURL=/fw/js/is/si_operationlogretry.js
 */
; (function () {
    var si_operationlogretryretry = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);

        //编辑页面初始化后触发
        _child.prototype.onBillInitialized = function (e) {
            var that = this;

        };

        //表格行按钮点击时触发
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            switch (e.btnid.toLowerCase()) {
                case 'detail':
                    that.showDetail(e);
                    break;
            }
        };

        //显示详情对话框
        _child.prototype.showDetail = function (e) {
            yiDialog.d({
                id: 'sync_log_detail',
                type: 1,
                resize: false,
                maxmin: false,
                title: '日志详情',
                content: '<div style="padding-bottom:15px;">' + e.data.flogcontent + '</div>',
                area: ['800px', '500px'],
                btn: ['确定'],
                yes: function (index, layero) {
                    layer.close(index);
                }
            });
        };

        return _child;
    })(BasePlugIn);
    window.si_operationlogretry = si_operationlogretry;
})();