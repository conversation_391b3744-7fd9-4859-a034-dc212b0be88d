/*
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/ydj/sal/ydj_closedaccounts.js
 */
; (function () {
    var ydj_closedaccounts = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

            that.accountFieldKeys = [];
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************

        //初始化页面插件
        _child.prototype.onBillInitialized = function (args) {

        };


        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            debugger
            e.id = e.id.toLowerCase();
            switch (e.id) {
                case 'fclosuredate':
                    e.result = true;
                    debugger
                    var date = new Date() // 获取时间
                    var year = date.getFullYear(); // 获取年
                    var month = date.getMonth() + 1;// 获取月
                    var strDate = date.getDate(); // 获取日
                    var flastclosuredate = that.Model.getValue({ id: 'fclosuredate' });
                    if (new Date(flastclosuredate) > date) {
                        yiDialog.mt({ msg: '不允许大于当前日期！', skinseq: 2 });
                        that.Model.setValue({ id: 'fclosuredate', value: '' })
                        that.Model.setValue({ id: 'fclosuredate', value: '' })
                        //that.Model.setValue({ id: 'fclosuredate', value: '' })
                        //that.Model.setValue({ id: 'fclosuredate', value: '' });
                    }
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_closedaccounts = window.ydj_closedaccounts || ydj_closedaccounts;
})();