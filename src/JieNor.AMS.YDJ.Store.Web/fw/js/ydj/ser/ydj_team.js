/// <reference path="/fw/js/basepage.js" />
//@ sourceURL=/fw/js/ydj/ser/ydj_team.js
; (function () {
    var ydj_team = (function (_super) {
    	//构造函数
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);
        
        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.staffEntryId = 'fstaffentry';
        
        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {

        };
        
        //初始化列表页面插件
        _child.prototype.onListInitialized = function (args) {

        };
        
         //创建明细表格
        _child.prototype.onCreateGrid = function (args) {
            if (!args.id) return;
            switch (args.id.toLowerCase()) {
                case this.staffEntryId:
                    args.result = { multiselect: false, rownumbers: false };
                    break;
            }
        };
        


        
        _child.prototype.onBeforeDoOperation = function (args) {
            var that = this;
            if (args.opcode.toLowerCase() == "save") {
                var count = that.Model.getEntryData({ id: that.staffEntryId }).filter(function (item) {
                    return item.fstaffid_e.id
                }).length;
                that.Model.setValue({ id: "fmembercount", value: count })
            }
        };
        
        return _child;
    })(BasePlugIn);
    window.ydj_team = window.ydj_team || ydj_team;
})();
