///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/ste/ste_saleinvoice_param.js
*/
; (function () {
    var ste_saleinvoice_param = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            _super.prototype.onInitialized.call(that, e);
        };

        return _child;
    })(BasePlugIn);
    window.ste_saleinvoice_param = window.ste_saleinvoice_param || ste_saleinvoice_param;
})();