; (function () {
    var ydj_prosend = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);
		
		//初始化编辑页面插件
        _child.prototype.onInitialized  = function (args) {
        	var that = this;
        	var sendObj = that.Model.getSimpleValue({ id: 'fsendobj' });
			that.procsendObjOp(sendObj);
        };
		
        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.sendcustomerEntryId = 'fsendcustomerentry';
        _child.prototype.sendsupplierEntryId = 'fsendsupplierentry';
		
		//字段值改变时
		_child.prototype.onFieldValueChanged = function (e) {
            var that = this;
			switch (e.id.toLowerCase()) {
			    case 'fsendobj':
			        that.procsendObjOp(e.value);
			        break;
			};
        };
		
		//如果已经关联了企业则按钮变为查看详情
        _child.prototype.procsendObjOp = function (sendObj) {
            var that = this;
            var sendObj = that.Model.getSimpleValue({ id: 'fsendobj' });
        	if(sendObj == 'sendpro_01'){
        		that.Model.setVisible({ id: '.y-sendcustomer', value: true });
        		that.Model.setVisible({ id: '.y-sendsupplier', value: false });
        		that.Model.resizeEntryWidth({ id: this.sendcustomerEntryId });
        		that.Model.setText({ id:'#send_objtext',value:'客户' });
        		that.Model.setValue({ id:'ftype',value:'customer' });
        		that.customerQuery();
        	}else if(sendObj == 'sendpro_02'){
        		that.Model.setVisible({ id: '.y-sendcustomer', value: false });
        		that.Model.setVisible({ id: '.y-sendsupplier', value: true });
        		that.Model.resizeEntryWidth({ id: this.sendsupplierEntryId });
        		that.Model.setText({ id:'#send_objtext',value:'供应商' });
        		that.Model.setValue({ id:'ftype',value:'supplier' });
        		that.supplierQuery();
        	};
        };
		
        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.sendcustomerEntryId:
                case this.sendsupplierEntryId:
                    e.result = { rownumbers: false };
                    break;
            }
        };
		
		//处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
             	case 'prosendcancel':
                    //点击取消关闭协同发布弹窗
                    args.result = true;
                    that.Model.close();
                    break;
                case 'prosendsave':
                    //保存发布操作
                    args.result = true;
                	that.synProduct();
                    break;
            }
        };
		
		
		//查询所有已协同客户
		_child.prototype.customerQuery = function (e) {
			var that = this;
			//列表数据对象
    		var customerEntryData = that.Model.getEntryData({ id: that.sendcustomerEntryId});
    		var supplierEntryData = that.Model.getEntryData({ id: that.sendsupplierEntryId});
    		var selectObj = that.Model.getSelectRows({id:that.sendsupplierEntryId});
			selectObj.length = 0;
			var url = '/bill/ydj_customer?operationno=getSynCustomer',
            param = {
                simpledata: {
				 	
                }
            };
            yiAjax.p(url, param, function (r) {
		        var sendobj = r.operationResult.srvData;
		        if(sendobj.length == 0){
		        	yiDialog.mt({msg:'没有找到已协同的客户！', skinseq: 2});
		        }else{
		        	//避免搜索时不断增加数据清空数据表格
	        		if (customerEntryData) {
	                    //清空数据源
	                    customerEntryData.length = 0;
	                    //刷新表格
	                    that.Model.refreshEntry({ id: that.sendcustomerEntryId });
	                }
	        		if (supplierEntryData) {
	                    //清空数据源
	                    supplierEntryData.length = 0;
	                    //刷新表格
	                    that.Model.refreshEntry({ id: that.sendsupplierEntryId });
	                }
	        		var customerlist = [];
	        		for(var i=0;i<sendobj.length;i++){
	        			customerlist.push({"fid":sendobj[i].fid,"fcustomername":sendobj[i].fname});
	                	customerEntryData.push(customerlist[i]);
	        		}
	        		//渲染数据
	                that.Model.refreshEntry({id:that.sendcustomerEntryId});
		        }
		    },
            function (m) {
                that.Model.unblockUI({ id: '#page#' });
                yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
            });
		};
		
		
		
		//查询所有已协同供应商
		_child.prototype.supplierQuery = function (e) {
			var that = this;
			//列表数据对象
    		var supplierEntryData = that.Model.getEntryData({ id: that.sendsupplierEntryId});
    		var customerEntryData = that.Model.getEntryData({ id: that.sendcustomerEntryId});
    		var selectObj = that.Model.getSelectRows({id:that.sendcustomerEntryId});
			selectObj.length = 0;
			var url = '/bill/ydj_supplier?operationno=getSynSupplier',
            param = {
                simpledata: {
				 	
                }
            };
            yiAjax.p(url, param, function (r) {
		        var sendobj = r.operationResult.srvData;
		        if(sendobj.length == 0){
		        	yiDialog.mt({msg:'没有找到已协同的供应商！', skinseq: 2});
		        }else{
		        	//避免搜索时不断增加数据清空数据表格
	        		if (supplierEntryData) {
	                    //清空数据源
	                    supplierEntryData.length = 0;
	                    //刷新表格
	                    that.Model.refreshEntry({ id: that.sendsupplierEntryId });
	                }
	        		//避免搜索时不断增加数据清空数据表格
	        		if (customerEntryData) {
	                    //清空数据源
	                    customerEntryData.length = 0;
	                    //刷新表格
	                    that.Model.refreshEntry({ id: that.sendcustomerEntryId });
	                }
	        		var supplierlist = [];
	        		for(var i=0;i<sendobj.length;i++){
	        			supplierlist.push({"fid":sendobj[i].fid,"fsuppliername":sendobj[i].fname});
	                	supplierEntryData.push(supplierlist[i]);
	        		}
	        		//渲染数据
	                that.Model.refreshEntry({id:that.sendsupplierEntryId});
		        }
        		
		    },
            function (m) {
                that.Model.unblockUI({ id: '#page#' });
                yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
            });
		};
		
		//保存发布操作
		_child.prototype.synProduct = function (e) {
			var that = this;
			var selectData =[],
				customerData = that.Model.getSelectRows({id:that.sendcustomerEntryId}),
				supplierData = that.Model.getSelectRows({id:that.sendsupplierEntryId}),
				sendType = that.Model.getValue({ id:'ftype'});
			if(sendType == 'customer'){
				selectData.push(customerData);
			}else if(sendType == 'supplier'){
				selectData.push(supplierData);
			}
			var RowData = [];
			for(var i = 0;i < selectData["0"].length;i++){
				var RowsId = selectData["0"][i].data.fid;
				RowData.push(RowsId);
			}
			if(that.Model.uiData.domainType == 'list'){
				
				if(selectData["0"].length>0){
					var simData = [];
					for(var j=0;j<that.Model.uiData["0"].length;j++){
						var simId = that.Model.uiData["0"][j].pkValue;
						simData.push(simId);
					}
					//选中行
		    	    var url = '/bill/ydj_product?operationno=synProduct',
		            param = {
		                simpledata: {
		                	id: simData.join(","),
						 	type: sendType,
						 	selectedRows: RowData.join(",")
		                }
		            };
		            yiDialog.c('确定要保存当前发布操作？',function(){
		            	yiAjax.p(url, param, function (r) {
		            		if(r.operationResult.isSuccess == true){
		            			yiDialog.mt({msg:'保存发布成功！', skinseq: 1});
		            		}
			            },
		                function (m) {
		                    that.Model.unblockUI({ id: '#page#' });
		                    yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
		                });
		            });
				}else if(selectData["0"].length==0 && sendType == 'customer'){
					yiDialog.mt({msg:'请选择发布的客户！', skinseq: 2});
				}
				else if(selectData["0"].length==0 && sendType == 'supplier'){
					yiDialog.mt({msg:'请选择发布的供应商！', skinseq: 2});
				}
			}
			
			if(that.Model.uiData.domainType == 'bill'){
				if(selectData["0"].length>0){
					var simData = that.Model.uiData.billId;
					var url = '/bill/ydj_product?operationno=synProduct',
			            param = {
			                simpledata: {
			                	id: simData,
							 	type: sendType,
							 	selectedRows: RowData.join(",")
			                }
			            };
		            yiDialog.c('确定要保存当前发布操作？',function(){
		            	yiAjax.p(url, param, function (r) {
		            		if(r.operationResult.isSuccess == true){
		            			yiDialog.mt({msg:'保存发布成功！', skinseq: 1});
		            		}
			            },
		                function (m) {
		                    that.Model.unblockUI({ id: '#page#' });
		                    yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
		                });
		            });
				}else if(selectData["0"].length==0 && sendType == 'customer'){
					yiDialog.mt({msg:'请选择发布的客户！', skinseq: 2});
				}
				else if(selectData["0"].length==0 && sendType == 'supplier'){
					yiDialog.mt({msg:'请选择发布的供应商！', skinseq: 2});
				}
				
			}   
		}
		
        return _child;
    })(BasePlugIn);
    window.ydj_prosend = window.ydj_prosend || ydj_prosend;
})();