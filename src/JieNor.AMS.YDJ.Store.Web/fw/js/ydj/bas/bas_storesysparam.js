///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/bas/bas_storesysparam.js
*/
; (function () {
    var bas_storesysparam = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode) {
                //保存操作后清除本地缓存数据
                case 'save':
                    localStorage.removeItem("storesysparam");
                    break;
            }
        };

        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            var v = that.Model.getValue({ id: "fcustomerunique" });
            v = v && v.length > 0 ? v.split(',') : ['fphone'];
            that.Model.getEleMent({ id: '[name=fcustomerunique]' }).attr('multiple', 'multiple').select2({ closeOnSelect: false }).select2('val', v);

            var a = that.Model.getValue({ id: "fcustomerarea" });
            var p = that.Model.getEleMent({ id: '[name=fcustomerarea]' }).attr('multiple', 'multiple').select2({ closeOnSelect: false });
            if (a && a.length > 0) {
                a = a.split(',');
                p.select2('val', a);
            }

            var customerrecorduniqueVal = that.Model.getValue({ id: "fcustomerrecordunique" });
            that.Model.getEleMent({ id: '[name=fcustomerrecordunique]' }).attr('multiple', 'multiple').select2({ closeOnSelect: false }).select2('val', customerrecorduniqueVal.split(','));

            var fporeserveprops = that.Model.getValue({ id: "fporeserveprops" });
            that.Model.getEleMent({ id: '[name=fporeserveprops]' }).attr('multiple', 'multiple').select2({ closeOnSelect: false }).select2('val', fporeserveprops.split(','));

            var flag = that.Model.getValue({ id: 'fcanremovetail' });
            that.setCheckEnable(flag);
            that.IsTopOrgid();
        };

        //控制复选框禁用状态
        _child.prototype.setCheckEnable = function (e) {
            var that = this;
            that.Model.setEnable({ id: 'fremovedecimal', value: e });
            that.Model.setEnable({ id: 'fremoveunidigit', value: e });
            that.Model.setEnable({ id: 'fremovetens', value: e });
            that.Model.setEnable({ id: 'fremovehundred', value: e });
            that.Model.setEnable({ id: 'fremovethousand', value: e });
        }

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fcanremovetail':
                    that.Model.setValue({ id: 'fremovedecimal', value: e.value });
                    that.Model.setValue({ id: 'fremoveunidigit', value: e.value });
                    that.Model.setValue({ id: 'fremovetens', value: e.value });
                    that.Model.setValue({ id: 'fremovehundred', value: e.value });
                    that.Model.setValue({ id: 'fremovethousand', value: e.value });
                    that.setCheckEnable(e.value);
                    break;
                case 'fproportionoratioamount':
                    debugger;
                    if (e.value < 0 || e.value > 100) {
                        yiDialog.mt({ msg: '销售合同允许出库的金额比例为1~100之间！', skinseq: 2 });
                        setTimeout(function () {
                            that.Model.setValue({ id: 'fproportionoratioamount', value: 100 });
                        }, 10);
                    }
                    break;
                case 'fproportionbuyeramount':
                    if (e.value < 0 || e.value > 100) {
                        yiDialog.mt({ msg: '销售合同允许采购的金额比例1~100之间！', skinseq: 2 });
                        setTimeout(function () {
                            that.Model.setValue({ id: 'fproportionbuyeramount', value: 100 });
                        }, 10);
                    }
                    break;
                case 'foptionalattrnumber':
                    if (e.value <= 0 || e.value > 100) {
                        yiDialog.mt({ msg: '商品非标选配属性值默认个数显示为1~100之间！', skinseq: 2 });
                        setTimeout(function () {
                            that.Model.setValue({ id: 'foptionalattrnumber', value: 1 });
                        }, 10);
                    }
                    break;
                    
            }
        }

        //字段值改变前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fcustomerunique':
                    debugger;
                    //e.value = that.Model.getValue({ id: 'fcustomerunique' })
                    if (e.value.indexOf('phone') < 0) {
                        e.result = false;
                        var fcustomerunique = e.value.length > 0 ? e.value + ',fphone' : 'fphone';
                        that.Model.setValue({ id: 'fcustomerunique', value: fcustomerunique });
                        var v = fcustomerunique.split(',');
                        yiDialog.m({ msg: "手机号必须是唯一条件！" });
                        that.Model.getEleMent({ id: '[name=fcustomerunique]' }).attr('multiple', 'multiple').select2({ closeOnSelect: false }).select2('val', v);
                    }
                    break;
            }
        }

        _child.prototype.onMenuItemClick = function (e) {
            debugger;
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'save':
                    var fcustomerunique = that.Model.getValue({ id: 'fcustomerunique' });
                    if (fcustomerunique.indexOf('phone') < 0) {
                        fcustomerunique += ',fphone';
                        that.Model.setValue({ id: 'fcustomerunique', value: fcustomerunique });
                    }
                    break;
            }
        }

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;

            switch (e.opcode) {
                case 'istoporgid':
                    //如果是总部用户则隐藏
                    if (!srvData) {
                        $("#" + that.formContext.pageId).find("input[name=fnodealprice]").parent().parent().parent().show();
                    }
                    break;
            }
        }

        //是否总部账号
        _child.prototype.IsTopOrgid = function () {
            var that = this;
            that.Model.invokeFormOperation({
                id: 'istoporgid',
                opcode: 'istoporgid',
                param: {
                    formId: 'pur_systemparam'
                }
            });
        }

        return _child;
    })(ParameterPlugIn);
    window.bas_storesysparam = window.bas_storesysparam || bas_storesysparam;
})();