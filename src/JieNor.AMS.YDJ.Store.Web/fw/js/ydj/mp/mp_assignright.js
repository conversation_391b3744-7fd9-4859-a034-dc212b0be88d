///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/mp/mp_assignright.js
*/
; (function () {
    var mp_assignright = (function (_super) {
        var _child = function (args) {
            var that = this;
            that.roles = [];
            that.role = {};
            that.currentNode;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.entryId = 'fentity';
        _child.prototype.roleTree = 'roletree';

        //初始化动态表单插件
        _child.prototype.onInitialized = function (e) {
            var that = this;

            //加载角色
            that.Model.invokeFormOperation({
                id: 'getpermrole',
                opcode: 'getpermrole'
            });
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.entryId:
                    e.result = { multiselect: false };
                    break;
            }
        };

        //处理树形控件的初始化过程
        _child.prototype.onCreateTreeList = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.roleTree:
                    e.result = {
                        view: {
                            selectedMulti: false
                        },
                        edit: {
                            enable: false,
                            editNameSelectAll: true,
                            renameTitle: "编辑",
                            showRenameBtn: function (treeId, treeNode) {
                                if (treeNode.isParent) {
                                    return false;
                                }
                                return true;
                            },
                            removeTitle: '删除',
                            showRemoveBtn: function (treeId, treeNode) {
                                if (treeNode.isParent) {
                                    return false;
                                }
                                return true;
                            }
                        },
                        data: {
                            simpleData: {
                                enable: true
                            }
                        }
                    };
                    break;
            }
        };

        //表单元素被单击后
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode) {
                case 'rolenew':
                    e.result = true;
                    that.Model.showBill({
                        formId: 'sec_role',
                        openStyle: "modal"
                    });
                    break;
                case 'permsave':
                    e.result = true;
                    that.save();
                    break;
                case 'permfresh':
                    e.result = true;
                    that.loadUserPermission('true');

                    //加载角色
                    that.Model.invokeFormOperation({
                        id: 'getpermrole',
                        opcode: 'getpermrole'
                    });
                    break;
                // case 'allot':
                //     e.result = true;
                //     that.allotRole();
                //     break;
            }
        };

        // //处理树形控件节点名称的编辑事件
        // _child.prototype.onTreeNodeEditing = function (e) {
        //     var that = this;
        //     if (!e.id) return;
        //     switch (e.id.toLowerCase()) {
        //         case that.roleTree:
        //             e.result = false;
        //             that.Model.showBill({
        //                 openStyle: Consts.openStyle.modal,
        //                 formId: 'sec_role',
        //                 pkids: [e.node.fbillhead_id]
        //             });
        //             break;
        //     }
        // };

        // //处理树形控件节点的删除事件
        // _child.prototype.onTreeNodeDeleting = function (e) {
        //     var that = this;
        //     if (!e.id) return;
        //     switch (e.id.toLowerCase()) {
        //         case that.roleTree:
        //             e.result = false;
        //             that.Model.invokeFormOperation({
        //                 id: 'delete',
        //                 opcode: 'delete',
        //                 opctx: { node: e.node.id },
        //                 selectedRows: [{ PKValue: e.node.fbillhead_id }],
        //                 param: {
        //                     domainType: Consts.domainType.list,
        //                     formId: 'sec_role'
        //                 }
        //             });
        //             break;
        //     }
        // };

        //处理树形控件节点的点击事件 
        _child.prototype.onTreeNodeClick = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case that.roleTree:
                    if (!e.node.isParent) {
                        that.currentNode = e.node;
                        that.loadUserPermission();
                    }
                    break;
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case "fuserid":
                    var userCompany = $.trim(e.value.id) ? Consts.loginCompany.name : '';
                    that.Model.setValue({ id: 'fusercompany', value: userCompany, row: e.row });
                    break;
            }
        };

        //加载角色用户菜单
        _child.prototype.loadUserPermission = function (forceNoCache) {
            var that = this;
            var node = that.currentNode;
            if (!node) return;
            var roleId = node.fbillhead_id;
            that.role = { fbillhead_id: roleId };
            for (var i = 0; i < that.roles.length; i++) {
                that.roles[i].isEdit = false;
                if (that.roles[i].fbillhead_id == roleId) {
                    that.role = that.roles[i];
                }
            }
            that.Model.invokeFormOperation({
                id: 'getrolepermission',
                opcode: 'getrolepermission',
                param: {
                    domainType: Consts.domainType.dynamic,
                    roleId: roleId,
                    forceNoCache: forceNoCache || ''
                }
            });
        };

        // //填充用户明细列表
        // _child.prototype.fillUserEntry = function () {
        //     var that = this;
        //     var users = that.Model.getEntryData({ id: that.entryId });
        //     if (users) {
        //         users.length = 0;
        //         var roleUsers = that.role.perm.roleUsers;
        //         //如果后端没有返回数据，则默认加一空行
        //         if ($.isEmptyObject(roleUsers)) {
        //             that.Model.addRow({ id: that.entryId });
        //         } else {
        //             var userInfo = that.role.perm.userInfo;
        //             for (var userId in roleUsers) {
        //                 var userName = roleUsers[userId];
        //                 var userEntry = {
        //                     fuserid: { id: userId, fnumber: userName, fname: userName },
        //                     fusername: userName,
        //                     fusercompany: Consts.loginCompany.name
        //                 };
        //                 //填充用户信息
        //                 if (userInfo) {
        //                     for (var i = 0; i < userInfo.length; i++) {
        //                         if (userInfo[i].userid === userId) {
        //                             userEntry.fuserid.fnumber = userInfo[i].usernumber;
        //                             break;
        //                         }
        //                     }
        //                 }
        //                 users.push(userEntry);
        //             }
        //         }
        //         that.Model.refreshEntry({ id: that.entryId });
        //     }
        // };

        //渲染底部导航栏菜单
        _child.prototype.drewPermPanel = function () {
            var that = this;
            var htmlTabbar = '';
            // 遍历 tabbar
            var perms = that.role.perm.mpMenuPermission;
            for (var i = 0; i < perms.length; i++) {
                var perm = perms[i];
                var groups = perm.groups;
                var _flag = true;
                console.log(12)
                for (var _i = 0; _i < groups.length; _i++) {
                    var menus = groups[_i].menus;
                    if (menus) {
                        for (var _m = 0; _m < menus.length; _m++) {
                            if (menus[_m].isAllow === false) {
                                _flag = false;
                                break;
                            }
                        }
                    }
                }
                htmlTabbar += '<div class="portlet col-md-12 box yellow-casablanca"><div class="portlet-title">';
                htmlTabbar += '<label class="checkbox-inline"><div class="input-icon right"><div class="checker">';
                if (_flag) {
                    htmlTabbar += '<span class=" {0} checked" flag="{0}">'.format(perm.tabbar);
                } else {
                    htmlTabbar += '<span class="left {0}" flag="{0}">'.format(perm.tabbar);
                }
                htmlTabbar += '<input type="checkbox" style="margin-left: -10px;" optype="tabbarcheck"  data-param="dataBiz:\'{0}\'">'.format(perm.tabbar);
                htmlTabbar += '</span></div>{0}</div></label>'.format(perm.tabbar);
                htmlTabbar += '<div class="tools">';
                //第一个默认展开，其他底部导航栏默认收起来
                if (i == 0) {
                    htmlTabbar += '<a class="collapse"></a></div></div><div class="portlet-body col-md-12 form" style="display: block;"><div class="form-body col-md-12">';
                } else {
                    htmlTabbar += '<a class="expand"></a></div></div><div class="portlet-body col-md-12 form" style="display: none;"><div class="form-body col-md-12">';
                }
                htmlTabbar += '<table class="sec-box" style="width:100%">';

                //遍历 group
                var htmlGroup = '';
                var groups = perm.groups;
                for (var j = 0; j < groups.length; j++) {
                    var group = groups[j];

                    //遍历 menu
                    var menu = group.menus;
                    var isAllow = true;
                    for (var k = 0; k < menu.length; k++) {
                        if (!menu[k].isAllow) {
                            isAllow = false;
                        }
                    }

                    var $allCheck = '<label class="checkbox-inline"><div class="input-icon right"><div class="checker">';
                    if (isAllow) {
                        $allCheck += '<span class="left checked ' + perm.tabbar + ' ' + group.group + ' ' + i + '-' + j + '">';
                    } else {
                        $allCheck += '<span class="left ' + perm.tabbar + ' ' + group.group + ' ' + i + '-' + j + '">';
                    }
                    $allCheck += '<input type="checkbox" style="margin-left: -10px;" optype="allcheck" data-param="dataBiz:\'{0}\',dataPerm:\'{1}\',dataInxs:\'{2}\',dataModel:\'{3}\'">'.format(group.group, "biz", i + '-' + j, perm.tabbar);
                    $allCheck += '</span></div>全选</div></label>';
                    htmlGroup += '<tr><td class="first-td" style="width:100px;">' + group.group + $allCheck +  '</td><td><div class="checkbox-list">';

                    for (var k = 0; k < menu.length; k++) {
                        var htmlMenu = '<label class="checkbox-inline ass-col no-pad"><div class="input-icon right">';
                        var _checked = '';
                        if (!menu[k].isAllow) {
                            isAllow = false;
                        } else {
                            _checked = 'checked ';
                        }
                        htmlMenu += '<div class="checker"><span class="' + _checked + perm.tabbar + ' ' + group.group + ' ' + i + '-' + j + '-' + k + ' right">';
                        htmlMenu += '<input type="checkbox" style="margin-left: -10px;" optype="onecheck" data-param="dataModule:\'{0}\',dataBiz:\'{1}\',dataItem:\'{2}\',dataInxs:\'{3}\',dataLen:\'{4}\',left:\'{5}\',dataModel:\'{6}\'" class="{1}_{2}">'.format(perm.tabbar, group.group, menu[k].id, i + '-' + j + '-' + k, menu.length, i + '-' + j, perm.tabbar);
                        htmlMenu += '</span></div>' + menu[k].name + '</div></label>';
                        htmlGroup += htmlMenu;
                    }
                    htmlGroup += '</div></td>';
 
                    htmlGroup += '</tr><tr class="space"></tr>';
                }
                htmlTabbar += htmlGroup;
                htmlTabbar += '</table></div></div></div>';
            }
            that.Model.setHtml({ id: '.ver-perm', value: htmlTabbar });
        };

        //表单元素单击事件
        _child.prototype.onElementClick = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id) {
                case 'tabbarcheck':
                    //底部导航栏复选框点击时
                    that.procTabbarCheck(e);
                    break;
                case 'allcheck':
                    //分组复选框点击时
                    that.procAllCheck(e);
                    break;
                case 'onecheck':
                    //菜单复选框点击时
                    that.procOneCheck(e);
                    break;
            }
            //阻止事件冒泡
            e.e.stopPropagation();
            e.e.preventDefault();
        };

        //底部导航栏复选框点击事件的处理函数
        _child.prototype.procTabbarCheck = function (e) {
            var that = this;
            var isAllow = false;
            var _classOpt = { id: '.' + e.param.dataBiz, value: 'checked' };
            var _class = that.Model.getAttr({ id: '.' + e.param.dataBiz, random: 'class' });
            if (_class.indexOf('checked') == -1) {
                isAllow = true;
                that.Model.addClass(_classOpt);
            } else {
                that.Model.removeClass(_classOpt);
            }
            var perms = that.role.perm.mpMenuPermission;
            for (var i = 0, l = perms.length; i < l; i++) {
                var perm = perms[i];
                if (perm && perm.tabbar == e.param.dataBiz) {
                    for (var j = 0, k = perm.groups.length; j < k; j++) {
                        var group = perm.groups[j];
                        var menus = group.menus;
                        if (menus) {
                            for (var m = 0; m < menus.length; m++) {
                                menus[m].isAllow = isAllow;

                                // 设置与菜单相同的复选框的选中状态
                                that.setMenuCheck({ dataBiz: group.group, dataItem: menus[m].id }, isAllow, i);
                            }

                            // 设置分组复选框的选中状态
                            that.setGroupCheck({
                                dataModule: perm.tabbar,
                                dataBiz: group.group,
                                dataLen: menus.length
                            });
                        }
                    }
                    break;
                }
            }
        };

        //分组复选框点击事件的处理函数
        _child.prototype.procAllCheck = function (e) {
            var that = this;
            if (!that.role || !that.role.perm) return;
            var $bizs = that.Model.getEleMent({ id: '.left.' + e.param.dataBiz });
            if (!$bizs || $bizs.length < 1) return;

            var isAllow = false;
            var _classOpt = { id: '.' + e.param.dataBiz, value: 'checked' };
            var _class = that.Model.getAttr({ id: '.' + e.param.dataBiz + '.' + e.param.dataInxs, random: 'class' });
            if (_class.indexOf('checked') == -1) {
                isAllow = true;
                that.Model.addClass(_classOpt);
            } else {
                that.Model.removeClass(_classOpt);
            }

            var perms = that.role.perm.mpMenuPermission;
            for (var i = 0; i < $bizs.length; i++) {
                var bizClass = $bizs.eq(i).attr('class').split(' ');
                if (!bizClass || bizClass.length < 4) continue;

                var dataInxs = bizClass[3].split('-');
                var perm = perms[dataInxs[0]];
                var menus = perm.groups[dataInxs[1]].menus;
                if (!menus) continue;

                for (var j = 0; j < menus.length; j++) {
                    menus[j].isAllow = isAllow;
                }

                //设置底部导航栏复选框的选中状态
                that.setTabbarCheck(perm);
            }
        };

        //菜单复选框点击事件的处理函数
        _child.prototype.procOneCheck = function (e) {
            var that = this;
            if (!that.role || !that.role.perm) return;
            var _class = that.Model.getAttr({ id: '.' + e.param.dataInxs, random: 'class' });
            var dataInxs = e.param.dataInxs.split('-');
            var perms = that.role.perm.mpMenuPermission;
            var perm = perms[dataInxs[0]];
            var menus = perm.groups[dataInxs[1]].menus;
            if (!menus) return;

            //设置当前点击的复选框的选中状态
            var isAllow = false;
            var _classOpt = { id: '.' + e.param.dataInxs, value: 'checked' };
            if (_class.indexOf('checked') == -1) {
                isAllow = true;
                that.Model.addClass(_classOpt);
            } else {
                that.Model.removeClass(_classOpt);
            }
            menus[dataInxs[2]].isAllow = isAllow;

            //设置菜单相同的复选框的选中状态
            that.setMenuCheck(e.param, isAllow, dataInxs[0]);

            //设置分组复选框的选中状态
            that.setGroupCheck(e.param);

            //设置底部导航栏复选框的选中状态
            that.setTabbarCheck(perm);
        };

        //设置菜单相同的复选框的选中状态
        _child.prototype.setMenuCheck = function (param, isAllow, moduleSeq) {
            var that = this;
            var perms = that.role.perm.mpMenuPermission;
            var _alikes = that.Model.getEleMent({ id: '.' + param.dataBiz + '_' + param.dataItem });
            _alikes.each(function () {
                var $ck = $(this);
                var _param = eval('({' + ($ck.attr('data-param') || '') + '})');
                var _dataInxs = _param.dataInxs.split('-');
                var $sp = $ck.parent('span');
                if (isAllow) {
                    $sp.addClass('checked');
                } else {
                    $sp.removeClass('checked');
                }
                var _perm = perms[_dataInxs[0]];
                var _menus = _perm.groups[_dataInxs[1]].menus;
                if (_menus) {
                    _menus[_dataInxs[2]].isAllow = isAllow;
                }
            });
        };

        //设置分组复选框的选中状态
        _child.prototype.setGroupCheck = function (param) {
            var that = this;
            //重名的分组个数
            var groupCount = that.Model.getEleMent({ id: '.left.' + param.dataModule + '.' + param.dataBiz }).length;
            //重名的分组的菜单个数
            var menuCount = groupCount * param.dataLen;
            var _checked = that.Model.getEleMent({ id: '.checked.right.' + param.dataModule + '.' + param.dataBiz });
            if (_checked && _checked.length == menuCount) {
                that.Model.addClass({ id: '.left.' + param.dataBiz, value: 'checked' });
            } else {
                that.Model.removeClass({ id: '.left.' + param.dataBiz, value: 'checked' });
            }
        };

        //设置底部导航栏复选框的选中状态
        _child.prototype.setTabbarCheck = function (perm) {
            var that = this;
            var groups = perm.groups;
            var _flag = true;
            for (var _i = 0, _l = groups.length; _i < _l; _i++) {
                var _menus = groups[_i].menus;
                if (_menus) {
                    for (var _m = 0; _m < _menus.length; _m++) {
                        if (_menus[_m].isAllow === false) {
                            _flag = false;
                            break;
                        }
                    }
                }
            }
            var _flagAttr = { id: '[flag={0}]'.format(perm.tabbar), value: 'checked' };
            if (_flag) {
                that.Model.addClass(_flagAttr);
            } else {
                that.Model.removeClass(_flagAttr);
            }
        }; 

        //根据分组Name查找分组
        _child.prototype.findGroup = function (group) {
            var that = this;
            var groups = [];
            var perms = that.role.perm.mpMenuPermission;
            for (var i = 0; i < perms.length; i++) {
                for (var j = 0; j < perms[i].groups.length; j++) {
                    if (perms[i].groups[j].group === group) {
                        groups.push(perms[i].groups[j]);
                    }
                }
            }
            return groups;
        };

        //分配角色
        // _child.prototype.allotRole = function () {
        //     var that = this;
        //     var node = that.currentNode;
        //     if (!node) {
        //         yiDialog.error('请选择要分配的角色！');
        //         return;
        //     }
        //     if (node.fispreset !== '1' || node.fmainorgid !== '0') {
        //         yiDialog.error('您选择的角色【' + node.name + '】没有手工设为共享，不允许分配！');
        //         return;
        //     }
        //     var perm = that.packPermission();
        //     that.Model.showForm({
        //         domainType: Consts.domainType.dynamic,
        //         openStyle: Consts.openStyle.modal,
        //         formId: 'sec_assignrightallot',
        //         cp: {
        //             role: node,
        //             perm: perm,
        //             callback: function (result) {
        //                 if (result && result.isSuccess) {

        //                 }
        //             }
        //         }
        //     });
        // };

        //组装角色授权信息
        _child.prototype.packPermission = function () {
            var that = this;
            if ($.isEmptyObject(that.role)) {
                yiDialog.a('请选择一个角色后再执行该操作！');
                return;
            } 

            var _perms = [];
            var clonePerm = $.extend(true, {}, that.role.perm);
            var perms = clonePerm.mpMenuPermission;
            for (var i = 0; i < perms.length; i++) {
                _perms.push({
                    tabbar: perms[i].tabbar,
                    groups: perms[i].groups
                });
            }
            clonePerm.mpMenuPermission = _perms;

            return clonePerm;
        };

        //保存
        _child.prototype.save = function () {
            var that = this;
            var perm = that.packPermission();

            // var roleUsers = [];
            // var users = that.Model.getEntryData({ id: that.entryId });
            // for (var i = 0; i < users.length; i++) {
            //     roleUsers.push(users[i].fuserid.id);
            //     roleUsers.push(users[i].fuserid.fname);
            // }
            // that.role.perm.roleUsers = roleUsers;
            // perm.roleUsers = roleUsers;
            perm.roleUsers = that.role.perm.roleUsers;

            that.Model.invokeFormOperation({
                id: 'saverolepermission',
                opcode: 'saverolepermission',
                param: {
                    domainType: Consts.domainType.dynamic,
                    roleId: that.role.fbillhead_id,
                    permission: JSON.stringify(perm)
                }
            });
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            switch (e.opcode) {
                case 'getpermrole':
                    if (isSuccess && srvData && srvData.data) {
                        var nodes = [];
                        var roles = srvData.data;
                        for (var i = 0; i < roles.length; i++) {
                            roles[i].id = i + 2;
                            roles[i].pId = 1;
                            roles[i].name = roles[i].fname;
                            nodes.push(roles[i]);
                        }
                        if (nodes.length > 0) {
                            //设置角色树控件数据源
                            that.Model.setTreeData({ id: that.roleTree, nodes: nodes });
                            //默认选中第一个节点
                            that.Model.selectTreeNode({ id: that.roleTree, node: 2 });
                            that.currentNode = that.Model.getTreeNode({ id: that.roleTree, node: 2 })
                            that.loadUserPermission();
                        }
                    }
                    break;
                // case 'delete':
                //     if (isSuccess) {
                //         that.Model.deleteTreeNode({ id: that.roleTree, node: e.opctx.node });
                //     }
                //     break;
                case 'getrolepermission':
                    if (srvData) {
                        that.role.perm = JSON.parse(srvData);
                        that.role.isEdit = true;
                        // that.fillUserEntry();
                        that.drewPermPanel();
                    }
                    break;
                // case 'queryfield':
                //     if (isSuccess) {
                //         var bizObjPerms = that.findGroup(e.opctx.dataBiz);
                //         that.Model.showForm({
                //             openStyle: 'modal',
                //             formId: 'sec_fields_permit',
                //             cp: {
                //                 fieldList: srvData,
                //                 fieldPerm: bizObjPerms[0].fieldACL,
                //                 callback: function (result) {
                //                     for (var i = 0; i < bizObjPerms; i++) {
                //                         bizObjPerms[i].fieldACL = result.fieldACL;
                //                     }
                //                 }
                //             }
                //         });
                //     }
                //     break;
            }
        };

        return _child;

    })(BasePlugIn);
    window.mp_assignright = window.mp_assignright || mp_assignright;
})();