/**
 * 选配范围
 * @ sourceURL=/fw/js/ydj/sel/sel_range.js
 */
; (function () {
    var sel_range = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }
        __extends(_child, _super);

        //编辑页面初始化事件
        _child.prototype.onBillInitialized = function (e) {
            var that = this;

        };

        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fmin':
                case 'fmax':
                    var fdatatype = that.Model.getValue({ id: "fdatatype", row: e.row });
                    if (fdatatype && fdatatype.id == '') {
                        fdatatype = e.value.fpropid.fdatatype;
                    }
                    if (fdatatype.id && fdatatype.id === '2') {
                        e.result.enabled = true;
                    } else {
                        e.result.enabled = false;
                    } 
                    return;
            }
        }

        //字段值变化事件
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fpropid':
                    //清空属性值
                    that.Model.setValue({ id: 'fpropvalueid', value: '', row: e.row });
                    break;
                case 'fpropvalueid':
                    //清空默认值
                    var propValueIds = e.value.id.split(',');
                    var defPropValueId = that.Model.getSimpleValue({ id: 'fdefaultpropvalueid', row: e.row });
                    if (propValueIds.indexOf(defPropValueId) === -1) {
                        that.Model.setValue({ id: 'fdefaultpropvalueid', value: '', row: e.row });
                    }
                    break;
            }
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'fpropid':
                    that.propQueryFilterString(e);
                    break;
                case 'fpropvalueid':
                    that.propValueQueryFilterString(e);
                    break;
                case 'fdefaultpropvalueid':
                    that.defPropValueQueryFilterString(e);
                    break;
            }
        };

        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            var fldId = e.id.toLowerCase();
            var fmin = that.Model.getSimpleValue({ id: 'fmin', row: e.row });
            var fmax = that.Model.getSimpleValue({ id: 'fmax', row: e.row });
            switch (fldId) {
                case 'fmin':
                    if (e.value > fmax && fmax != 0 )
                    {
                        e.value = fmin;
                        e.result = true;
                        yiDialog.mt({ msg: '起不允许大于止！', skinseq: 2 });
                    }
                    if (e.value < 0) {
                        e.value = fmin;
                        e.result = true;
                        yiDialog.mt({ msg: '起不允许小于0！', skinseq: 2 });
                    }
                    break;
                case 'fmax':   
                    if (e.value < fmin && fmin != 0 )
                    {
                        e.value = fmax;
                        e.result = true;
                        yiDialog.mt({ msg: '止不允许大于起！', skinseq: 2 });
                    }
                    break;
            } 
        }

        //处理【属性】字段的过滤条件
        _child.prototype.propQueryFilterString = function (e) {
            var that = this;

            //自动过滤筛选出未选过的属性, 不允许重复选择到不同行上
            var propIds = [];

            var entry = that.Model.getEntryData({ id: 'fentity' });
            for (var i = 0; i < entry.length; i++) {

                //当前属性跳过
                if (entry[i].id === e.row) continue;

                var propId = $.trim(entry[i].fpropid.id);
                if (propId) {
                    propIds.push(propId);
                }
            }

            if (propIds.length > 0) {
                e.result.filterString = "fid not in('{0}')".format(propIds.join("','"));
            }
        };

        //处理【属性值】字段的过滤条件
        _child.prototype.propValueQueryFilterString = function (e) {
            var that = this;

            //按属性过滤属性值
            var propId = that.Model.getSimpleValue({ id: 'fpropid', row: e.row });
            e.result.filterString = "fpropid='{0}'".format(propId);
        };

        //处理【默认值】字段的过滤条件
        _child.prototype.defPropValueQueryFilterString = function (e) {
            var that = this;

            //按属性值过滤默认值
            var propValueId = that.Model.getSimpleValue({ id: 'fpropvalueid', row: e.row });
            var propValueIds = propValueId.split(',');
            if (propValueIds.length === 1) {
                e.result.filterString = "fid='{0}'".format(propValueIds[0]);
                return;
            }
            if (propValueIds.length > 1) {
                e.result.filterString = "fid in('{0}')".format(propValueIds.join("','"));
                return;
            }
            e.result.filterString = "fid=''";
        };

        return _child;
    })(BasePlugIn);
    window.sel_range = sel_range;
})();