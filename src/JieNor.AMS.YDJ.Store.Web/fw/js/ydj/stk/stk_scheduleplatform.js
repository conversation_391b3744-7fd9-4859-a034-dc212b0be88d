/// <reference path="/fw/js/platform/mvvm/baseplugin.js" />
/*@ sourceURL=/fw/js/ydj/stk/stk_scheduleplatform.js*/
; (function () {
    var stk_scheduleplatform = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            that.level;
            that.parentNode;
            that.formId;
            that.Node;
            that.key = [];
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.menuTree = 'menutree';
        _child.prototype.newTree = 'newdatetree';
        _child.prototype.oldTree = 'olddatetree';
        _child.prototype.nodeIndex = 1;
        _child.prototype.currTreeNode = {};

        _child.prototype.otable = 'fbillentity';//待排单据
        _child.prototype.ttable = 'fdetailentity';//待排单据明细
        _child.prototype.thtable = 'fscheduleentity';//已排单据明细

        _child.prototype.currOneSel = '';//待排单据选中行
        _child.prototype.currTwoSel = [];//待排单据明细选中行
        _child.prototype.currThrSel = [];//已排单据明细选中行
        _child.prototype._qtd={
            orderBy:'fdate',// fdate,fcarid 默认按日期还是按车辆排序
            bizStatus:0  // 0,未排定， 1 已排定
        }
        _child.prototype.saveFlag = true;//切换左侧树节点，如果 saveFlag为true，则允许切换，如果为false，证明数据有修改，需要保存后才能切换
        _child.prototype.saveBas = true;//如果基本信息有改动，则需要保存后才能确认
        //是否齐套复选框的拷贝，此变量只有在页面初始化和点击搜索时赋值，用来处理查询待排单据明细行与待排单据时不一致的情况，
        //例如用户用非齐套查待排单据,在加载待排单据明细行前改成齐套，从而可能导致待排单据明细行加载为空
        _child.prototype.copyMatchall = false;
        //初始化动态表单插件
        _child.prototype.onInitialized = function (args) {
            debugger
            this._qtd={
                orderBy:'fdate',// fdate,fcarid 默认按日期还是按车辆排序
                bizStatus:0  // 0,未排定， 1 已排定
            }
            //初始化左侧树形模块
            //this.initTreeModule();
            this.qTreeData();
            this.Model.setValue({});
            this.Model.setAttr({id:'.sch-style #overview_1',random:'class',value:'tab-pane active'});
            this.Model.setVisible({ id: '[opcode=cancelscheduleplan]', value: false });
            //未排定状态不显示保存和确认排定按钮
            this.saveFlag = true;
            this.copyMatchall = this.Model.getSimpleValue({ "id": "fismatchall" });
            var that = this;

            $('#scheduleTreeTab a[data-toggle="tab"]').on('show.bs.tab', function (e) {
                if (!that.saveFlag) {
                    e.preventDefault();
                }
            });
        };
        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.otable:
                   
                    e.result = { multiselect: false};
                break;        
            }
        };
        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            
            if(this._qtd.bizStatus==1){//已排定的单据，不允许修改。
                e.result.enabled = false;
            }
        }
        
        //表单元素被双击后
        _child.prototype.onEntryRowDblClick = function (e) {
            var that=this;
            switch(e.id){
                case that.otable:
                
                that.currOneSel=e.row;
                var _rows = that.Model.getEntryRowData({id:e.id,row:e.row});

                var _param = {
                    sourceId: _rows.fsourceinterid,
                    sourceFormId: _rows.fsourceformid.id
                }
                
                this.Model.setAttr({ id: '.sch-style #overview_1', random: 'class', value: 'tab-pane active' });
                this.Model.setAttr({ id: '.sch-style #overview_2', random: 'class', value: 'tab-pane' });
                that.Model.resizeEntryWidth({ id: that.ttable });
                
                that.qArranging(_param);
                break;
            }
        }

        _child.prototype.qTreeData=function(){
            var _val=this.Model.getValue({id:'fdatesec'});
            var begTime=this.Model.getValue({id:'fstarttime_m'});
            var endTime=this.Model.getValue({id:'fendtime_m'});
            this._qtd.beginDate='';
            this._qtd.endDate='';
            this._qtd.beginTime=begTime;
            this._qtd.endTime=endTime;
            if(_val && _val.id){
                this._qtd.beginDate=_val.id;
                this._qtd.endDate=_val.dateTo;
            }
            
            this.Model.invokeFormOperation({
                formId:'stk_scheduleplatform',
                id: 'GetSchedulePlanBillTree',
                opcode: 'GetSchedulePlanBillTree',
                param:this._qtd
            });
        }

        _child.prototype.qArranging=function(args){//请求待排单据明细
            var that = this;
            var planId = that.currTreeNode.billId;
            var scheduleEntities = "";

            if (planId) {
                var _thtable = that.Model.getEntryData({ id: that.thtable });
                scheduleEntities = [];
                for (var m = 0, n = _thtable.length; m < n; m++) {
                    var thtableItem = _thtable[m];
                    if (args.sourceId == thtableItem['fsourceinterid_m']) {
                        scheduleEntities.push({
                            "sourceId": thtableItem["fsourceinterid_m"],
                            "sourceEntryId": thtableItem["fsourceentryid_m"],
                            "planEntryId": thtableItem["fplanentryid"],
                            "scheduleQty": thtableItem["fscheduleqty_m"]
                        });
                    }
                }
                scheduleEntities = JSON.stringify(scheduleEntities);
            }

            args["isMatchAll"] = that.copyMatchall;
            args["planId"] = planId;
            args["scheduleEntities"] = scheduleEntities;

            this.Model.invokeFormOperation({
                
                id: 'GetWatingSchedulePlanEntry',
                opcode: 'GetWatingSchedulePlanEntry',
                param:args
            });
        }
        
        _child.prototype.getOldSche=function(){//点击左侧树节点获得已排单据明细
            if(!this.currTreeNode.billId){
                return;
            }
            var _param={
                billId:this.currTreeNode.billId
            }
            
            this.Model.invokeFormOperation({
                
                id: 'GetScheduleEntity',
                opcode: 'GetScheduleEntity',
                param:_param
            });
        }
        _child.prototype.confirmSchel=function(){//ConfirmSchedulePlan 排单确认
            
            if(!this.currTreeNode.billId){
                yiDialog.mt({ msg: '无当前选中节点。', skinseq: 2 });
                return;
            }
            
            var _param={
                billIds:this.currTreeNode.billId
            }
            
            this.Model.invokeFormOperation({
                
                id: 'ConfirmSchedulePlan',
                opcode: 'ConfirmSchedulePlan',
                param:_param
            });
        }
        _child.prototype.cancelSchel=function(){//cancelscheduleplan 取消排单计划
            
            if(!this.currTreeNode.billId){
                yiDialog.mt({ msg: '无当前选中节点。', skinseq: 2 });
                return;
            }
            
            var _param={
                billIds:this.currTreeNode.billId
            }
            
            this.Model.invokeFormOperation({
                
                id: 'CancelSchedulePlan',
                opcode: 'CancelSchedulePlan',
                param:_param
            });
        }
        
        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            //that.saveFlag
            switch (e.id.toLowerCase()) {
                // case 'fscheduleqty':
                    
                //     var _rows= that.Model.getEntryRowData({id:that.ttable,row:e.row});
                //     //如果本次基本单位排单数量大于基本单位待排数量，则set本次基本单位排单数量为0
                //     if(_rows.fscheduleqty>_rows.fremainqty){
                //         that.Model.endEditEntry({id:that.ttable});
                //         that.Model.setValue({id:'fscheduleqty',row:e.row,value:1});
                //         that.Model.refreshEntry({id:that.ttable});
                //     }
                    
                // break;
                case 'fbizscheduleqty':// 
                    
                    //that.Model.refreshEntry({id:that.ttable});
                    break;
                case 'fscheduleqty_m':// 已排单据明细的本次排单数量不做校验
                case 'fstorehouseidfrom_m':
                case 'fstorehouseidto_m': 
                //当
                case 'fschedulestaffid':
                case 'fscheduledeptid':
                case 'fstockdateto':
                case 'fstockdeptidto':
                case 'fstockstaffidto':
                case 'fstockdate':
                case 'fstockdeptid':
                case 'fstockstaffid':
                
                this.saveFlag=false;
                this.saveBas=false;
                
                break;
            }
        }
        
        //字段值改变后
        _child.prototype.onBeforeSetValue = function (e) {
            
            var that = this;
            switch (e.id.toLowerCase()) {//本次排单数量大于待排数量的时候，本次排单数量设为0 { "expression": "fscheduleqty=0|fscheduleqty>fremainqty " }
                // case 'fbizscheduleqty':
                    
                //     var _val=that.Model.getValue({id:'fremainqty',row:e.row});
                //     if(e.value>_val){
                //         e.value=0;
                //         that.Model.endEditEntry({id:that.ttable });
                //     }
                    
                // break;
                case 'fscheduleqty_m'://已排单据明细的本地排单数量不能大于标尺数量
                    // var _val=that.Model.getValue({id:'fscalqty',row:e.row});
                    // if(e.value>_val){
                    //     e.value=0;
                    //     that.Model.endEditEntry({id:that.thtable });
                    // }
                break; 
            }
        }
        _child.prototype.ctschedul = function (args) {//创建已排单据行。
            var that=this;
            
            if(args && args.tsel &&args.tsel.length<=0 ){
                yiDialog.mt({ msg: '请先选择待排明细', skinseq: 2 });
                return;
            }else{//
                
            }
            var parVal={};//主行
            
            parVal.fsourceformid_m=args.osel.fsourceformid;//来源单据
            parVal.fsourcebillno_m=args.osel.fsourcebillno;//来源单编号
            parVal.fsourceinterid_m=args.osel.fsourceinterid;//来源单内码
            
            parVal.fsourcebilltypename_m=args.osel.fsourcebilltypename;//单据类型
            parVal.fsourcebiztypename_m=args.osel.fsourcebiztypename;//业务类型
            parVal.fsourcebizdate_m=args.osel.fsourcebizdate;//业务日期
            parVal.fplanbizdate_m=args.osel.fplanbizdate;//计划交期
            parVal.fstaffid_m=args.osel.fstaffid;//人员
            parVal.fdeptid_m=args.osel.fdeptid;//部门
            parVal.fcustomerid_m=args.osel.fcustomerid;//客户
            parVal.fphone_m=args.osel.fphone;//联系方式
            parVal.faddress_m=args.osel.faddress;//地址

            parVal.fstockdateto_m=  this.Model.getValue({id:'fstockdateto'});//收货日期
            
            parVal.fstockdate_m= this.Model.getValue({id:'fstockdate'});//发货日期

            // parVal.fstockdeptidto_m=  this.Model.getValue({id:'fstockdeptidto'});//收货部门
            // parVal.fstockstaffidto_m= this.Model.getValue({id:'fstockstaffidto'});//收货人
            // parVal.fstockdeptid_m= this.Model.getValue({id:'fstockdeptid'});//发货部门
            // parVal.fstockstaffid_m= this.Model.getValue({id:'fstockstaffid'});//发货人

            
            var aldata=[];
            for(var i=0,l=args.tsel.length; i<l; i++){
                var sval={};
                var lm=args.tsel[i].data;
                sval.fmaterialid_m = lm.fmaterialid;//商品
                sval.fmtrlnumber_m = lm.fmtrlnumber;//商品编号
                sval.fmtrlmodel_m=lm.fmtrlmodel;//规格型号
                sval.fattrinfo_m=lm.fattrinfo;//辅助属性
                sval.fcustomdesc_m=lm.fcustomdesc;//定制说明
                sval.fmtono_m=lm.fmtono;//物流跟踪号
                sval.fqty_m=lm.fqty;//数量 

                sval.funitid_m=lm.funitid;//基本单位
                sval.fbizunitid_m=lm.fbizunitid;//单位
                sval.fbizqty_m=lm.fbizqty;//数量。无意义，只是给用户观看。
                sval.fremainqty_m = lm.fremainqty;//待排数量
                sval.fusableqty_m = lm.fusableqty;//可用排单数量
                sval.ftailrow=lm.id;//标尺待排单据明细行
                sval.fsourceentryid_m = lm.fsourceentryid;//来源单分录内码
                
                if(lm.fstockdateto_d&& $.trim(lm.fstockdateto_d)){
                    sval.fstockdateto_m=lm.fstockdateto_d;//收货日期
                }else{}

                if(lm.fstockdeptidto_d&& $.trim(lm.fstockdeptidto_d.id)){
                    sval.fstockdeptidto_m=lm.fstockdeptidto_d;//收货部门
                }else{
                    var _par=JSON.stringify(this.Model.getValue({id:'fstockdeptidto'}));
                    _par=JSON.parse(_par);
                    sval.fstockdeptidto_m=_par;
                }


                if(lm.fstockstaffidto_d&& $.trim(lm.fstockstaffidto_d.id)){
                    sval.fstockstaffidto_m=lm.fstockstaffidto_d;//收货人
                }else{
                    var _par=JSON.stringify(this.Model.getValue({id:'fstockstaffidto'}));
                    _par=JSON.parse(_par);
                    sval.fstockstaffidto_m=_par;
                }

                if(lm.fstockdate_d&& $.trim(lm.fstockdate_d)){
                    sval.fstockdate_m=lm.fstockdate_d;//发货日期
                }

                if(lm.fexdeliverydate_d&& $.trim(lm.fexdeliverydate_d)){
                    sval.fexdeliverydate_m=lm.fexdeliverydate_d;//预计交货日期
                }

                if(lm.fstockdeptid_d&& $.trim(lm.fstockdeptid_d.id)){
                    sval.fstockdeptid_m=lm.fstockdeptid_d;//发货部门
                }else{
                    var _par=JSON.stringify(this.Model.getValue({id:'fstockdeptid'}));
                    _par=JSON.parse(_par);
                    sval.fstockdeptid_m=_par;
                }

                if(lm.fstockstaffid_d&& $.trim(lm.fstockstaffid_d.id)){
                    sval.fstockstaffid_m=lm.fstockstaffid_d;//发货人
                }else{
                    var _par=JSON.stringify(this.Model.getValue({id:'fstockstaffid'}));
                    _par=JSON.parse(_par);
                    sval.fstockstaffid_m=_par;
                }

                sval.fscheduledqty_m = lm.fscheduledqty;//已排单数量
                sval.fscheduleqty_m=lm.fscheduleqty;//本次基本单位排单数量
                sval.fbizscheduleqty_m=lm.fbizscheduleqty;//本次排单数量
                sval.fstorehouseidfrom_m=lm.fstorehouseidfrom;//发货仓
                sval.fstorehouseidto_m=lm.fstorehouseidto;//收货仓
                var resul=$.extend({},parVal,sval);
                aldata.push(resul);//获得已排单据数据
            }

            //以下计算只是为了用户有良好的体验加了计算待排单据明细的可用量、待排数量和已排数量，但并不准确，要以后端计算为准
            var _delRos=[];
            var _addRos=[];
            for(var i=0,l=aldata.length; i<l; i++){
                var rem=aldata[i];
                if (rem.fremainqty_m - rem.fscheduleqty_m == 0 || rem.fusableqty_m - rem.fscheduleqty_m == 0) {//因为这个是组合出来的数据，所以组合的本次基本单位待排数量-本次基本单位排单数量
                    _delRos.push(rem.ftailrow);
                }else{
                    this.Model.setValue({ id: 'fremainqty', row: rem.ftailrow, value: rem.fremainqty_m - rem.fscheduleqty_m });//待排单据明细对应行，待排数量=原有待排数量-本次排单数量
                    this.Model.setValue({ id: 'fusableqty', row: rem.ftailrow, value: rem.fusableqty_m - rem.fscheduleqty_m });//待排单据明细对应行，可用量=原有可用量-本次排单数量
                    this.Model.setValue({ id: 'fscheduledqty', row: rem.ftailrow, value: rem.fscheduledqty_m + rem.fscheduleqty_m });//待排单据明细对应行，已排数量=本次排单数量+原有已排数量
                    this.Model.setValue({id:'fscheduleqty',row:rem.ftailrow,value:0});//清空本次排单数量
                }
                _addRos.push(rem);
            }
            
            for(var i=0,l=_delRos.length; i<l; i++){
                that.Model.deleteRow({id:that.ttable,row:_delRos[i]});
            }
            
            for(var i=0,l=_addRos.length; i<l; i++){
                that.Model.addRow({id:that.thtable,data:_addRos[i]});
            }

            that.Model.refreshEntry({ id: that.ttable });
            //以上计算只是为了用户有良好的体验加了计算待排单据明细的可用量、待排数量和已排数量，但并不准确，要以后端计算为准

            //将当前已排明细发回后端，刷新待排单据明细
            var _param = {
                sourceId: parVal.fsourceinterid_m,
                sourceFormId: parVal.fsourceformid_m.id
            }

            that.qArranging(_param);
        }

        _child.prototype.savesche=function(){
            var _entrys=this.Model.getValue({id:this.thtable});
            
            if(!this.currTreeNode.billId){
                yiDialog.mt({ msg: '请选择一个排程计划节点(左侧节点)', skinseq: 2 });
                return;
            }
            // if(_entrys.length==0){
            //     yiDialog.mt({ msg: '请增加已排单据信息', skinseq: 2 });
            //     return;
            // }
            
            var _resEntry=[];
            for(var i=0,l=_entrys.length; i<l; i++){
                var lm=_entrys[i];
                var _res={};
                _res.fsourceformid=lm.fsourceformid_m.id;//来源单类型
                _res.fsourcebillno=lm.fsourcebillno_m ;//来源单编号
                _res.fsourceinterid=lm.fsourceinterid_m ;//来源单内码
                _res.fsourceentryid=lm.fsourceentryid_m ;//来源单分录内码
                _res.fscheduleqty=lm.fscheduleqty_m ;//排单数量  本次排单数量
                
                _res.fstockdateto_e=lm.fstockdateto_m ;//收货日期
                _res.fstockdeptidto_e=lm.fstockdeptidto_m.id ;//收货部门
                _res.fstockstaffidto_e=lm.fstockstaffidto_m.id ;//收货人
                _res.fstockdate_e=lm.fstockdate_m ;//发货日期
                _res.fstockdeptid_e=lm.fstockdeptid_m.id ;//发货部门
                _res.fstockstaffid_e=lm.fstockstaffid_m.id ;//发货人

                _res.fstorehouseidfrom=lm.fstorehouseidfrom_m.id ;//发货仓
                _res.fstorehouseidto=lm.fstorehouseidto_m.id ;//收货仓
                _resEntry.push(_res);

            }
            
            var _billData={id:this.currTreeNode.billId,fentity:_resEntry};
            _billData.fschedulestaffid=this.Model.getValue({id:'fschedulestaffid'}).id;//排单员
            _billData.fscheduledeptid=this.Model.getValue({id:'fscheduledeptid'}).id;//排单部门

            _billData.fstockdateto=this.Model.getValue({id:'fstockdateto'});//收货日期
            _billData.fstockdeptidto=this.Model.getValue({id:'fstockdeptidto'}).id;//收货部门
            _billData.fstockstaffidto=this.Model.getValue({id:'fstockstaffidto'}).id;//收货人
            _billData.fstockdate=this.Model.getValue({id:'fstockdate'});//发货日期
            _billData.fstockdeptid=this.Model.getValue({id:'fstockdeptid'}).id;//发货部门
            _billData.fstockstaffid=this.Model.getValue({id:'fstockstaffid'}).id;//发货人

            var simple={
                billDatas:[_billData]
            };
            
            simple.billDatas=JSON.stringify(simple.billDatas);
            
            this.Model.invokeFormOperation({
                
                id: 'SaveScheduleBillEntry',
                opcode: 'SaveScheduleBillEntry',
                param:simple
            });
        }

        _child.prototype.onEntryRowDeleted = function (e) {
            debugger
            var that = this;
            if (this._qtd.bizStatus == 1) {
                //已排定的情况，不允许操作。
                e.result = true;
                return;
            }

            switch (e.id.toLowerCase()) {
                //已排单据明细
                case that.thtable:

                    var _selRows = e.delRow;
                    if (!_selRows) {
                        return;
                    }

                    if (that.currOneSel) {//如果待排单据选中存在
                        var _oneRows = that.Model.getEntryRowData({ id: that.otable, row: that.currOneSel });

                        if (_oneRows && _oneRows.fsourceformid.id == _selRows.fsourceformid_m.id && _oneRows.fsourceinterid == _selRows.fsourceinterid_m) {
                            //如果待排单据选中行formid和来源单据id都相同，则刷新的第一条件满足

                            var _param = {
                                sourceId: _oneRows.fsourceinterid,
                                sourceFormId: _oneRows.fsourceformid.id
                            };
                            that.qArranging(_param);
                        }

                    }
                    break;
            }
        }
        
        _child.prototype.onEntryRowDeleting = function (e) {
            var that=this;
            if(this._qtd.bizStatus==1){
                //已排定的情况，不允许操作。
                e.result=true;
                return;
            }
        }
        
        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that=this;
            
            if (!args.opcode) return;
            switch (args.opcode) {
                
                case 'resize'://调整宽度
                args.result = true;
                
                setTimeout(function() {
                    for(var i=0,l=args.param.data.length; i<l; i++){
                        that.Model.resizeEntryWidth({id:args.param.data[i]});
                    }
                    
                }, 100);
                
                
                break;
                case 'search'://搜索
                args.result = true;
                that.qTreeData();
                break;
                case 'schesave'://保存
                    args.result = true;
                    
                    that.savesche();
                    

                break;
                case 'scheconfirm'://排单确认
                    args.result = true;
                    var _thTable=that.Model.getEntryData({id:that.thtable});
                    if(_thTable.length==0){
                        yiDialog.mt({ msg: '商品明细没有数据时,不允许确认排单', skinseq: 2 });
                        return;
                    }

                    if(!this.saveFlag){
                        yiDialog.mt({ msg: '已排单据信息有变化，请先保存后再进行排单确认', skinseq: 2 });
                        return;
                    }
                    yiDialog.c('排单确认成功后，刷新当前表单，以便查看已排定数据。',function(){
                        that.confirmSchel();
                    })
                    
                    
                    
                break;
                case 'cancelscheduleplan'://取消排单计划
                    args.result = true;
                    var _thTable=that.Model.getEntryData({id:that.thtable});
                    if(_thTable.length==0){
                        yiDialog.mt({ msg: '商品明细没有数据时,不允许取消排单', skinseq: 2 });
                        return;
                    }

                    if(!this.saveFlag){
                        yiDialog.mt({ msg: '已排单据信息有变化，请先保存并确认后再进行取消排单', skinseq: 2 });
                        return;
                    }
                    that.cancelSchel();
                     
                break;

                case 'billrefresh'://待排单据明细刷新
                
                    args.result = true;
                    var _rows=that.Model.getEntryRowData({id:that.otable,row:that.currOneSel});//此时激活的待排单据
                    if(!_rows){
                        yiDialog.mt({ msg: '请双击选择一行的待排明细', skinseq: 2 });
                        return;
                    }
                    var _param={
                        sourceId:_rows.fsourceinterid,
                        sourceFormId: _rows.fsourceformid.id
                    }
                    that.qArranging(_param);
                    
                break;
                
                case 'settle'://排定
                    args.result = true;
                    
                    if(this._qtd.bizStatus==1){
                    
                        yiDialog.mt({ msg: '左侧筛选条件为已排定的条件下，不允许排单', skinseq: 2 });
                        return;
                    }

                    if(!that.currTreeNode.id){
                        yiDialog.mt({ msg: '请先选择左侧数据节点，因为保存时需要关联具体节点数据', skinseq: 2 });
                        return;
                    }
                    var _osel=that.Model.getEntryRowData({id:that.otable,row:that.currOneSel});//此时激活的待排单据

                    var _tsel= that.Model.getSelectRows({id:that.ttable});//获取待排单据明细选中行
                    
                    for (var i = 0, l = _tsel.length; i < l; i++) {
                        var lm = _tsel[i].data;

                        if (lm.fscheduleqty <= 0) {
                            yiDialog.mt({ msg: '选中行有非法数据（本次基本单位排单数量必须大于0）', skinseq: 2 });
                            return;
                        }
                        
                        if(lm.fscheduleqty>lm.fremainqty){
                            yiDialog.mt({ msg: '选中行有非法数据（本次基本单位排单数量不允许大于基本单位待排数量）', skinseq: 2 });
                            return;
                        }
                        if (lm.fscheduleqty > lm.fusableqty) {
                            yiDialog.mt({ msg: '选中行有非法数据（本次基本单位排单数量不允许大于基本单位可用量）', skinseq: 2 });
                            return;
                        }
                    }
                    this.saveFlag=false;
                    that.ctschedul({
                        osel:_osel,
                        tsel:_tsel
                    });
                    
                break;
                
                case 'createsch':
                    
                    var cp = {
                        
                        callback: function (result) {
                            if (!result || !result.data) { return; }
                            
                            // that.Model.invokeFormOperation({
                            //     id: 'tbSaveSchedulePlanBill',
                            //     opcode: 'saveSchedulePlanBill',
                            //     param:result.data
                            // });
                        }
                    };
                    cp = $.extend(true, cp);
                    //弹出受理对话框
                    that.Model.showForm({
                        formId: 'stk_schedulecreate',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: cp
                    });
                    
                    args.result = true;
                    break;
                case 'change':
                    args.result = true;
                    if (!that.saveFlag) {
                        yiDialog.mt({ msg: '已排单据信息有变化，请先保存后再切换节点', skinseq: 2 });
                        return;
                    }
                    
                    this.currTreeNode={};
                    if(args && args.param && args.param.data){
                        switch(args.param.data){
                            // _qtd={
                            //     orderBy:'fdate',// fdate,fcarid 默认按日期还是按车辆排序
                            //     bizStatus:0  // 0,未排定， 1 已排定
                            // }
                            case 1:
                                that._qtd.orderBy='fdate';
                            break;
                            case 2:
                                that._qtd.orderBy='fcarid';
                            break;
                            case 3:
                                that._qtd.bizStatus=0;
                                this.Model.setVisible({ id: '[opcode=schesave]', value: true });
                                this.Model.setVisible({ id: '[opcode=scheconfirm]', value: true });
                                this.Model.setVisible({ id: '[opcode=clearsche]', value: true });
                                this.Model.setVisible({ id: '[opcode=cancelscheduleplan]', value: false });
                                allEnable(true);
                            break;
                            case 4:
                                that._qtd.bizStatus=1;
                                this.Model.setVisible({ id: '[opcode=schesave]', value: false });
                                this.Model.setVisible({ id: '[opcode=scheconfirm]', value: false });
                                this.Model.setVisible({ id: '[opcode=clearsche]', value: false });
                                this.Model.setVisible({ id: '[opcode=cancelscheduleplan]', value: true });
                                allEnable(false);
                            break;
                        }
                    }
                    
                    this.qTreeData();
                    break;
                case "searchwatingplan":
                    args.result = true;
                    that.copyMatchall = that.Model.getSimpleValue({ "id": "fismatchall" });
                    var receiptStatus = that.Model.getSimpleValue({ "id": "freceiptstatus" });
                    var searchText = that.Model.getSimpleValue({ "id": "fsearchtext" });
                    var selectFormId = that.Model.getSimpleValue({ "id": "fselectformid" });
                    var isMatchAll = that.copyMatchall;
                    var simple = {
                        "receiptStatus": receiptStatus,
                        "searchText": searchText,
                        "selectFormId": selectFormId,
                        "isMatchAll": isMatchAll
                    };
                    this.Model.invokeFormOperation({
                        id: 'GetWatingSchedulePlan',
                        opcode: 'GetWatingSchedulePlan',
                        param: simple
                    });
                    break;
                case "clearsche":
                    args.result = true;
                    if (that._qtd.bizStatus == 1) {
                        yiDialog.mt({ msg: '已确认的排单不允许清空!', skinseq: 2 });
                        return;
                    }
                    var _thtable = that.Model.getEntryData({ id: that.thtable });
                    if (!_thtable || _thtable.length <= 0) {
                        yiDialog.mt({ msg: '当前已排明细已清空!', skinseq: 2 });
                        return;
                    }
                    yiDialog.c('您确认清空已排明细吗？', function () {
                        that.Model.deleteEntryData({ id: that.thtable });
                        that.saveBas = false;
                        that.saveFlag = false;

                        if (that.currOneSel) {//如果待排单据选中存在，则刷新待排明细
                            var _oneRows = that.Model.getEntryRowData({ id: that.otable, row: that.currOneSel });
                            if (_oneRows && _oneRows.fsourceformid.id  && _oneRows.fsourceinterid ) {
                                var _param = {
                                    sourceId: _oneRows.fsourceinterid,
                                    sourceFormId: _oneRows.fsourceformid.id
                                };
                                that.qArranging(_param);
                            }
                        }
                    });
                    break;
                case "changetreevisibility":
                    args.result = true;
                    var status = that.Model.getAttr({ "id": ".sch-tree-control", random: 'data-status' });
                    switch (status) {
                        case "show":
                            that.Model.setAttr({ "id": ".sch-tree-control", random: 'data-status', value: 'hide' });
                            that.Model.setAttr({ "id": ".sch-tree-control", random: 'class', value: 'sch-tree-control sch-tree-hide-control' });
                            that.Model.setVisible({ id: '#scheduleTreeTab', value: false });
                            that.Model.setAttr({ "id": "#scheduleList", random: 'class', value: 'col-md-12 sch-list-max-width' });
                            break;
                        case "hide":
                            that.Model.setAttr({ "id": ".sch-tree-control", random: 'data-status', value: 'show' });
                            that.Model.setAttr({ "id": ".sch-tree-control", random: 'class', value: 'sch-tree-control sch-tree-show-control' });
                            that.Model.setVisible({ id: '#scheduleTreeTab', value: true });
                            that.Model.setAttr({ "id": "#scheduleList", random: 'class', value: 'col-md-9' });
                            break;
                    }
                    that.Model.resizeEntryWidth({ id: that.otable });
                    that.Model.resizeEntryWidth({ id: that.ttable });
                    that.Model.resizeEntryWidth({ id: that.thtable });
                    break;
            }
            function allEnable(flag){
                
                var _enbs=['fschedulestaffid','fscheduledeptid','fstockdateto','fstockdeptidto','fstockstaffidto','fstockdate','fstockdeptid','fstockstaffid'];
                for(var i=0,l=_enbs.length; i<l; i++){
                    that.Model.setEnable({id:_enbs[i],value:flag});
                }
            }
        };
        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            switch (e.opcode) {
                case 'savescheduleplanbill':
                    if(isSuccess){
                        yiDialog.success('创建单据成功');
                        //一般来说，这个不适宜使用，因为此页面为特殊单页面；
                        var _pageId= that.Model.getPageId();
                        var _theModel= Index.getPage(_pageId);
                        _theModel.returnData && _theModel.returnData.model.close();

                        that.Model.refresh();
                    }
                break;
                case 'confirmscheduleplan':
                    if(isSuccess){
                        yiDialog.success('已排单据确认成功');
                        that.Model.refresh();
                    }
                break;
                case 'cancelscheduleplan':
                    if(isSuccess){
                        yiDialog.success('取消排单计划成功');
                        that.Model.refresh();
                    }
                break;
                case 'saveschedulebillentry':
                    if(isSuccess){
                        that.saveFlag=true;
                        this.saveBas=true;
                        yiDialog.success('已排单据保存成功');
                        //刷新一下待排单据明细
                        var _rows=that.Model.getEntryRowData({id:that.otable,row:that.currOneSel});//此时激活的待排单据
                        if(_rows){
                            var _param={
                                sourceId:_rows.fsourceinterid,
                                sourceFormId: _rows.fsourceformid.id
                            }
                            that.qArranging(_param);
                            
                        }
                        
                    }
                    
                break;
                case 'getscheduleentity':
                    
                    that.Model.deleteEntryData({id:that.thtable});//删除已排单据明细
                    if(srvData && srvData[that.thtable].length>0){
                        for(var i=0,l=srvData[that.thtable].length; i<l; i++){
                            var lm=srvData[that.thtable][i];
                            
                            var _fqty=lm.fscheduleqty_m;
                            lm.fscheduleqty_m=0;
                            var _r= that.Model.addRow({id:that.thtable,data:lm});
                            that.Model.setValue({id:'fscheduleqty_m',row:_r,value:_fqty});
                            
                        }
                    }
                    var _des={id:'',fnumber:'',fname:''};
                    that.Model.setValue({id:'fschedulebillno',value:''});//清空排程编号
                    that.Model.setValue({id:'fdate',value:''});//清空排程日期
                    that.Model.setValue({id:'fstarttime',value:''});//清空开始时间
                    that.Model.setValue({id:'fendtime',value:''});//清空结束时间
                    that.Model.setValue({id:'fschedulestaffid',value:_des});//清空排单员
                    that.Model.setValue({id:'fscheduledeptid',value:_des});//清空排单部门

                    that.Model.setValue({id:'fstockdateto',value:''});//清空收货日期
                    that.Model.setValue({id:'fstockdeptidto',value:_des});//清空收货部门
                    that.Model.setValue({id:'fstockstaffidto',value:_des});//清空收货人
                    that.Model.setValue({id:'fstockdate',value:''});//清空发货日期
                    that.Model.setValue({id:'fstockdeptid',value:_des});//清空发货部门
                    that.Model.setValue({id:'fstockstaffid',value:_des});//清空发货人

                    that.Model.setValue({id:'fcarid',value:_des});//清空车辆
                    
                    that.Model.setValue({id:'fschedulebillno',value:srvData['fschedulebillno']});//设值排程编号
                    that.Model.setValue({id:'fdate',value:srvData['fdate']});//设值排程日期
                    that.Model.setValue({id:'fstarttime',value:srvData['fstarttime']});//设值开始时间
                    that.Model.setValue({id:'fendtime',value:srvData['fendtime']});//设值结束时间
                    that.Model.setValue({id:'fschedulestaffid',value:srvData['fschedulestaffid']});//设值排单员
                    that.Model.setValue({id:'fscheduledeptid',value:srvData['fscheduledeptid']});//设值排单部门

                    var fstockdateto = srvData['fstockdateto'];
                    fstockdateto = fstockdateto && $.trim(fstockdateto) ? fstockdateto : srvData['fdate'];
                    that.Model.setValue({ id: 'fstockdateto', value: fstockdateto });//设值收货日期
                    that.Model.setValue({id:'fstockdeptidto',value:srvData['fstockdeptidto']});//设值收货部门
                    that.Model.setValue({ id: 'fstockstaffidto', value: srvData['fstockstaffidto'] });//设值收货人
                    var fstockdate = srvData['fstockdate'];
                    fstockdate = fstockdate && $.trim(fstockdate) ? fstockdate : srvData['fdate'];
                    that.Model.setValue({ id: 'fstockdate', value: fstockdate });//设值发货日期
                    that.Model.setValue({id:'fstockdeptid',value:srvData['fstockdeptid']});//设值发货部门
                    that.Model.setValue({id:'fstockstaffid',value:srvData['fstockstaffid']});//设值发货人

                    that.Model.setValue({id:'fcarid',value:srvData['fcarid']});//设值车辆
                    
                    that.saveFlag=true;
                    var _rows=that.Model.getEntryRowData({id:that.otable,row:that.currOneSel});//此时激活的待排单据
                    if(_rows){
                        var _param={
                            sourceId:_rows.fsourceinterid,
                            sourceFormId: _rows.fsourceformid.id
                        }
                        that.qArranging(_param);
                        
                    }
                break;
                
                //加载业务表单数据字典
                case 'getscheduleplanbilltree':
                    

                    //按模块字段 moduleorder 升序排序
                    var treeData = srvData;
                    if (!treeData) {
                        return;
                    }
                    //将一级菜单数据进行排序
                    treeData = treeData.sort(yiCommon.sortby('order'));
                    //节点数组
                    var treeNodes = [];

                    //遍历一级节点
                    for (var i = 0, l = treeData.length; i < l; i++) {
                        var obj = treeData[i];
                        var rootNode = { id: i + 1, open: true, isParent: true, pId: 0, fid: treeData[i].id, };
                        
                        //将后台数据遍历并添加到当前结点上
                        for (var k in obj) {
                            //遍历对象，k即为key，obj[k]为当前k对应的值
                            if (k != 'id') {
                                if (k === 'icon') {
                                    rootNode['f' + k] = obj[k];
                                } else {
                                    rootNode[k] = obj[k];
                                }
                            }
                            
                        }
                        treeNodes.push(rootNode);

                    }
                    
                    var _tree='';
                    if(that._qtd.bizStatus==0){//未排定
                        _tree='newdatetree';
                        
                    }else{//已排定
                        _tree='olddatetree';
                        
                    }
                    var _id= that.Model.getPageId()+'_'+_tree;
                    
                    that.Model.setHtml({id:'#'+_id,value:''});
                    that.Model.setTreeData({ id: _tree, nodes: treeNodes });
                    this.currTreeNode={};
                break;
                case 'getwatingscheduleplan':
                    debugger
                    that.Model.deleteEntryData({ id: 'fbillentity' });//删除待排单据
                    if(srvData && srvData.length>0){
                        for(var i=0,l=srvData.length; i<l; i++){
                            var lm=srvData[i];
                            
                            that.Model.addRow({id:'fbillentity',data:lm});
                        }
                    }
                    that.Model.refreshEntry({id:'fbillentity'});
                break;
                case 'getwatingscheduleplanentry'://请求待排单据明细
                    //如果之前有数据，就清空，以便重新渲染
                    that.Model.deleteEntryData({ id: that.ttable });
                    
                    if(srvData && srvData.length>0){
                        for (var i = 0, l = srvData.length; i < l; i++) {
                            var lm = srvData[i];
                            var setQty = lm.fremainqty > lm.fusableqty ? lm.fusableqty : lm.fremainqty;
                            var addRowId = that.Model.addRow({ id: that.ttable, data: lm });
                            that.Model.setValue({ id: 'fscheduleqty', row: addRowId, value: setQty });
                        }
                    }
                break;
            }
        };
        
        _child.prototype.onBeforeDoOperation = function (args) {
            args.billData=JSON.stringify([{}]);
            switch (args.opcode) {
                //加载业务表单数据字典
                case 'getwatingscheduleplanentry':
                case 'settle':
                

                break;
            }
            
        }
        //处理树形控件的初始化过程
        _child.prototype.onCreateTreeList = function (args) {
            debugger
            var that = this;
            if (!args.id) return;
            switch (args.id.toLowerCase()) {
                case this.newTree:
                case this.oldTree:
                
                    args.result = {
                        view: {
                            selectedMulti: false// 禁止多点同时选中的功能
                        },
                        edit: {
                            enable: false
                            
                        },
                        
                        data: {
                            simpleData: {
                                enable: true
                            }
                        }
                    };
                    break;
            }

        };
        //点击前事件
        _child.prototype.onTreeNodeBeforeClick=function(args) {
            
            //args.flag=false;  监测，如果已排单据明细有修改的地方，则需要提示先保存，才能切换左侧树节点
            if(!this.saveFlag){
                yiDialog.mt({ msg: '已排单据信息有变化，请先保存后再切换节点', skinseq: 2 });
            }
            args.flag=this.saveFlag; 
            

        };
        //数节点名称编辑后的事件
        _child.prototype.onTreeNodeEdited = function (e) {
            if (!e || !e.node) return;
            var that = this;
            var nameFieldId = '';
            switch (e.node.level) {
                case 0:
                    nameFieldId = 'fname_m';
                    break;
                case 1:
                    nameFieldId = 'fname_g';
                    break;
                case 2:
                    nameFieldId = 'fname_c';
                    break;
            }
            if (nameFieldId) {

                //更新菜单名称
                that.Model.setValue({ id: nameFieldId, value: e.node.name });

                this.currTreeNode = e.node;

                //点击树节点显示右侧对应的页面
                this.showPage(e.node);
            }
        };

        //处理树形控件节点的点击事件
        _child.prototype.onTreeNodeClick = function (args) {
            if (!args.id) return;
            switch (args.id.toLowerCase()) {
                case this.newTree:
                case this.oldTree:
                    
                    if(args.node&& !args.node.isParent){//非父级节点
                        this.currTreeNode = args.node;
                        //点击树节点显示右侧对应的页面
                    
                        //GetScheduleEntity  billId  获得已排单据明细
                        this.getOldSche();
                    }else{
                        this.currTreeNode = {};
                    }
                    
                    
                    break;
            }
        };

        //处理树形控件节点的删除前事件
        _child.prototype.onTreeNodeDeleting = function (args) {
            if (!args.id) return;
            switch (args.id.toLowerCase()) {
                case this.menuTree:
                    this.delete(args.node);
                    break;
            }
        };

        //表格单元格点击事件
        _child.prototype.onEntryCellClick = function (e) {
            debugger
            var that = this;
            if (e.id === 'fbillentity' && e.fieldId === 'fsourcebillno') {
                e.result = true;
                var billNo = that.Model.getSimpleValue({ id: "fsourcebillno", row: e.row });
                var formId = that.Model.getSimpleValue({ id: "fsourceformid", row: e.row }); 
                if (billNo == null || billNo.length <= 0 || formId == null || formId.length <= 0) {
                    return;
                }
                that.Model.invokeFormOperation({
                    id: 'orderdetail',
                    opcode: 'showDetail',
                    param: {
                        domainType: Consts.domainType.bill,
                        formId: formId,
                        billNos: billNo
                    }
                });
            }
        };

        return _child;
    })(BasePlugIn);
    window.stk_scheduleplatform = window.stk_scheduleplatform || stk_scheduleplatform;
})();