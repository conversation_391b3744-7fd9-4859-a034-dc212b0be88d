/*
    销售出库单插件
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/ydj/stk/stk_sostockout.js
 */
; (function () {
    var stk_sostockout = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            that.filesavemsg = [];//全局存储信息
            that.result = [];
            that.tempId = [];
            that.succArr = [];
            that.nowRow = '';//当前被双击的行
            that.suentry = [];
            that.notGetFIFOStock = {};//不获取库位推荐
            that.prodRecord = {};//商品行记录
            that.isOutAutoNewService = false; //【经销商.出库自动创建送装服务单】参数是否启用
            that.remindnumbers = "";
            that.isqueryinventory = false;
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = 'fentity';//商品信息
        _child.prototype.fpackageentry = 'fpackageentry';//包件信息

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.fentity:
                case this.fpackageentry:

                    break;
            }
        };

        _child.prototype.onBillInitialized = function (args) {
            var that = this;

            var fstatus = that.Model.getSimpleValue({ id: 'fstatus' });
            var fchangestatus = that.Model.getSimpleValue({ id: 'fchangestatus' });
            if (fstatus == 'D' || fstatus == 'E' || fchangestatus == '1') {
                $("#selectCus").attr("style", "cursor:not-allowed;");
                $("#selectCus").addClass('disabled');
                /*that.Model.setStyle({ id: 'a[opcode="selectCus"]', value: { '"cursor': 'not-allowed' } });*/
            }
            else {
                that.Model.setStyle({ id: 'a[opcode="selectCus"]', value: { 'cursor': 'pointer' } });
            }

            //创建状态
            if (that.formContext.status === "new" || that.formContext.status === "push") {
                that.dealfamount();
            }

            yiAjax.p('/bill/stk_sostockout?operationno=queryautonewserviceparam', null, function (r) {
                var res = r.operationResult;
                if (res.isSuccess && res.srvData === 1) {
                    that.isOutAutoNewService = true;
                    that.setFieldMustFlag({ id: "fhomedelivery", caption: "是否送货上门", must: true });
                }
                else {
                    that.isOutAutoNewService = false;
                    that.setFieldMustFlag({ id: "fhomedelivery", caption: "是否送货上门", must: false });
                }
            }, null, null, null, { async: false });

            that.setDropShipMentMenuVisible();
            that.setDropShipMentVisible();
        };

        //行选中事件
        _child.prototype.onSelectedRowsChanged = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case "list":
                    if (!e.data && e.data.length <= 0) return;
                    var filterdata = e.data.filter(x => x.fstatus == 'D');
                    if (filterdata.length > 0 && filterdata.length == e.data.length) {
                        that.Model.setEnable({ id: '#tbDeliveryTask', value: true });
                    }
                    else {
                        that.Model.setEnable({ id: '#tbDeliveryTask', value: false });
                    }
                    break;
            }
        };


        _child.prototype.entrycopy = function () {
            //选中行
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行关联复制行！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('关联复制行不支持多选！');
                return;
            };
            if (ds) {
                var newentry = $.extend(true, [], ds);
                var newid = yiCommon.uuid(18);
                newentry[0].data.id = newid;
                newentry[0].pkid = newid;
                newentry[0].data.fiscopy = 1;
                var row = that.Model.addRow({ id: that.fentity, pid: ds[0].pkid, data: newentry[0].data });
                that.Model.setEnable({ id: "fmaterialid", row: row, value: false });
                var fields = that.Model.uiForm.getAllFields();
                //仓库、数量不锁定，其它都锁定
                var noLockFields = ["fstorelocationid", "fentrynote", "fbizqty", "fbizplanqty"];
                for (var i = 0; i < fields.length; i++) {
                    if (that.uiForm.getField(fields[i]).entityKey != that.fentity) {
                        continue;
                    }
                    if (noLockFields.indexOf(fields[i]) > -1) {
                        continue;
                    }
                    that.Model.setEnable({ id: fields[i], row: row, value: false });
                }
            }
        };

        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'entrycopy':
                    e.result = true;
                    that.entrycopy();
                    break;
                case 'specialaudit':
                    var id = that.Model.getValue({ id: 'id' });
                    if (id) return;//只有创建状态的才保存
                    that.Model.invokeFormOperation({
                        id: 'tbSave',
                        opcode: 'save',
                        param: {
                            formId: 'stk_sostockout',
                            isSpecialAudit: true
                        }
                    });
                    break;
                case 'pull':
                    that.pull(e);
                    break;

                case 'isexsitbarcode':
                    that.querybarcode(e);
                    break;
                //新增服务
                case 'pushservice':
                    e.result = true;
                    that.Model.invokeFormOperation({
                        'id': 'tbPushService',
                        'opcode': 'push',
                        'param': {
                            'id': that.Model.getValue({ id: 'id' }),
                            'ruleid': "stk_sostockout2ydj_service"
                        }
                    });
                    break;
                case 'selectcus':

                    e.result = true;
                    var fstatus = that.Model.getSimpleValue({ id: 'fstatus' });
                    var fchangestatus = that.Model.getSimpleValue({ id: 'fchangestatus' });
                    if (fstatus == 'D' || fstatus == 'E' || fchangestatus == '1') {
                        return;
                    }
                    var fcustomer = that.Model.getSimpleValue({ id: 'fcustomerid' });
                    if (!fcustomer) {
                        yiDialog.warn('请先选择客户！');
                        return;
                    }
                    var filterString = "fid = '{0}' and fcontacter !=''"
                        .format(fcustomer);
                    that.Model.showSelectForm({
                        formId: 'ydj_customercontact',
                        selectMul: false,
                        pkid: fcustomer,
                        dynamicParam: {
                            filterString: filterString,
                            customer: fcustomer
                        }
                    });
                    break;
                case 'save':
                case 'savesubmit':
                    e.result = true;
                    var param = e.param;
                    param.formId = "stk_sostockout";
                    that.Model.invokeFormOperation({
                        id: 'tbSave',
                        opcode: 'save',
                        param: param
                    });
                    break;
                case 'saveaudit':
                    e.result = true;
                    var checkorder = that.checkOrder(e.opcode.toLowerCase());
                    if (Consts.isdirectsale && e.opcode.toLowerCase() == 'saveaudit') {
                        yiDialog.c('审核通过后自动同步总部且不可反审核，确定继续审核？', function () {
                            if (checkorder && that.remindnumbers) {
                                e.result = true;
                                yiDialog.d({
                                    id: 'remindnumbers',
                                    type: 1,
                                    resize: false,
                                    maxmin: false,
                                    title: '系统提示',
                                    content: that.remindnumbers,
                                    area: ['400px', '200px'],
                                    btn: ['确定'],
                                    yes: function (index, layero) {
                                        layer.close(index);
                                    }
                                });
                            } else {
                                var param = e.param;
                                param.formId = "stk_sostockout";
                                that.Model.invokeFormOperation({
                                    id: 'tbSave',
                                    opcode: 'save',
                                    param: param
                                });
                            }
                        }, function () {
                            e.result = true;
                            return;
                        });
                    }
                    else {
                        if (checkorder && that.remindnumbers) {
                            yiDialog.d({
                                id: 'remindnumbers',
                                type: 1,
                                resize: false,
                                maxmin: false,
                                title: '系统提示',
                                content: that.remindnumbers,
                                area: ['400px', '200px'],
                                btn: ['确定'],
                                yes: function (index, layero) {
                                    layer.close(index);
                                }
                            });
                        } else {
                            var param = e.param;
                            param.formId = "stk_sostockout";
                            that.Model.invokeFormOperation({
                                id: 'tbSave',
                                opcode: 'save',
                                param: param
                            });
                        }
                    }
                    break;
                case 'rejectflow':
                case 'auditflow':
                    e.result = true;
                    var checkorder = that.checkOrder(e.opcode.toLowerCase());
                    if (Consts.isdirectsale && e.opcode.toLowerCase() == 'auditflow') {
                        yiDialog.c('审核通过后自动同步总部且不可反审核，确定继续审核？', function () {
                            if (checkorder && that.remindnumbers) {
                                e.result = true;
                                yiDialog.d({
                                    id: 'remindnumbers',
                                    type: 1,
                                    resize: false,
                                    maxmin: false,
                                    title: '系统提示',
                                    content: that.remindnumbers,
                                    area: ['400px', '200px'],
                                    btn: ['确定'],
                                    yes: function (index, layero) {
                                        layer.close(index);
                                    }
                                });
                            } else {
                                var param = e.param;
                                param.formId = "stk_sostockout";
                                that.Model.invokeFormOperation({
                                    id: 'tbAudit',
                                    opcode: e.opcode.toLowerCase(),
                                    param: param
                                });
                            }
                        }, function () {
                            e.result = true;
                            return;
                        });
                    }
                    else {
                        if (checkorder && that.remindnumbers) {
                            yiDialog.d({
                                id: 'remindnumbers',
                                type: 1,
                                resize: false,
                                maxmin: false,
                                title: '系统提示',
                                content: that.remindnumbers,
                                area: ['400px', '200px'],
                                btn: ['确定'],
                                yes: function (index, layero) {
                                    layer.close(index);
                                }
                            });
                        } else {
                            var param = e.param;
                            param.formId = "stk_sostockout";
                            that.Model.invokeFormOperation({
                                id: 'tbAudit',
                                opcode: e.opcode.toLowerCase(),
                                param: param
                            });
                        }
                    }
                    break;
            }
        }

        //检查订单是否满足条件
        _child.prototype.checkOrder = function (opname) {
            var that = this;
            var isremind = false;

            var selectedRows;
            if (that.Model.viewModel.domainType == Consts.domainType.bill) {
                selectedRows = [{ pkValue: that.Model.pkid }];
            }
            if (that.Model.viewModel.domainType == Consts.domainType.list) {
                selectedRows = that.Model.getSelectRows();
                if (!selectedRows || selectedRows.length <= 0) {
                    yiDialog.mt({ msg: '请至少选择一条数据。', skinseq: 2 });
                    return;
                }
            }

            var ids = [];
            for (var i = 0; i < selectedRows.length; i++) {
                ids.push(selectedRows[i].pkValue);
            }
            var param = {
                simpleData: {
                    formId: 'stk_sostockout',
                    Ids: ids.join(","),
                    opname: opname
                }
            };

            yiAjax.p('/bill/stk_sostockout?operationno=verifyproduct', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (res.isSuccess && srvData) {
                    isremind = true;
                    that.remindnumbers = srvData;
                }
            }, null, null, null, { async: false });
            return isremind;
        };

        //多选列表选择后的数据。
        _child.prototype.onAfterSelectFormData = function (e) {
            if (!e || !e.formId) return;
            var that = this;
            switch (e.formId) {
                case 'ydj_customercontact':

                    e.result = true;
                    //填充数据。
                    var data = e.data;
                    if (data.length == 0) {
                        return;
                    }
                    var d = data[0];
                    //收货人
                    var fcontacter = d.fcontact_a;
                    if (fcontacter) that.Model.setValue({ id: "fconsignee", value: fcontacter });
                    var fphone = d.fphone_a;
                    if (fphone) that.Model.setValue({ id: "fphone", value: fphone });
                    var faddress = d.faddress_a;
                    if (faddress) that.Model.setValue({ id: "faddress", value: faddress });
                    var fprovince = d.fprovince_a;
                    if (fprovince) that.Model.setValue({ id: "fprovince", value: fprovince });
                    var fcity = d.fcity_a;
                    if (fcity) that.Model.setValue({ id: "fcity", value: fcity });
                    var fregion = d.fregion_a;
                    if (fregion) that.Model.setValue({ id: "fregion", value: fregion });
                    break;
            }
        };

        //条码联查
        _child.prototype.querybarcode = function (e) {
            var that = this;
            e.result = true;

            var selRows = that.Model.getSelectRows({ id: that.fentity });
            if (!selRows || selRows.length < 1) {
                yiDialog.warn('请先选中行再操作!');
                return;
            }
            //if (selRows.length > 1) {
            //    yiDialog.warn('只允许勾选一行进行条码联查!');
            //    return;
            //}
            var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
            if (fbillno == '' || fbillno == undefined) {
                yiDialog.mt({ msg: '请保存单据', skinseq: 2 });
                return;
            }
            var datas = [];
            for (var i = 0; i < selRows.length; i++) {
                datas.push({ seldata: selRows[i].data.fmaterialid.id });
            }
            //JSON.stringify(datas)
            that.Model.invokeFormOperation({
                id: 'isexsitbarcode',
                opcode: 'isexsitbarcode',
                param: {
                    'formId': 'bcm_barcodemaster',
                    'fsourcetype': 'stk_sostockout',
                    'fsourcenumber': fbillno,
                    'fmaterialid': JSON.stringify(datas),
                }
            });
        };


        //初始化商品明细表的经销价插件
        _child.prototype.onBillInitProduct = function (param, params) {

            var that = this;
            var productData = [];

            if (param == 'init') {//初始化，


            } else if (param == 'change') {//改变某一行
                var reGridData = that.Model.getValue({ id: that.fentity });

                var rowData = {};
                for (var i = 0, l = reGridData.length; i < l; i++) {
                    if (reGridData[i] && reGridData[i].id == params.attrinfo.row) {
                        rowData = reGridData[i];
                    }
                }

                var reAttrinofEntry = [];//按照接口，重新组合
                var tempAttr = [];
                if (rowData.fattrinfo) {
                    tempAttr = rowData.fattrinfo.fentity;
                }

                for (var n = 0, m = tempAttr.length; n < m; n++) {
                    reAttrinofEntry.push({
                        valueId: tempAttr[n].fvalueid,
                        auxPropId: tempAttr[n].fauxpropid.id
                    })
                }

                productData.push({
                    clientId: rowData.id,
                    productId: rowData.fmaterialid.id,
                    bizDate: that.Model.uiData.fdate,
                    length: rowData.flength,
                    width: rowData.fwidth,
                    thick: rowData.fthick,
                    attrInfo: {
                        id: '',
                        entities: reAttrinofEntry
                    }
                });
            }

            productData = JSON.stringify(productData);

            that.Model.invokeFormOperation({
                id: param,

                opcode: 'getprices',
                //option: cvtParams,
                param: {
                    productInfos: productData,
                    formId: 'ydj_price',
                    domainType: 'dynamic'
                }
            });
        }

        //报价明细 辅助属性价格查询按钮点击事件
        _child.prototype.onPriceSerch = function (e) {
            var that = this;
            var flag = true;
            that.alertModel = e;
            var productData = [];
            var reAttrinofEntry = [];//按照接口，重新组合 
            var fentry = that.alertModel.Model.uiData.fentity;
            for (var n = 0, m = fentry.length; n < m; n++) {
                var lm = fentry[n];
                if (lm.fisselect) {//被选中的辅助属性行

                    if (!lm.fvalueid) {//辅助属性行需要填满信息才能查询
                        flag = false;
                    }
                    reAttrinofEntry.push({
                        valueId: lm.fvalueid,
                        auxPropId: lm.fauxpropid.id
                    })
                }

            }

            productData.push({
                clientId: '',
                productId: that.alertModel.Model.uiData.fmaterialid.id,
                bizDate: that.Model.uiData.fdate,//订单日期
                length: that.alertModel.Model.uiData.flength,
                width: that.alertModel.Model.uiData.fwidth,
                thick: that.alertModel.Model.uiData.fthick,
                attrInfo: {
                    id: '',
                    entities: reAttrinofEntry
                }
            });

            productData = JSON.stringify(productData);
            if (flag) {
                that.Model.invokeFormOperation({
                    id: 'onPriceSerch',

                    opcode: 'getprices',
                    //option: cvtParams,
                    param: {
                        productInfos: productData,
                        formId: 'ydj_price',
                        domainType: 'dynamic'
                    }
                });
            } else {
                yiDialog.mt({ msg: '辅助属性信息不全，无法查询价格。', skinseq: 2 });
            }


        };

        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    //that.onBillInitProduct('change',{attrinfo:e});
                    that.getFIFOStock(e.row);
                    break;
            }
        };

        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    var isMain = that.Model.getValue({ id: 'fpublishstatus' });
                    //采购订单，发布状态  fpublishstatus=='publish_status_02' 的时候，全部锁住
                    if (isMain && isMain.id == 'publish_status_02') {
                        e.result = true;
                        yiDialog.mt({ msg: '已经建立协同关系，不允许删除商品明细！', skinseq: 2 });
                    }
                    break;
            }
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    //重新计算表头字段值
                    that.culFbill();
                    break;
            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            var isMain = that.Model.getValue({ id: 'fpublishstatus' });
            var isCopy = that.Model.getValue({ id: 'fiscopy', row: e.row });
            ////复制行只能 编辑仓库、仓位、备注、数量类的字段
            //if (isCopy && !(e.id.toLowerCase() == "fstorehouseid"
            //    || e.id.toLowerCase() == "fstorelocationid"
            //    || e.id.toLowerCase() == "fentrynote"
            //    || e.id.toLowerCase() == "fbizqty"
            //    || e.id.toLowerCase() == "fbizplanqty"
            //    )
            //)
            //{
            //    e.result.enabled = false;
            //    return;
            //}

            switch (e.id.toLowerCase()) {

                case 'fattrinfo':
                    //商品为空时，不允许编辑
                    productId = $.trim(that.Model.getSimpleValue({ id: 'fmaterialid', row: e.row }));
                    if (!productId) {
                        e.result.enabled = false;
                        return;
                    }
                    break;
                case 'fcustomdesc':
                    //下推的商品明细，定制说明不允许编辑
                    var fsoorderno = $.trim(that.Model.getValue({ id: 'fsoorderno', row: e.row }));
                    if (fsoorderno) {
                        e.result.enabled = false;
                    }
                    break;
            }
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            if (!e.id) return;
            var that = this;
            var fbilltypeid = that.Model.getValue({ "id": "fbilltype" })
            var billtypeNo = fbilltypeid ? fbilltypeid.fnumber : "";
            var billtypeName = fbilltypeid ? fbilltypeid.fname : "";
            switch (e.id.toLowerCase()) {
                //仓库：按库存参数中控制可选仓库范围
                case 'fstorehouseid':
                    var deptid = that.Model.getSimpleValue({ id: 'fstockdeptid' });
                    var srcPara = {
                        formid: 'stk_sostockout',
                        deptid: deptid,
                    };
                    if (Consts.isdirectsale) {//直营，只允许选择总仓或门店仓
                        e.result.filterString = " fwarehousetype in ('warehouse_01','warehouse_02') ";
                    }
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;
                case 'fmaterialid':
                    var fdeptid = that.Model.getValue({ "id": "fdeptid" });//门店信息
                    var srcPara = {
                        billtypeid: fbilltypeid,
                        billtypeNo: billtypeNo,
                        billtypeName: billtypeName,
                        deptId: (fdeptid == null ? '' : fdeptid.id) || '',
                        deptNo: (fdeptid == null ? '' : fdeptid.fnumber) || '',
                        deptName: (fdeptid == null ? '' : fdeptid.fname) || '',
                    };
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;


            }
        };


        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {

                //数量单价的变化，影响金额
                case 'fqty':
                case 'fprice':
                    that.culNum({ name: e.id, rowId: e.row, value: e.value });
                    break;
                //计算总件数
                case 'fpackageqty':
                    that.culFbill();
                    break;
                //计算总立方数
                case 'fcubeqty':
                    that.culFbill();
                    break;
                case 'fmaterialid':
                case 'fstockdeptid':
                    // that.SetDefaultStockInfo(false, e.row)
                    break;
                case 'fcustomerid':
                    /*
                       当销售出库单重新选择客户，需要直接清空原已选择的所有源单信息；
                    */
                    that.clearEntry();
                    break;
                case 'finstallerid':
                case 'fdrivername':
                    if (e.tgChange == false) {
                        return;
                    }
                    that.SaveEditInfo(e.id.toLowerCase(), e.value);
                    break;
            }

            // 库位推荐
            var isqueryinventory = e && e.ctx && e.ctx.isqueryinventory;//如果是从库存综合查询选中的数据，不需要做库位推荐
            switch (e.id.toLowerCase()) {
                case 'fmaterialid':
                // case 'fattrinfo':
                //case 'funitid':
                //case 'fstockunitid':
                case 'fownertype':
                case 'fownerid':
                case 'fmtono':
                case 'flotno':
                    if (isqueryinventory == false || isqueryinventory == undefined) {
                        that.getFIFOStock(e.row);
                    }
                    if (isqueryinventory == true) {
                        that.isqueryinventory = true;//设置标记，以便从库存综合查询返回填充fqty时不要做库位推荐
                    }
                    break;
                case 'fqty':
                    if (that.isqueryinventory == false && (isqueryinventory == false || isqueryinventory == undefined)) {
                        if (!that.notGetFIFOStock.hasOwnProperty(e.row)) {
                            that.getFIFOStock(e.row);
                        }
                    }
                    break;
                case 'fstorehouseid':
                    if (that.isqueryinventory == false && (isqueryinventory == false || isqueryinventory == undefined)) {
                        // 更改仓库时，需要清空标记，以便修改数量时，触发推荐
                        if (that.notGetFIFOStock.hasOwnProperty(e.row)) {
                            delete that.notGetFIFOStock[e.row];
                        }
                    }
                    break;
                case 'fstorelocationid':
                    if (that.isqueryinventory == false && (isqueryinventory == false || isqueryinventory == undefined)) {
                        // 清空仓位时，需要清空标记，以便修改数量时，触发推荐
                        if (that.notGetFIFOStock.hasOwnProperty(e.row) && e.value.id === '') {
                            delete that.notGetFIFOStock[e.row];
                        }
                    }
                    break;
            }
        };

        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            if (e.id == that.fentity) {
                switch (e.btnid.toLowerCase()) {
                    case 'g_record': //批录按钮 
                        //仓位禁止批录，需求#35670 
                        if (e.fieldId.toLowerCase() === 'fstorehouseid') {
                            e.copyFields = ["fstockstatus"];
                            e.clearFields = ["fstorelocationid"];
                        }
                        break;
                }
            }
        };

        ///审核状态下的安装员,司机姓名字段修改直接保存
        _child.prototype.SaveEditInfo = function (fileName, value) {
            var that = this;
            var fileValue = value;
            if (fileName == 'finstallerid') {
                fileValue = value.id;
            }
            var status = that.Model.getSimpleValue({ id: 'fstatus' });
            if (status == 'E' || status == 'D') {
                that.Model.invokeFormOperation({
                    id: 'tbSave',
                    opcode: 'saveeditinfo',
                    param: {
                        pkid: this.Model.pkid,
                        fileName: fileName,
                        fileValue: fileValue,
                    }
                });
            }
            that.Model.setValue({ id: fileName, tgChange: false });
        }

        // //获取默认的仓库
        // _child.prototype.SetDefaultStockInfo = function (isMatChange, rowIndex) {
        //     var that = this;
        //     var deptid = that.Model.getSimpleValue({ id: 'fstockdeptid' });
        //     if (!deptid) {
        //         //没有设置部门，不需要请求获取数据
        //         return;
        //     }
        //     if (isMatChange) {
        //         var fstorehouseid = that.Model.getSimpleValue({ id: 'fstorehouseid', row: rowIndex });
        //         if (fstorehouseid) {
        //             //修改物料时，如果已经有仓库，不需要再设置默认的
        //             return;
        //         }
        //     }

        //     yiAjax.p('/bill/ydj_storehouse?operationno=getdefaultstockinfo&srcformid=stk_sostockout&deptid=' + deptid, null,
        //         function (r) {
        //             var data = r.operationResult;
        //             if (data.isSuccess) {
        //                 //库存参数中启用了部门仓库控制
        //                 var stockInfo = data.srvData;
        //                 if (stockInfo) {
        //                     //设置默认仓库
        //                     if (isMatChange) {
        //                         that.Model.setValue({ id: 'fstorehouseid', row: rowIndex, value: stockInfo.id });
        //                     }
        //                     else {
        //                         var ds = that.Model.getEntryData({ id: 'fentity' });
        //                         for (var i = 0, j = ds.length; i < j; i++) {
        //                             that.Model.setValue({ id: 'fstorehouseid', row: ds[i].id, value: stockInfo.id });
        //                         }
        //                     }
        //                 }
        //                 else {
        //                     //清空仓库
        //                     if (isMatChange) {
        //                         that.Model.setValue({ id: 'fstorehouseid', row: rowIndex, value: "" });
        //                     }
        //                     else {
        //                         var ds = that.Model.getEntryData({ id: 'fentity' });
        //                         for (var i = 0, j = ds.length; i < j; i++) {
        //                             that.Model.setValue({ id: 'fstorehouseid', row: ds[i].id, value: '' });
        //                         }
        //                     }
        //                 }
        //             } else {

        //             }
        //         }, null, null, null, { async: false }
        //     );

        // }







        //重新计算明细
        _child.prototype.culFbill = function (e) {
            var that = this;
            var ds = that.Model.getEntryData({ id: that.fentity });
            var totalpackageQty = 0;
            var totalcubeQty = 0;
            if (ds && ds.length > 0) {
                for (var i = 0, l = ds.length; i < l; i++) {
                    totalpackageQty += yiMath.toNumber(ds[i].fpackageqty);
                    totalcubeQty += yiMath.toNumber(ds[i].fcubeqty);
                }
            }
            that.Model.setValue({ id: 'ftotalpackageqty', value: totalpackageQty });
            that.Model.setValue({ id: 'ftotalcubeqty', value: totalcubeQty });
        }

        //表单操作前触发
        _child.prototype.onBeforeDoOperation = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'save':
                    var fstatus = that.Model.getSimpleValue({ id: 'fstatus' });
                    var fhomedelivery = that.Model.getSimpleValue({ id: 'fhomedelivery' });
                    if (that.isOutAutoNewService === true && fstatus != "E" && fhomedelivery === "") {
                        //判断当前出库单的所有商品行中【仓库】对应的【仓库类型】都为“总仓”时，并【是否送货上门】为空，则需反写【是否送货上门】=“是”。
                        var fstorehouseids = [];
                        var entryData = that.Model.getEntryData({ id: that.fentity });
                        entryData.forEach(o => {
                            if (o.fmaterialid.id != "" && o.fstorehouseid.id != "") {
                                fstorehouseids.push(o.fstorehouseid.id)
                            }
                        });

                        var parm = {
                            simpledata: {
                                fstorehouseids: fstorehouseids
                            }
                        };

                        yiAjax.p('/bill/stk_sostockout?operationno=queryentrywarehousetype', parm, function (r) {
                            var res = r.operationResult;
                            if (res.isSuccess && res.srvData === true) {
                                that.Model.setValue({ id: 'fhomedelivery', value: "01" });
                            }
                        }, null, null, null, { async: false });
                    }
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            switch (e.opcode) {
                //case 'isexsitbarcode':
                //    //if (!isSuccess) {
                //    //    yiDialog.warn("当前商品未查询到对应条码信息!");
                //    //    return false;
                //    //}
                //    //弹出《条码主档》列表界面
                //    var filterString = "fmainorgid = '{0}' and fid in ({1})"
                //        .format(Consts.loginCompany.id, srvData.split(','));
                //    that.Model.showForm({
                //        formId: 'bcm_barcodemaster',
                //        domainType: Consts.domainType.list,
                //        param: {
                //            openStyle: Consts.openStyle.modal,
                //            filterstring: filterString
                //        }
                //    });
                //    break;

                case 'getfifostock':
                    that.fillFIFOStock(srvData);
                    break;
                case 'save':
                    if (isSuccess) {
                        var pkid = srvData.length > 0 ? srvData[0].id : '';
                        that.Model.invokeFormOperation({
                            id: 'UpdatePrice',
                            opcode: 'UpdatePrice',
                            param: {
                                pkid: pkid
                            }
                        });
                    }
                    break;
            }
        };

        _child.prototype.culNum = function (opt) {
            var that = this;
            //行对象
            var row = that.Model.getEntryRowData({ id: that.fentity, row: opt.rowId });

            if (!row) return;

            if (row.fbizorderqty != row.fbizqty) {
                //如果不全部出库，则出库单价和之前不等时，设置为之前的成交单价
                var parm = that.prodRecord[opt.rowId];
                if (parm && parm.fprice !== row.fprice) {
                    that.Model.setValue({ id: 'fprice', row: opt.rowId, value: parm.fprice });
                }
            }
            else {
                that.dealfamount(row);
            }
        };

        // 处理下推金额
        _child.prototype.dealfamount = function (row) {
            var that = this;

            var soorderentryidarr = [];
            var ds = that.Model.getEntryData({ id: that.fentity });
            if (!row) {
                ds.forEach(e => {
                    if (e.fsoorderentryid != "") {
                        soorderentryidarr.push(e.fsoorderentryid);
                    }
                });
            }
            else {
                soorderentryidarr.push(row.fsoorderentryid);
            }

            var param = {
                simpleData: {
                    formId: 'stk_sostockout',
                    fsoorderentryid: soorderentryidarr.join(','),
                    pkId: that.Model.pkid,
                    domainType: 'dynamic'
                }
            };

            if (soorderentryidarr.join(',') != "") {
                yiAjax.p('/bill/stk_sostockout?operationno=queryprice', param, function (r) {
                    var res = r.operationResult;
                    if (res.isSuccess && res.srvData) {
                        ds.forEach(e => {
                            var product = res.srvData.find(c => c.soorderEntryid == e.fsoorderentryid);
                            //最后一次出库时处理差额
                            if (product && product.orderQty == e.fqty + product.outQty) {
                                //如果 当前金额+当前商品其他已出库金额!=合同金额 或者 当前金额!=下推金额
                                var famount = yiMath.toDecimal(e.fqty * e.fprice, 2); //当前金额
                                var outAmount = Math.floor(product.outAmount * 100) / 100;//其他已出库金额
                                var orderAmount = yiMath.toNumber(product.orderAmount);//合同金额
                                if (yiMath.toNumber(famount) + outAmount != orderAmount || famount != e.famount) {
                                    var parm = {
                                        fprice: that.Model.getValue({ id: "fprice", row: e.id }),//明细行成交单价
                                    }
                                    that.prodRecord[e.id] = parm;//记录原有成交单价

                                    that.Model.setValue({ id: 'famount', row: e.id, value: orderAmount - outAmount });
                                    that.Model.setValue({ id: 'fprice', row: e.id, value: (orderAmount - outAmount) / e.fqty, tgChange: false });
                                }
                            }
                        });
                    }
                }, null, null, null, { async: false });
            }
        }

        // 选单
        _child.prototype.pull = function (e) {
            var that = this;
            /*
               1、当销售出库单.客户不为空时
               a、点<选单>，进入《销售合同-列表》，需要自动匹配筛选 “ 销售合同的【客户】等于销售出库单所选择的当前客户 且 合同商品明细行的【销售数量】大于 源单合同商品明细已关联销售出库明细的【实发数量】汇总 ” 这一类销售合同数据范围供选择；
               b、当销售出库单重新选择客户，需要直接清空原已选择的所有源单信息；
            */
            // var fcustomerid = that.Model.getSimpleValue("fcustomerid");
            // if (fcustomerid){
            //     e.param.filterString += ' fcustomerid='+fcustomerid+' and fid in (select fid from t_orderentry where fbizqty) '
            // }
            var fcustomerid = that.Model.getSimpleValue({ id: "fcustomerid" });
            if (fcustomerid) {
                e.param.fcustomerid = fcustomerid;
            }
            var sourceNumber = that.Model.getValue({ id: 'fsourcenumber' });
            if (sourceNumber) {
                e.param.sourceNumber = sourceNumber;
            }
        };

        // 清空源单信息
        _child.prototype.clearEntry = function () {
            var that = this;
            that.Model.deleteEntryData({ id: that.fentity });
            // 添加空行
            that.Model.addRow({ id: that.fentity });
        }


        //获取先进先出库位
        _child.prototype.getFIFOStock = function (rowId) {
            if (!rowId) {
                return;
            }
            var that = this;
            var vm = that.Model.viewModel;
            var rowData = that.Model.getEntryRowData({ id: that.fentity, row: rowId });
            if (!rowData) {
                return;
            }

            if (rowData.fqty <= 0) {
                return;
            }

            var fisnofifostock = rowData.fisnofifostock;
            if (fisnofifostock) {
                return;
            }

            // 设置了仓库和仓位的，不触发获取
            if (rowData.fstorehouseid && rowData.fstorehouseid.id.trim() && rowData.fstorelocationid && rowData.fstorelocationid.id.trim()) {
                return;
            }

            that.Model.invokeFormOperation({
                id: 'getfifostock',
                opcode: 'getfifostock',
                param: {
                    formId: vm.formId,
                    rowData: JSON.stringify(rowData),
                    rowId: rowId
                }
            });

            if (that.notGetFIFOStock.hasOwnProperty(rowId)) {
                delete that.notGetFIFOStock[rowId];
            }
        }

        //填充推荐库位
        _child.prototype.fillFIFOStock = function (srvData) {
            var that = this;
            if (srvData && srvData.rowDatas) {

                var rowDatas = srvData.rowDatas;
                var firstRowData = rowDatas[0];
                var rowId = srvData.rowId;

                //先清理标记位，避免死循环
                that.notGetFIFOStock[rowId] = "";

                that.Model.setValue({ id: 'fstorehouseid', value: firstRowData.fstorehouseid, row: rowId });
                that.Model.setValue({ id: 'fstorelocationid', value: firstRowData.fstorelocationid, row: rowId });
                that.Model.setValue({ id: 'fstockstatus', value: firstRowData.fstockstatus, row: rowId });
                that.Model.setValue({ id: 'fstockqty', value: firstRowData.fstockqty, row: rowId });
                that.Model.setValue({ id: 'fqty', value: firstRowData.fqty, row: rowId, tgChange: false });

                if (rowDatas.length > 1) {
                    for (var i = rowDatas.length - 1; i > 0; i--) {
                        var rowData = rowDatas[i];
                        var id = that.Model.addRow({ id: that.fentity, row: rowId, data: rowData });

                        that.notGetFIFOStock[id] = "";
                    }
                }
            }
        }

        //设置必录标签
        _child.prototype.setFieldMustFlag = function (e) {
            var that = this;
            var elem = that.Model.getEleMent({ id: '[name=' + e.id + ']' });
            if (elem) {
                var $label = elem.parent().siblings('.control-label');
                if ($label) {
                    if (e.must) {
                        that.Model.setAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html('<span class="required">*</span>' + e.caption);
                    } else {
                        that.Model.removeAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html(e.caption);
                    }
                }
            }
        };
        // 库存查询设置了标准字段后的回调，每行返回了rownumber
        _child.prototype.queryInventoryCallback = function (e) {
            var srvData = e.srvData;
            var that = this;

            //恢复标记位
            that.isqueryinventory = false;

            if (!srvData || !srvData.length) return;
        }


        //设置一件代发按钮显示隐藏
        _child.prototype.setDropShipMentMenuVisible = function () {

            var that = this;
            debugger
            var fpiecesendtag = that.Model.getValue({ id: 'fpiecesendtag' });
            if (fpiecesendtag) {
                var entryData = that.Model.getEntryData({ id: that.fentity });//判断是否已转采购
                if (entryData.filter(a => a.fdeliverytype?.id == 'delivery_type_01').length > 0) {
                    that.Model.setVisible({ id: '[menu=save]', value: false });
                    that.Model.setVisible({ id: '[menu=savesubmit]', value: false });
                    that.Model.setVisible({ id: '[menu=saveaudit]', value: false });
                    that.Model.setVisible({ id: '[menu=submit]', value: false });
                    that.Model.setVisible({ id: '[menu=submitflow]', value: false });
                    that.Model.setVisible({ id: '[menu=audit]', value: false });
                    that.Model.setVisible({ id: '[menu=auditflow]', value: false });
                    that.Model.setVisible({ id: '[menu=delete]', value: false });
                    that.Model.setVisible({ id: '[menu=cancel]', value: false });
                    that.Model.setVisible({ id: '[menu=change]', value: false });
                }
            }
        }


        //设置一件代发显示隐藏
        //_child.prototype.setDropShipMentVisible = function () {

        //    var that = this;

        //    var flag = false;
        //    var billtypename = that.Model.getValue({ id: "fbilltype" })?.fname;
        //    if ((billtypename == "v6定制柜合同" || billtypename == "v6定制柜合同")) {
        //        flag = false;
        //    } else {
        //        var parm = {
        //        };
        //        yiAjax.p('/bill/ydj_order?operationno=getagentcustomchannel', parm, function (r) {
        //            if (r) {
        //                if (r.operationResult?.srvData) {
        //                    var fcustomchannel = r.operationResult.srvData["fcustomchannel"];
        //                    if (fcustomchannel == "1") {
        //                        flag = true;
        //                    }
        //                }
        //            }
        //        }, null, null, null, { async: false });
        //    }

        //    that.Model.setVisible({ id: '.dropshipping-info-tag', value: flag });//一口价
        //}

        //设置一件代发显示隐藏
        _child.prototype.setDropShipMentVisible = function () {
            var that = this;
            var flag = false;
            var billtypename = that.Model.getValue({ id: "fbilltype" })?.fname;
            if ((billtypename == "v6定制柜合同" || billtypename == "v6定制柜合同")) {
                flag = false;
            } else {
                var parm = {
                };
                yiAjax.p('/bill/ydj_order?operationno=getagentcustomchannel', parm, function (r) {
                    if (r) {
                        if (r.operationResult?.srvData) {
                            var fcustomchannel = r.operationResult.srvData["fcustomchannel"];
                            if (fcustomchannel == "1") {
                                flag = true;
                            }
                            var ftoppiecesendtag = r.operationResult.srvData["ftoppiecesendtag"];
                            var fmanagemodel = r.operationResult.srvData["fmanagemodel"];
                            if (fmanagemodel == 1 && ftoppiecesendtag) {
                                flag = true;
                            } else if (fmanagemodel == 1 && !ftoppiecesendtag) {
                                flag = false;
                            }
                        }
                    }
                }, null, null, null, { async: false });
            }
            that.Model.setVisible({ id: '.dropshipping-info-tag', value: flag });//一件代发相关字段
        }

        return _child;
    })(BasePlugIn);
    window.stk_sostockout = window.stk_sostockout || stk_sostockout;
})();