///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/stk_initstockbill.js
*/
; (function () {
    var stk_initstockbill = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = 'fentity';
        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.onBillInitialized = function (args) {
            var that = this;

        };

        //批量转标准品 将当前页面的
        _child.prototype.showstandardcustombatch = function (e) {
            debugger;
            var that = this;
            var rows=[];
            if (that.Model.viewModel.domainType === 'list') {
                rows = that.Model.getAllSelectedRows();
            } else {
                var obj = {
                    'data':that.Model.uiData,
                    'pkValue':that.Model.uiData.id
                };
                rows.push(obj);
            }
           
            if (rows.some(o => o.data.fstatus == "E" || o.data.fstatus.id == "E"))
            {
                yiDialog.warn('不允许选择已审核的初始库存单转标准品！');
                e.result = true;
                return;
            }
            //获取当前明细数据
            var rowData = that.Model.getEntryData({ id: that.fentity });
            that.Model.invokeFormOperation({
                id: 'standardbustombatch',
                opcode: 'standardbustombatch',
                param: {
                    formId: 'stk_initstockbill',
                    allselectrows: JSON.stringify(rows),
                    domainType: 'dynamic'
                }
            });
        };


        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            switch (e.opcode) {
                case 'showstandardcustombatch':
                    e.result = true;
                    that.showstandardcustombatch(e);
                    break;
            }
        };


        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'standardbustombatch':
                    debugger;
                    if (isSuccess && srvData) {
                        debugger;
                        //如果匹配到标准品商品id则直接更新商品
                        if (srvData.length > 0) {
                            //刷新页面
                            that.Model.refresh();
                        }
                    }
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.stk_initstockbill = window.stk_initstockbill || stk_initstockbill;
})();