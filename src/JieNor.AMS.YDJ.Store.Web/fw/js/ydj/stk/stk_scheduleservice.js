; (function () {
    var stk_scheduleservice = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);
		
		//初始化编辑页面插件
        _child.prototype.onInitialized  = function (args) {
        	var that = this;
        };
		//字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;

            switch (e.id.toLowerCase()) {
                
                case 'fqty_s':
                case 'fprice_s':
                    //计算
                    that.change({ rowId: e.row, value: e.value });
                    break;
            }
        }
        _child.prototype.change = function (opt) {
            var that = this;
            
            //行对象
            var row = that.Model.getEntryRowData({ id: 'fschentry', row: opt.rowId });

            //数量
            var qty = yiMath.toNumber(row.fqty_s);

            //单价
            var price = yiMath.toNumber(row.fprice_s);

            //表体金额 = 表体数量 * 表体单价
            var amount = qty * price;

            //更新金额
            that.Model.setValue({ id: 'famount_s', row: opt.rowId, value: yiMath.toDecimal(amount, 2) });
        }
		//处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
             	case 'cancel':
                    
                    args.result = true;
                    that.Model.close();
                    break;
                case 'confirm':
					//数据回传
					var simpledata={

					};
                    var _entrys=this.Model.getValue({id:'fschentry'});
                    var _res=[];
                    
                    for(var i=0,l=_entrys.length; i<l; i++){
                        var lm=_entrys[i];
                        if(lm.fseritemid_s && lm.fseritemid_s.id){
                            _res.push(lm);//如果没有服务项目，就不用存储
                        }
                    }
					
					//还有开始时间和结束时间。
					//还需要一个预置条件，如果有些字段没填，就不允许发起请求。并提示
					that.Model.setReturnData({ data: _res });
					//关闭当前弹窗
					that.Model.close();
                    args.result = true;
                	
                    break;
            }
        };
        
        
        
		
        return _child;
    })(BasePlugIn);
    window.stk_scheduleservice = window.stk_scheduleservice || stk_scheduleservice;
})();