<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）

    条码主档    
-->
<html lang="en">
<head>
</head>
<body id="bcm_barcodemaster" basemodel="bd_basetmpl" el="3" cn="条码主档" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_bcm_barcodemaster" pn="fbillhead" cn="条码主档">
        <!--重定义基类字段名称-->
        <input type="text" id="fnumber" el="108" ek="fbillhead" cn="条码" lix="1" />
        <input type="text" id="fname" el="100" ek="fbillhead" cn="名称" lix="2" visible="1150" />
        <input type="text" id="fcreatorid" el="118" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fcreatorid" pn="fcreatorid" cn="创建人" copy="0" visible="1150" lix="250" xlsin="0" />
        <input type="datetime" id="fmodifydate" el="121" ek="fbillhead" fn="fmodifydate" pn="fmodifydate" cn="修改日期" visible="-1" copy="0" lix="253" xlsin="0" />
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="1150" xlsin="0" copy="0" lix="260" align="center"></select>
        <input type="text" id="fdescription" el="100" ek="fbillhead" cn="备注" visible="-1" lock="-1" lix="500" />

        <input group="基本信息" el="100" ek="fbillhead" id="fpackgroup" fn="fpackgroup" pn="fpackgroup" visible="1150" cn="打包批次"
               lock="-1" copy="0" lix="2" notrace="true" ts="" />

        <input group="基本信息" el="152" ek="fbillhead" id="fpackagtype" fn="fpackagtype" pn="fpackagtype" visible="1150" cn="打包类型"
               lock="-1" vals="'1':'标准','2':'1件多包','3':'1包多件'" defVal="'3'" lix="3" />

        <input group="基本信息" el="101" ek="fbillhead" id="fpackcount" fn="fpackcount" pn="fpackcount" visible="-1" cn="总包数"
               lock="-1" copy="0" lix="3" notrace="true" ts="" format="0,000" defval="1" />
        <input group="基本信息" el="101" ek="fbillhead" id="fpackindex" fn="fpackindex" pn="fpackindex" visible="-1" cn="包序号"
               lock="-1" copy="0" lix="4" notrace="true" ts="" format="0,000" defval="1" />

        <input group="基本信息" el="152" ek="fbillhead" id="fbizstatus" fn="fbizstatus" pn="fbizstatus" visible="-1" cn="业务状态"
               lock="-1" copy="0" lix="6" notrace="true" ts="" vals="'1':'可用','2':'已出库','4':'待入库','5':'已备货'" defval="'4'" />

        <input group="基本信息" el="106" ek="fbillhead" id="fstorehouseid" fn="fstorehouseid" pn="fstorehouseid" visible="-1" cn="当前仓库"
               lock="-1" copy="1" lix="50" notrace="true" ts="" refid="ydj_storehouse" filter="" reflvt="0" dfld="" />
        <input group="基本信息" el="153" ek="fbillhead" id="fstorelocationid" fn="fstorelocationid" pn="fstorelocationid" visible="-1" cn="当前仓位"
               lock="-1" copy="1" lix="60" notrace="true" ts="" ctlfk="fstorehouseid" />

        <input el="140" ek="fbillhead" id="fsourcetype" fn="fsourcetype" pn="fsourcetype" apipn="sourceType" cn="来源单据" visible="-1" copy="0" lock="-1" lix="70">
        <input el="141" ek="fbillhead" id="fsourcenumber" fn="fsourcenumber" pn="fsourcenumber" apipn="sourceNumber" cn="来源单据编号" ctlfk="fsourcetype" visible="-1" copy="0" lock="-1" lix="71">
        <input el="100" ek="fbillhead" id="fsourcelinenumber" fn="fsourcelinenumber" pn="fsourcelinenumber" ts="" cn="来源单据行号" visible="1150" copy="0" lock="-1" lix="72" />
        <input el="100" ek="fbillhead" id="fsourceentryid" fn="fsourceentryid" ts="" cn="来源单分录内码" visible="0" copy="0" lix="200" lock="-1" />
        <input el="100" ek="fbillhead" id="fsourcebillid" fn="fsourcebillid" ts="" cn="来源单内码" visible="0" copy="0" lix="200" lock="-1" />
        <input el="100" ek="fbillhead" id="frecnum" fn="frecnum" pn="frecnum" ts="" cn="收货单号" visible="1150" copy="0" lock="-1" lix="72" />

        <input group="基本信息" el="100" ek="fbillhead" id="fsuffix" fn="fsuffix" pn="fsuffix" visible="0" cn="后缀"
               lock="-1" copy="0" lix="2" notrace="true" ts="" />

        <input el="106" ek="fbillhead" id="finisrcformid" fn="finisrcformid" pn="finisrcformid" ts="" cn="初始来源单据" refid="bcm_receptionscantask" visible="1148" copy="0" lix="360" lock="-1" />
        <input el="141" ek="fbillhead" id="finisrcbillno" fn="finisrcbillno" pn="finisrcbillno" ts="" cn="初始来源单据编号" visible="1148" copy="0" lix="365" lock="-1" />
        <input el="100" ek="fbillhead" id="finisrcinterid" fn="finisrcinterid" pn="finisrcinterid" ts="" cn="初始来源单内码" visible="1148" copy="0" lix="370" lock="-1" />
        <input el="100" ek="fbillhead" id="finisrcentryid" fn="finisrcentryid" pn="finisrcentryid" ts="" cn="初始来源单行内码" visible="1148" copy="0" lix="370" lock="-1" width="100" />
        <input el="100" ek="fbillhead" id="finisrcseq" fn="finisrcseq" pn="finisrcseq" ts="" cn="初始来源单行号" visible="1148" copy="0" lix="375" lock="-1" />

        <input el="100" ek="fbillhead" id="fmainsncode" fn="fmainsncode" pn="fmainsncode" ts="" cn="总部SN码" visible="1150" copy="0" lock="-1" lix="72" />
        <input el="100" ek="fbillhead" id="fmainsecuritycode" fn="fmainsecuritycode" pn="fmainsecuritycode" ts="" cn="总部防伪码" visible="1150" copy="0" lock="-1" lix="72" />

        <input el="116" ek="fbillhead" id="fisscannedcode" fn="fisscannedcode" pn="fisscannedcode" cn="是否已收货扫码" visible="1150" defval="false" lock="-1" />


        <input type="text" id="fprimitiveid" el="100" ek="fbillhead" remove />

    </div>

    <!--商品明细-->
    <table id="fentity" el="52" pk="fentryid" tn="t_bcm_mastermtrlentry" pn="fentity" cn="商品明细" >
        <tr>
            <th el="100" ek="fentity" id="fbarcode" fn="fbarcode" pn="fbarcode" visible="1150" cn="合包子条码"
                lock="-1" copy="1" lix="99" notrace="true" ts=""></th>

            <th el="100" ek="fentity" id="fserialno" fn="fserialno" pn="fserialno" visible="1150" cn="序列号"
                lock="-1" copy="-1" lix="100" notrace="true" ts=""></th>

            <th el="107" ek="fentity" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="1150" cn="商品编码"
                lock="-1" lix="101" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber"></th>

            <th el="106" ek="fentity" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" visible="1150" cn="商品名称"
                lock="-1" copy="1" lix="105" notrace="true" ts="" refid="ydj_product" filter="" reflvt="0" dfld="fspecifica" sformid=""></th>

            <th el="132" ek="fentity" id="fattrinfo" fn="fattrinfo" cn="辅助属性" lix="107" ctlfk="fmaterialid" pricefk="" width="140" visible="1150" lock="-1"></th>
            <th el="100" ek="fentity" id="fcustomdesc" fn="fcustomdesc" cn="定制说明" lix="108" width="140" visible="1150" lock="-1" len="2000"></th>

            <th el="103" ek="fentity" id="fstockqty" fn="fstockqty" cn="库存单位数量" lix="109" visible="1150" ctlfk="fstockunitid" basqtyfk="fqty" lock="-1" width="100" format="0,000.00"></th>
            <th el="109" ek="fentity" id="fstockunitid" fn="fstockunitid" cn="库存单位" lix="110" ctlfk="fmaterialid" refid="ydj_unit" sformid="" visible="1150" width="80" lock="-1"></th>

            <th el="100" ek="fentity" id="flotno" fn="flotno" pn="flotno" visible="1150" cn="批号"
                lock="-1" copy="1" lix="125" notrace="true" ts="" />
            <th el="100" ek="fentity" id="fmtono" fn="fmtono" pn="fmtono" visible="1150" cn="物流跟踪号"
                lock="-1" copy="1" lix="126" notrace="true" ts="" />

            <th el="149" ek="fentity" id="fownertype" fn="fownertype" pn="fownertype" lix="127" dataviewname="v_bd_ownerdata" cn="货主类型" width="100" visible="1150" lock="-1">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
            </th>
            <th el="150" ek="fentity" id="fownerid" fn="fownerid" pn="fownerid" lix="128" ctlfk="fownertype" cn="货主" width="100" visible="1150" lock="-1"></th>

            <th el="107" ek="fentity" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="1150" cn="规格型号"
                lock="-1" copy="1" lix="128" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fspecifica" refvt="0"></th>

            <th lix="129" el="107" ek="fentity" id="fbrandid" fn="fbrandid" cn="品牌" visible="1150" dispfk="fbrandid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>
            <th lix="130" el="107" ek="fentity" id="fseriesid" fn="fseriesid" cn="系列" visible="1150" dispfk="fseriesid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>
            <th lix="131" el="107" ek="fentity" id="fsubseriesid" fn="fsubseriesid" cn="子系列" visible="1086" dispfk="fsubseriesid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>

            <th lix="131" el="100" ek="fentity" visible="1150" id="fsuitcombnumber" fn="fsuitcombnumber" pn="fsuitcombnumber" cn="套件组合号" lock="-1"></th>
            <th lix="132" el="100" ek="fentity" visible="1150" id="fpartscombnumber" fn="fpartscombnumber" pn="fpartscombnumber" cn="配件组合号" lock="-1"></th>
            <th lix="133" el="100" ek="fentity" visible="1150" id="fsofacombnumber" fn="fsofacombnumber" pn="fsofacombnumber" cn="沙发组合号" lock="-1"></th>

            <th lix="150" el="103" ek="fentity" id="fqty" fn="fqty" cn="基本单位数量" visible="1150" ctlfk="funitid" width="80" format="0,000.00" lock="-1"></th>
            <th lix="151" el="109" ek="fentity" id="funitid" fn="funitid" cn="基本单位" ctlfk="fmaterialid" refid="ydj_unit" sformid="" visible="1150" width="80" lock="-1"></th>

            <th lix="152" el="106" ek="fentity" id="fstockstatus" fn="fstockstatus" cn="库存状态" defVal="'311858936800219137'" refid="ydj_stockstatus" sformid="" visible="0" width="100" lock="-1"></th>

            <th lix="153" el="112" ek="fentity" id="finstockdate" fn="finstockdate" pn="finstockdate" visible="0" cn="入库日期" lock="-1" copy="0" notrace="true" ts=""></th>

            <th id="fseltypeid" el="107" ek="fentity" fn="fseltypeid" ctlfk="fmaterialid" dispfk="fseltypeid" ts="" cn="型号" visible="-1" lix="154">型号</th>

            <th el="100" lix="20" ek="fentity" id="fentrynote" fn="fentrynote" pn="fentrynote" visible="-1" cn="备注" len="4000" />

            <th el="100" lix="160" ek="fentity" id="fmainsncode_e" fn="fmainsncode" pn="fmainsncode" visible="1150" cn="总部子SN码" />
            <th el="100" lix="161" ek="fentity" id="fmainsecuritycode_e" fn="fmainsecuritycode" pn="fmainsecuritycode" visible="1150" cn="总部子防伪码" />
        </tr>
    </table>

    <!--追溯明细,该明细不存储，只是用于前端动态显示(取条码扫描记录数据)---->
    <table id="ftraceentity" el="52" pk="fentryid" pn="ftraceentity" cn="追溯明细">
        <tr>
            <th el="100" ek="ftraceentity" id="fsourceformid_fname" fn="" pn="fsourceformid_fname" ts="" cn="业务单据" visible="1150" copy="0" lix="250" lock="-1"></th>
            <th el="100" ek="ftraceentity" id="fsourcebillno" fn="" pn="fsourcebillno" ts="" cn="业务单据编号" visible="1150" copy="0" lix="251" lock="-1"></th>

            <th el="112" ek="ftraceentity" id="fcreatedate_e" fn="" pn="fcreatedate" visible="1150" cn="业务日期"
                lock="-1" copy="0" lix="253" notrace="true" ts=""></th>
            <th el="100" ek="ftraceentity" id="foperatorid_fname" fn="" pn="foperatorid_fname" visible="1150" cn="操作人"
                lock="-1" copy="0" lix="254" notrace="true" ts=""></th>
            <th el="113" ek="ftraceentity" id="fopdatetime" fn="" pn="fopdatetime" visible="1150" cn="操作时间"
                lock="-1" copy="0" lix="255" notrace="true" ts=""></th>

            <th el="100" ek="ftraceentity" id="fstorehousename" fn="" pn="fstorehousename" ts="" cn="仓库" visible="1150" copy="0" lix="257" lock="-1"></th>
            <th el="100" ek="ftraceentity" id="fstorelocationname" fn="" pn="fstorelocationname" ts="" cn="仓位" visible="1150" copy="0" lix="258" lock="-1"></th>

            <th el="100" ek="ftraceentity" id="fscansceneid_txt" fn="" pn="fscansceneid_txt" visible="1150" cn="业务操作"
                lock="-1" copy="0" lix="259" notrace="true" ts=""/>

            <th el="100" ek="ftraceentity" id="fremark" fn="" pn="fremark" visible="1150" cn="备注"
                lock="-1" copy="0" lix="260" notrace="true" ts=""></th>
            <th el="100" ek="ftraceentity" id="fpda" fn="" pn="fpda" cn="PDA" visible="1150" lix="261" width="100" lock="-1"></th>
        </tr>
    </table>

    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="">
            <!--去掉名称唯一性校验-->
            <li el="11" id="save_valid_fname" remove></li>
        </ul>

    </div>
    <!--表单所涉及的权限项定义-->
    <div id="permList">

    </div>
</body>
</html>