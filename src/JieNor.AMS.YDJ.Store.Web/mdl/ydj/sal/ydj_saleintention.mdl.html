<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_saleintention" basemodel="bill_basetmpl" el="1" cn="销售意向" approvalflow="true" btfk="fbilltypeid" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_saleintention" pn="fbillhead" cn="销售意向">

        <!--重写基类模型中的部分字段属性-->
        <input id="fbillno" el="108" visible="-1" lix="1" width="115" align="center" />
        <input id="fcreatorid" el="118" visible="0" lix="9" width="80" />
        <input id="fcreatedate" el="119" visible="-1" lix="31" width="125" />
        <input id="fdescription" el="100" visible="1150" />

        <!--基本信息-->
        <input group="基本信息" el="123" ek="fbillhead" visible="-1" id="fbilltypeid" fn="fbilltypeid" pn="fbilltypeid" refid="bd_billtype" cn="单据类型" copy="0" lix="2" />
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="ftype" fn="ftype" pn="ftype" cn="业务类型" cg="业务类型" refid="bd_enum" dfld="fenumitem" defval="'order_type_01'" lix="4" width="90" apipn="type"></select>
        <input group="基本信息" el="112" ek="fbillhead" visible="-1" id="fdate" fn="fdate" cn="业务日期" defval="@currentshortdate" copy="0" width="90" lix="6" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fcustomerid" fn="fcustomerid" cn="客户" refid="ydj_customer" width="90" lix="8" dfld="fname,fcontacts,fphone" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fphone_e" fn="fphone_e" pn="fphone_e" cn="手机号" width="80" lix="10" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="销售部门" refid="ydj_dept" dfld="fname" lix="12" defVal="@currentDeptId" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstaffid" fn="fstaffid" pn="fstaffid" cn="销售员" refid="ydj_staff" dfld="fname" lix="14" defVal="@currentStaffId" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstylistid" fn="fstylistid" pn="fstylistid" cn="设计师" filter="fbiztype = '5'" refid="ydj_staff" apipn="designer" lix="16" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="faddress_e" fn="faddress_e" cn="详细地址" lix="18" />
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" id="fispushorder" fn="fispushorder" cn="已下推合同" copy="0" lock="-1" lix="20" width="85" />
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="-1" lix="27"></select>
        <input el="119" ek="fbillhead" id="fcreatedate" fn="fcreatedate" cn="创建日期" width="130" visible="1150" copy="0" lix="251" />

        <input group="基本信息" el="112" ek="fbillhead" visible="1150" id="fpickdate" fn="fpickdate" cn="交货日期" copy="0" width="105" lix="40" />
        <input el="107" ek="fbillhead" visible="1150" id="fcustomerstatus" fn="fcustomerstatus" cn="客户协同状态" ctlfk="fcustomerid" dispfk="fcoostate" copy="0" lix="42" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fcommercebillno" fn="fcommercebillno" pn="fcommercebillno" cn="电商订单号" lix="44" />
        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="fchannel" fn="fchannel" pn="fchannel" cn="合作渠道" refid="ste_channel" dfld="fname" lix="46" />
        <input group="基本信息" el="100" ek="FBillHead" id="flinkstaffid" fn="flinkstaffid" pn="flinkstaffid" visible="1150" cn="联系人"
               lock="0" copy="1" lix="48" notrace="true" ts="" canchange="true" />

        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem" lix="50"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem" lix="52"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem" lix="54"></select>

        <input group="基本信息" el="106" type="text" id="fbuildingid" ek="fbillhead" fn="fbuildingid" pn="fbuildingid" refid="ydj_building" ts="" visible="1150" cn="楼盘" lix="56" />


        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="fbrandid_e" fn="fbrandid_e" pn="fbrandid_e" cn="品牌" refid="ydj_brand" width="90" lix="58" apipn="brand" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fdesignscheme" fn="fdesignscheme" pn="fdesignscheme" cn="设计方案" lock="-1" lix="60" copy="0" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fscalerecord" fn="fscalerecord" pn="fscalerecord" cn="量尺记录" lock="-1" lix="62" copy="0" />
        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="fmeasurerid" fn="fmeasurerid" pn="fmeasurerid" cn="量尺员" filter="fbiztype = '5'" refid="ydj_staff" apipn="measurer" lix="64" />

        <input group="基本信息" el="106" ek="FBillHead" id="finnercustomerid" fn="finnercustomerid" pn="finnercustomerid" visible="1150" cn="加盟商"
               lock="-1" copy="0" lix="66" notrace="true" ts="" refid="ydj_customer" filter="" reflvt="0" dfld="" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fmallorderno" fn="fmallorderno" pn="fmallorderno" cn="商场合同号" lix="68" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fhqderno" fn="fhqderno" pn="fhqderno" cn="总部合同号" lock="-1" lix="70" />
        <input el="100" ek="fbillhead" id="fscancode" pn="fscancode" cn="商品扫码" visible="1150" copy="0" editmode="1" lix="72" />

        <!-- 协同信息 -->
        <input group="协同信息" el="100" ek="fbillhead" visible="32" id="fdept" fn="fdept" cn="门店(协同)" width="140" lock="-1" copy="0" lix="72" />
        <input group="协同信息" el="100" ek="fbillhead" visible="32" id="fstaff" fn="fstaff" cn="导购员(协同)" lock="-1" copy="0" lix="74" />
        <input group="协同信息" el="100" ek="fbillhead" visible="32" id="fstylist" fn="fstylist" cn="设计师(协同)" width="80" copy="0" lock="-1" lix="76" />
        <input group="协同信息" el="100" ek="fbillhead" visible="32" id="fterminalcustomer" fn="fterminalcustomer" cn="终端客户(协同)" width="90" copy="0" lock="-1" lix="78" />
        <input group="协同信息" el="100" ek="fbillhead" visible="32" id="fphone" fn="fphone" cn="手机号(协同)" width="80" copy="0" lock="-1" lix="80" />
        <input group="协同信息" el="100" ek="fbillhead" visible="32" id="fprovincecityregion" fn="fprovincecityregion" cn="省市区(协同)" width="180" copy="0" lock="-1" lix="82" />
        <input group="协同信息" el="100" ek="fbillhead" visible="32" id="faddress" fn="faddress" cn="详细地址(协同)" copy="0" lock="-1" lix="84" />
        <input group="协同信息" el="100" ek="fbillhead" visible="1086" id="fdemandorderno" fn="fdemandorderno" cn="采购方订单号" copy="0" lock="0" lix="86" />
        <input group="协同信息" el="134" ek="fbillhead" visible="1150" id="fquotelist" fn="fquotelist" cn="报价清单" copy="0" lock="0" lix="88" />
        <input group="协同信息" el="152" type="text" ek="fbillhead" id="fchargebackstatus" fn="fchargebackstatus" cn="退单状态" vals="'0':'正常','1':'已退单'" visible="1150" lix="90" defval="'0'" lock="-1" />
        <input group="协同信息" el="100" type="text" ek="fbillhead" id="fchargebackreason" fn="fchargebackreason" cn="退单原因" visible="1150" lix="92" lock="-1" />

        <!--财务信息-->
        <input group="财务信息" el="105" ek="fbillhead" visible="1150" id="ffbillamount" fn="ffbillamount" cn="订单金额" lock="-1" width="90" lix="94" />
        <input group="财务信息" el="105" ek="fbillhead" visible="32" id="freceiptamount" fn="freceiptamount" cn="待结算金额" lock="-1" lix="96" />
        <input group="财务信息" el="105" ek="fbillhead" visible="32" id="freceivedamount" fn="freceivedamount" cn="已结算金额" lock="-1" copy="0" lix="98" />
        <input group="财务信息" el="105" ek="fbillhead" visible="32" id="fconfirmamount" fn="fconfirmamount" cn="待确认金额" lock="-1" copy="0" width="105" lix="100" />
        <input group="财务信息" el="105" ek="fbillhead" visible="-1" id="fcollectamount" fn="fcollectamount" cn="应收定金" width="90" copy="0" lix="24" />
        <input group="财务信息" el="105" ek="fbillhead" visible="-1" id="fcollectedamount" fn="fcollectedamount" cn="已收定金" lock="-1" width="90" copy="0" lix="25" />
        <input group="财务信息" el="105" ek="fbillhead" visible="-1" id="fconfirmedamount" fn="fconfirmedamount" cn="确认已收" lock="-1" width="90" copy="0" lix="22" />
        <select group="财务信息" el="122" ek="fbillhead" visible="1150" id="freceiptstatus" fn="freceiptstatus" cn="结算状态" refid="bd_enum" cg="收款状态" dfld="fenumitem" defval="'receiptstatus_type_01'" lock="-1" copy="0" width="90" align="center" lix="104"></select>
        <select group="财务信息" el="122" ek="fbillhead" visible="1150" id="fbizstatus" fn="fbizstatus" cn="业务状态" refid="bd_enum" dfld="fenumitem" cg="协同订单业务状态" defval="'business_status_01'" lock="-1" copy="0" width="90" align="center" lix="106"></select>
        <input group="财务信息" el="105" ek="fbillhead" visible="1150" id="factrefundamount" fn="factrefundamount" cn="实退金额" lock="-1" lix="108" />
        <!--后台字段-->
        <select group="后台字段" el="122" ek="fbillhead" visible="0" id="fpublishstatus" fn="fpublishstatus" cn="发布状态" refid="bd_enum" dfld="fenumitem" cg="协同订单发布状态" defval="'publish_status_01'" copy="0"></select>
        <input group="后台字段" el="100" ek="fbillhead" visible="0" id="fpublishcompany" fn="fpublishcompany" cn="发布企业" copy="0" />
        <input group="后台字段" el="100" ek="fbillhead" visible="0" id="fpublishcompanyid" fn="fpublishcompanyid" cn="发布企业id" copy="0" />
        <input group="后台字段" el="113" ek="fbillhead" visible="0" id="fpublishdate" fn="fpublishdate" pn="fpublishdate" cn="发布时间" copy="0" />

        <input group="后台字段" el="144" ek="fbillhead" visible="1150" id="foperate" fn="foperate" pn="foperate" cn="操作" width="260" lix="1000" />

        <input group="后台字段" el="100" ek="fbillhead" visible="0" id="forderid" fn="forderid" cn="销售合同主键Id" lock="-1" copy="0" desc="下推合同保存时反写" />

        <!--图纸信息-->
        <select group="图纸信息" el="122" ek="fbillhead" visible="32" id="fdrawtype" fn="fdrawtype" cn="图纸类型" refid="bd_enum" dfld="fenumitem" cg="图纸类型" copy="0"></select>
    </div>

    <!--销售员信息-->
    <table id="fdutyentry" el="52" pk="fentryid" tn="t_ydj_orderduty" pn="fdutyentry" cn="销售员信息" kfks="fdutyid,fratio" apipn="dutyEntry">
        <tr>
            <th el="116" ek="fdutyentry" id="fismain" fn="fismain" pn="fismain" cn="主要负责" width="80" visible="1124" lock="-1" apipn="isMain"></th>
            <th el="106" ek="fdutyentry" id="fdutyid" fn="fdutyid" pn="fdutyid" cn="销售员" refid="ydj_staff" sformid="" width="110" visible="1124" apipn="saleMan"></th>
            <th el="106" ek="fdutyentry" id="fdeptid_ed" fn="fdeptid" pn="fdeptid" cn="销售部门" refid="ydj_dept" sformid="" width="120" visible="1150" />
            <th el="102" ek="fdutyentry" id="fratio" fn="fratio" pn="fratio" cn="分成比例%" width="65" visible="1124" format="%" apipn="ratio"></th>
            <th el="105" ek="fdutyentry" id="famount_ed" fn="famount" pn="famount" cn="分成金额" width="100" lock="-1" visible="1124" format="0,000.00" apipn="amount"></th>
            <th el="100" ek="fdutyentry" id="fdescription_ed" fn="fdescription" pn="fdescription" cn="备注" width="170" visible="1124" apipn="description"></th>
        </tr>
    </table>

    <!--商品明细-->
    <table id="fentity" el="52" pk="fentryid" tn="t_ydj_saleentry" pn="fentity" cn="商品明细" kfks="fmaterialid" apipn="entity">
        <tr>
            <th el="106" lix="53"  ek="fentity" id="fsuitid" fn="fsuitid" pn="fsuitid" cn="所属套件" refid="ydj_suit" sformid="" visible="32" lock="-1"></th>
            <!--<th el="116" ek="fentity" id="fissplit" fn="fissplit" pn="fissplit" cn="不可拆卖" visible="96" lock="-1"></th>-->
            <th el="116" ek="fentity" id="fissplit" fn="fissplit" pn="fissplit" cn="不可拆卖" visible="0" lock="-1"></th>
            <th el="116" lix="52" ek="fentity" id="fisgiveaway" fn="fisgiveaway" pn="fisgiveaway" cn="赠品" visible="32" lock="-1"></th>
            <th ek="fentity" lix="1" el="107" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="1150" cn="商品编码"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0"></th>
            <th ek="fentity" lix="3" dfld="fname,fnumber,fimage,fbrandid,fseriesid,fcategoryid,foriginplace,fisfixprop,fspecifica,fguideprice"
                el="106" sformid="" sformtype="2" id="fmaterialid" fn="fmaterialid" cn="商品" visible="1150" refid="ydj_product"
                reflvt="1" multsel="true" width="160" filter="fispulloff='0'"></th>
            <th ek="fentity" lix="5" el="107" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="1124" cn="规格型号" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fspecifica"></th>
            <th ek="fentity" lix="7" el="132" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" cn="辅助属性" ctlfk="fmaterialid" pricefk="fprice" width="140" visible="1150" apipn="attrInfo"></th>
            <th ek="fentity" lix="13" el="109" id="fbizunitid" fn="fbizunitid" pn="fbizunitid" cn="销售单位" refid="ydj_unit" sformid="" ctlfk="fmaterialid" width="90" visible="1124" must="1"></th>
            <th ek="fentity" lix="15" el="103" id="fbizqty" fn="fbizqty" pn="fbizqty" cn="销售数量" ctlfk="fbizunitid" format="0,000.00" basqtyfk="fqty" width="80" visible="1124"></th>
            <th ek="fentity" lix="17" el="104" id="fprice" fn="fprice" cn="零售价" format="0,000.000000" dformat="0,000.00" visible="1150" width="100"></th>
            <th ek="fentity" lix="19" el="105" id="famount" fn="famount" cn="金额" format="0,000.00" visible="1150" width="100" lock="-1"></th>
            <th ek="fentity" lix="21" el="102" id="fdistrate" fn="fdistrate" pn="fdistrate" cn="折扣" width="60" visible="1124" format="0,000.00" apipn="distRate"></th>
            <th ek="fentity" lix="23" el="105" id="fdistamount_e" fn="fdistamount" pn="fdistamount" cn="折扣额" width="90" visible="1124" format="0,000.00" apipn="distAmount" lock="-1"></th>
            <th ek="fentity" lix="25" el="104" id="fdealprice" fn="fdealprice" pn="fdealprice" cn="成交单价" width="90" lock="-1" visible="1124" format="0,000.000000" dformat="0,000.00" apipn="dealPrice"></th>
            <th ek="fentity" lix="27" el="105" id="fdealamount_e" fn="fdealamount" pn="fdealamount" cn="成交金额" width="90" lock="-1" visible="1124" format="0,000.00" apipn="dealAmount"></th>
            <th ek="fentity" lix="29" el="107" id="fbrandid" fn="fbrandid" cn="品牌" visible="1150" ctlfk="fmaterialid" dispfk="fbrandid" sformid="" width="100" lock="-1"></th>
            <th ek="fentity" lix="31" el="161" id="fmtrlimage" fn="fmtrlimage" pn="fmtrlimage" cn="商品图片" ctlfk="fmaterialid" width="200" visible="1150"></th>
            <th lix="33" el="152" ek="fentity" id="fdoorderstatus" fn="fdoorderstatus" pn="fdoorderstatus" visible="1150" cn="成单状态" canchange="true" defval="'0'"
                lock="-1" copy="0" notrace="true" ts="" vals="'0':'未成单','1':'已成单'"></th>
            <th ek="fentity" lix="35" el="100" id="fnote" fn="fnote" cn="备注" visible="1124" width="160" len="1000" copy="0"></th>

            <th ek="fentity" lix="37" el="107" id="fisfixprop" fn="fisfixprop" pn="fisfixprop" visible="0" cn="固定组合"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fisfixprop" refvt="0"></th>

            <th ek="fentity" lix="39" el="107" id="fattribute" fn="fattribute" pn="fattribute" visible="0" cn="属性" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fattribute" refvt="0"></th>

            <th ek="fentity" lix="41" el="107" id="fseriesid" fn="fseriesid" cn="系列" visible="32" ctlfk="fmaterialid" dispfk="fseriesid" sformid="" width="100" lock="-1"></th>
            <!-- <th ek="fentity" lix="8" el="107" id="fcustom" fn="fcustom" pn="fcustom" visible="1150" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fcustom" refvt="116"></th>-->
            <th ek="fentity" lix="43" el="107" id="fcustom" fn="fcustom" pn="fcustom" visible="0" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fcustom" refvt="116"></th>
            <!-- <th ek="fentity" lix="8" el="107" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="1150" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fispresetprop" refvt="116"></th>-->
            <th ek="fentity" lix="45" el="107" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="0" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fispresetprop" refvt="116"></th>

            <th ek="fentity" lix="47" el="100" len="2000" id="fcustomdes_e" fn="fcustomdes_e" pn="fcustomdes_e" cn="定制说明" width="160" visible="32"></th>


            <th ek="fentity" lix="49" el="109" id="funitid" fn="funitid" pn="funitid" cn="基本单位" refid="ydj_unit" sformid="" ctlfk="fmaterialid" lock="-1" filter="fisbaseunit='1'" width="80" visible="32" must="1"></th>
            <th ek="fentity" lix="51" el="103" id="fqty" fn="fqty" must="1" cn="基本单位数量" ctlfk="funitid" format="0,000.00" width="100" visible="32"></th>

            <th el="116"  lix="53" ek="fentity" visible="32" id="funstdtype" fn="funstdtype" pn="funstdtype" cn="是否非标" width="90" copy="0"></th>

            <th ek="fentity" lix="55" el="116" id="fisoutspot" fn="fisoutspot" pn="fisoutspot" cn="出现货" visible="32"></th>
            <th lix="57" el="152" ek="fentity" id="fdeliverymode" fn="fdeliverymode" pn="fdeliverymode" visible="32" cn="提货方式" canchange="true"
                lock="0" copy="0" notrace="true" ts="" vals="'0':'物流配送','1':'立即提货','2':'预约提货'"></th>
            <th ek="fentity" lix="59" el="106" id="fstockstatus" fn="fstockstatus" pn="fstockstatus" visible="32" cn="库存状态" notrace="true" refid="ydj_stockstatus" dfld="fcolor" defVal="'311858936800219137'"></th>
            <th ek="fentity" lix="61" el="106" id="fstorehouseid" fn="fstorehouseid" cn="仓库" refid="ydj_storehouse" sformid="" visible="32" width="100" apipn="storehouse"></th>
            <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
            <th ek="fentity" lix="63" el="153" id="fstorelocationid" fn="fstorelocationid" cn="仓位" ctlfk="fstorehouseid" luek="fentity" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="32" width="100"></th>
            <th el="100" lix="65" ek="fentity" id="fmtono" fn="fmtono" pn="fmtono" cn="物流跟踪号" width="100" visible="32" lock="0" canchange="true"></th>
            <th el="149" lix="67" ek="fentity" id="fownertype" fn="fownertype" pn="fownertype" dataviewname="v_bd_ownerdata" cn="货主类型" width="100" visible="32" lock="0">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th el="150" lix="69" ek="fentity" id="fownerid" fn="fownerid" pn="fownerid" ctlfk="fownertype" cn="货主" width="100" visible="32" lock="0"></th>

            <th ek="fentity" el="100" id="fsourceentryid_e" fn="fsourceentryid" cn="源单明细ID" visible="0" lock="-1" copy="0"></th>
            <th ek="fentity" el="100" id="fsuitentryid_e" fn="fsuitentryid_e" cn="套件分录ID" visible="0" lock="-1" copy="0"></th>
            <th ek="fentity" el="100" id="fsynnote" fn="fsynnote" cn="协同备注" visible="0" len="1000" lock="-1" desc="用于记录协同过程中出现的错误信息或者系统备注信息" copy="0"></th>
            <th el="122" ek="fentity" visible="0" id="fspace" fn="fspace" pn="fspace" cn="空间" cg="空间" apipn="sapace" refid="bd_enum" dfld="fenumitem"></th>
            <th el="116" ek="fentity" visible="0" id="fisclearstock" fn="fisclearstock" pn="fisclearstock" cn="清库存" lock="-1"></th>

        </tr>
    </table>

    <!--商品子明细-->
    <table id="fdetail" el="53" pek="fentity" pk="fdetailid" tn="t_ydj_saledetail" pn="fdetail" cn="报价明细" desc="商品子明细">
        <tr>
            <th el="106" ek="fdetail" id="fproductid_sub" fn="fproductid" pn="fproductid" cn="商品" refid="ydj_product" multsel="true" sformid="" width="160" visible="1124" copy="0"></th>
            <th el="100" ek="fdetail" id="fproductname_sub" fn="fproductname" pn="fproductname" cn="物料名称" width="160" visible="1124" copy="0"></th>
            <th el="116" ek="fdetail" id="fisadjust_sub" fn="fisadjust" pn="fisadjust" cn="手工调整" width="90" visible="1124" copy="0"></th>

            <th el="109" ek="fdetail" id="funitid_sub" fn="funitid" pn="funitid" cn="基本单位" refid="ydj_unit" sformid="" ctlfk="fproductid_sub" lock="-1" filter="fisbaseunit='1'" width="80" visible="1124" copy="0"></th>
            <th el="103" ek="fdetail" id="fqty_sub" fn="fqty" pn="fqty" cn="基本单位数量" ctlfk="funitid_sub" format="0,000.00" lock="-1" width="100" visible="1124" copy="0"></th>
            <th el="109" ek="fdetail" id="fbizunitid_sub" fn="fbizunitid" pn="fbizunitid" cn="销售单位" refid="ydj_unit" sformid="" ctlfk="fproductid_sub" width="90" visible="1124" copy="0"></th>
            <th el="103" ek="fdetail" id="fbizqty_sub" fn="fbizqty" pn="fbizqty" cn="销售数量" ctlfk="fbizunitid_sub" format="0,000.00" basqtyfk="fqty_sub" width="80" visible="1124" copy="0"></th>

            <th el="132" ek="fdetail" id="fattrinfo_sub" fn="fattrinfo" pn="fattrinfo" cn="辅助属性" ctlfk="fproductid_sub" pricefk="fprice" width="140" visible="1124" copy="0"></th>
            <th el="133" ek="fdetail" id="flength_sub" fn="flength" pn="flength" cn="长" width="65" visible="1124" copy="0"></th>
            <th el="133" ek="fdetail" id="fwidth_sub" fn="fwidth" pn="fwidth" cn="宽" width="65" visible="1124" copy="0"></th>
            <th el="133" ek="fdetail" id="fthick_sub" fn="fthick" pn="fthick" cn="厚" width="65" visible="1124" copy="0"></th>
            <th el="104" ek="fdetail" id="fprice_sub" fn="fprice" pn="fprice" cn="零售价" width="80" visible="1124" format="0,000.000000" dformat="0,000.00" copy="0"></th>
            <th el="105" ek="fdetail" id="famount_sub" fn="famount" pn="famount" cn="金额" width="80" lock="-1" visible="1124" format="0,000.00" copy="0"></th>
            <th el="100" ek="fdetail" id="fdescription_sub" fn="fdescription" pn="fdescription" cn="备注" width="120" visible="1124" copy="0"></th>
        </tr>
    </table>

    <!--图纸明细-->
    <table id="fdrawentity" el="52" pk="fentryid" tn="t_ydj_saledrawentry" pn="fdrawentity" cn="图纸明细" kfks="ffilename,ffileid">
        <tr>
            <th ek="fdrawentity" el="100" id="ffilename" fn="ffilename" cn="文件名" visible="1124" width="350" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="110" id="ffileid" fn="ffileid" cn="文件id" visible="0" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="100" id="ffileformat" fn="ffileformat" pn="ffileformat" cn="文件格式" visible="1124" width="70" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="100" id="ffilesize" fn="ffilesize" pn="ffilesize" cn="文件大小" visible="1124" width="70" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="100" id="fnote_d" fn="fnote" cn="备注" visible="1124" width="260" copy="0"></th>
            <th ek="fdrawentity" el="100" id="fuploader" fn="fuploader" cn="上传人" visible="1124" width="65" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="100" id="fuploaderid" fn="fuploaderid" pn="fuploaderid" cn="上传人id" visible="0" width="100" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="113" id="fuptime" fn="fuptime" cn="上传时间" visible="1124" width="120" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="100" id="fsourceentryid" fn="fsourceentryid" cn="源单明细ID" visible="0" lock="-1" copy="0" width="120"></th>
            <th ek="fdrawentity" el="144" id="foplist" fn="foplist" cn="操作" visible="96" width="120" btnid="download,delete" btntxt="下载,删除" lock="-1" align="left"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="accase" op="accase" opn="受理" data="" permid="sal_accase"></ul>
        <ul el="10" ek="fbillhead" id="cancelaccase" op="cancelaccase" opn="取消受理" data="" permid="sal_cancelaccase"></ul>
        <ul el="10" ek="fbillhead" id="transmit" op="transmit" opn="报价确认" data="" permid="sal_transmit"></ul>
        <ul el="10" ek="fbillhead" id="canceltransmit" op="canceltransmit" opn="取消报价确认" data="" permid="sal_canceltransmit"></ul>
        <ul el="10" ek="fbillhead" id="receipt" op="receipt" opn="收款" data="" permid="sal_receipt"></ul>
        <ul el="10" ek="fbillhead" id="refund" op="refund" opn="退款" data="" permid="sal_refund"></ul>
        <ul el="10" ek="fbillhead" id="salecontract" op="salecontract" opn="成单" data="" permid="sal_salecontract"></ul>
        <ul el="10" id="followerrecord" op="followerrecord" opn="跟进" data="" permid="followerrecord"></ul>

        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="11" vid="511" cn="已经生成了销售合同后不可以反审核" data='{"expr": [{"linkFormId":"ydj_order", "sourceLinkFieldKey":"fbillno","linkFieldKey":"fsourcenumber"}],
                "message":"已经生成了下游单据，不允许反审核！"}'></li>
        </ul>
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <li el="11" vid="510" cn="业务状态-协同未完成(未收货)不允许审核。" data="{'expr':'(fpublishcompanyid==\'\' or fpublishcompanyid==\' \') or (fbizstatus==\'business_status_05\' or fbizstatus==\'business_status_09\' or  fbizstatus==\'business_status_10\')','message':'业务状态-协同未完成(未收货)不允许审核。'}"></li>
        </ul>

        <ul el="10" ek="fentity" id="queryinventory" op="queryinventory" opn="库存查询" permid="fw_queryinventory"
            data="{
                'parameter':{
                    'fieldMaps':{
                        'fcustomdesc':'fcustomdes_e',
                        'fusableqty':'fqty',
                    },
                    'filterString':'fmaterialid in(select fid from t_bd_material where fispulloff!=\'1\') and fmtono=\'\''
                }
            }"></ul>

        <ul el="10" id="save" op="save" opn="保存">
            <li el="11" vid="510" cn="应收定金 不允许 小于 已收定金。" data="{'expr':'SYS.GetSystemParam(\'bas_storesysparam\',\'fenablecollectamount\',False)==False or (fcollectamount>=fcollectedamount)','message':'应收定金 不允许 小于 已收定金'}"></li>
            <li el="11" vid="510" ek="fentity" cn="货主字段不能为空!" data="{'expr':'(fownertype=\'\' and fownerid=\'\') or (fownertype!=\'\' and fownerid!=\'\')','message':'货主字段不能为空!'}"></li>
            <li el="17" sid="1002" cn="反写销售机会的意向单" data="{
                'sourceFormId':'ydj_customerrecord',
                'sourceControlFieldKey':'fintentionid',
                'sourceLinkFieldKey':'fbillno',
                'linkIdFieldKey':'fsourcenumber',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'',
                'writebackFieldKey':'fintentionno',
                'expression':'fbillno',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
            <li el="17" sid="1002" cn="反写销售机会的最后跟进时间" data="{
                 'sourceFormId':'ydj_customerrecord',
                 'sourceControlFieldKey':'ffollowtime',
                 'sourceLinkFieldKey':'',
                 'linkIdFieldKey':'fsourceentryid',
                 'linkFormFieldKey':'',
                 'linkFilterString':'',
                 'writebackFieldKey':'ffollowtime',
                 'expression':'@currentDate',
                 'writebackMode':0,
                 'excessCondition':'',
                 'excessMessage':''
                 }"></li>
            <li el="11" vid="510" ek="fentity" cn="销售单位和基本单位一致时，要求销售数量和基本单位数量也要一致"
                data="{'expr':'fbizqty==fqty','message':'销售单位和基本单位一致时，要求销售数量和基本单位数量也要一致！'}"
                precon="fbizunitid==funitid"></li>

            <li el="11" vid="510" ek="fentity" cn="数量必须大于0" data="{'expr':'fbizqty>0 ','message':'数量必须大于0！'}"></li>
            <li el="11" vid="510" ek="fentity" cn="金额必须大于0" data="{'expr':'famount>=0 ','message':'金额不允许为负数！'}"></li>

            <li el="17" sid="2003" cn="更新预留明细" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes_e'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" id="delete" op="delete" opn="删除">
            <li el="17" sid="1002" cn="反写销售机会的意向单" data="{
                'sourceFormId':'ydj_customerrecord',
                'sourceControlFieldKey':'fintentionid',
                'sourceLinkFieldKey':'fbillno',
                'linkIdFieldKey':'fsourcenumber',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'',
                'writebackFieldKey':'fintentionno',
                'expression':'\'\'',
                'writebackMode':3,
                'excessCondition':'',
                'excessMessage':''
                }"></li>

            <li el="17" sid="2006" cn="删除对应的预留，同时预留转回到上游业务" data="{
                'preCondition':'',
                'qtyFieldKey':'fqty'
                }"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="manualrelease" op="manualrelease" opn="手动释放" permid="ydj_manualrelease">
            <li el="17" sid="2002" cn="手动释放预留数量" data="{'releaseWay':2}"></li>
        </ul>


        <ul el="10" ek="fbillhead" id="cancel" op="cancel" opn="作废" data="" permid="">
            <li el="17" sid="2002" cn="自动释放预留数量" data="{
                'message':'订单作废，自动释放预留数量',
                'preCondition':'',
                'releaseWay':5,
                'releaseType':0}"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="uncancel" op="uncancel" opn="反作废" data="" permid="">
            <li el="17" sid="2002" cn="取消释放预留数量" data="{
                'message':'订单反作废，自动取消释放预留数量',
                'preCondition':'',
                'releaseWay':5,
                'releaseType':1}"></li>
        </ul>


        <ul el="10" ek="fbillhead" id="reserveinventory" op="reserveinventory" opn="预留" data="" permid="fw_reserveinventory"></ul>



    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="sal_accase" cn="受理"></ul>
        <ul el="12" id="sal_cancelaccase" cn="取消受理"></ul>
        <ul el="12" id="sal_transmit" cn="报价确认"></ul>
        <ul el="12" id="sal_canceltransmit" cn="取消报价确认"></ul>
        <ul el="12" id="sal_receipt" cn="收款"></ul>
        <ul el="12" id="sal_refund" cn="退款"></ul>
        <ul el="12" id="sal_salecontract" cn="成单"></ul>
        <ul el="12" id="fw_queryinventory" cn="库存查询"></ul>
        <ul el="12" id="followerrecord" cn="跟进"></ul>
        <ul el="12" id="ydj_manualrelease" cn="手动释放"></ul>
        <ul el="12" id="fw_reserveinventory" cn="预留" order="20"></ul>
    </div>

</body>
</html>