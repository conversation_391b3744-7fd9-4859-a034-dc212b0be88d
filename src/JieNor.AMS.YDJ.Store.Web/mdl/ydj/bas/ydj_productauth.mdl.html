<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_productauth" basemodel="bd_basetmpl" el="3" cn="商品授权清单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_productauth" pn="fbillhead" cn="商品授权清单">

        <input id="fnumber" el="108" cn="编码" copy="0" lix="1" notrace="false"/>
        <input id="fname" el="100" len="1000" copy="0" lix="2" notrace="false"/>

        <input el="107" ek="fbillhead" visible="-1" id="forgparentid" fn="forgparentid" pn="forgparentid" cn="授予组织所属上级" ctlfk="forgid" dispfk="fparentid" lix="3" />
        <!--<input el="107" ek="fbillhead" visible="-1" id="forgtype" fn="forgtype" pn="forgtype" cn="授予组织类型" ctlfk="forgid" dispfk="forgtype" lix="4" />-->
        <select el="152" ek="fbillhead" visible="-1" id="forgtype" fn="forgtype" pn="forgtype" cn="授予组织类型"
                vals="'1':'总部','2':'分公司','3':'区域','4':'经销商','5':'门店'" defval="'总部'" lock="-1"></select>
        <input el="106" ek="fbillhead" visible="-1" id="forgid" fn="forgid" pn="forgid" cn="授予组织" dfld="fparentid,forgtype" refid="bas_organization" lix="5" len="1000" notrace="false"/>
        <input el="107" ek="fbillhead" visible="-1" id="forgnumber" fn="forgnumber" pn="forgnumber" cn="授予组织编码" ctlfk="forgid" dispfk="fnumber" lix="3" />
        <input el="106" ek="fbillhead" visible="-1" id="fcityid" fn="fcityid" pn="fcityid" cn="授予组织所属城市" refid="ydj_city" lix="6" notrace="false"/> 

        <!--送达方信息-->
        <!--<table id="fdeliverentry" el="52" pk="fdeliverentryid" tn="t_ydj_productauth_deliver" pn="fdeliverentry" cn="送达方信息" kfks="fdeliverid_o">
            <tr>
                <th el="106" ek="fdeliverentry" visible="1150" width="300" id="fdeliverid_o" fn="fdeliverid_o" pn="fdeliverid_o" cn="送达方" lock="-1" refid="bas_deliver" sformid="" dfld="fnumber" lix="11"></th>
                <th el="107" ek="fdeliverentry" visible="1150" id="fdelivernumber_o" fn="fdelivernumber_o" pn="fdelivernumber_o" cn="送达方编码" ctlfk="fdeliverid_o" dispfk="fnumber" lix="12" />
                <th el="106" ek="fdeliverentry" visible="1150" width="300" id="fsaleorgid_o" fn="fsaleorgid_o" pn="fsaleorgid_o" cn="销售组织" lock="-1" refid="bas_organization" sformid="" lix="13"></th>
                <th el="107" ek="fdeliverentry" visible="1150" id="fsaleorgnumber_o" fn="fsaleorgnumber_o" pn="fsaleorgnumber_o" cn="销售组织编码" ctlfk="fsaleorgid_o" dispfk="fnumber" lix="14" />
            </tr>
        </table>-->
        <!--授权品牌/系列-->
        <table id="fproductauthbs" el="52" pk="fentryid" tn="t_ydj_productauthbs" pn="fproductauthbs" cn="授权品牌/系列" kfks="fbrandid,fserieid">
            <tr>
                <th el="106" ek="fproductauthbs" visible="-1" id="fbrandid" fn="fbrandid" pn="fbrandid" refid="ydj_brand" dfld="fnumber" sformid="" cn="品牌名称" lix="8"></th>
                <th el="107" ek="fproductauthbs" visible="-1" id="fbrandnumber" pn="fbrandnumber" cn="品牌编码" ctlfk="fbrandid" dispfk="fnumber" lock="-1" lix="7"></th>

                <!--授权系列：不填表示品牌下的所有系列-->
                <th el="131" ek="fproductauthbs" visible="-1" id="fserieid" fn="fserieid" pn="fserieid" refid="ydj_series" dfld="fnumber,fbrandid" cn="系列" sformid="" TableName="t_ydj_productauthbs_ser" width="200" lix="8" notrace="false"></th>

                <!--例外商品：填了表示该商品是例外的，不授予权限-->
                <!--<th el="131" ek="fproductauthbs" visible="1150" id="fproductid_o" fn="fproductid_o" pn="fproductid_o" refid="ydj_product" dfld="fnumber" cn="例外商品" sformid="" TableName="t_ydj_productauthbs_prd" width="200" lix="8"></th>-->

            </tr>
        </table>

        <!--例外商品（不授予权限的商品）-->
        <table id="fproductauthexclude" el="52" pk="fentryid" tn="t_ydj_productauthbs_prd" pn="fproductauthexclude" cn="例外商品" kfks="fproductid_o">
            <tr>
                <th el="107" ek="fproductauthexclude" visible="-1" id="fproductnumber_o" fn="fproductnumber_o" pn="fproductnumber_o" cn="商品编码" ctlfk="fproductid_o" dispfk="fnumber" lock="-1" lix="10"></th>
                <th el="106" ek="fproductauthexclude" visible="-1" id="fproductid_o" fn="fproductid_o" pn="fproductid_o" cn="商品" refid="ydj_product" sformid="" dfld="fnumber,fbrandid,fseriesid,fcategoryid" lix="11"></th>
                <th el="107" ek="fproductauthexclude" visible="-1" id="fforbrandid_o" fn="fforbrandid_o" pn="fforbrandid_o" cn="所属品牌" ctlfk="fproductid_o" dispfk="fbrandid" lock="-1" lix="12"></th>
                <th el="107" ek="fproductauthexclude" visible="-1" id="fforseriesid_o" fn="fforseriesid_o" pn="fforseriesid_o" cn="所属系列" ctlfk="fproductid_o" dispfk="fseriesid" lock="-1" lix="13"></th>
                <th el="107" ek="fproductauthexclude" visible="-1" id="fcategoryid_o" fn="fcategoryid_o" pn="fcategoryid_o" cn="商品类别" ctlfk="fproductid_o" dispfk="fcategoryid" lock="-1" lix="14"></th>
            </tr>
        </table>

        <!--授权商品-->
        <table id="fproductauthlist" el="52" pk="fentryid" tn="t_ydj_productauthlist" pn="fproductauthlist" cn="授权商品" kfks="fproductid">
            <tr>
                <th el="107" ek="fproductauthlist" visible="-1" id="fproductnumber" fn="fproductnumber" pn="fproductnumber" cn="商品编码" ctlfk="fproductid" dispfk="fnumber" lock="-1" lix="10"></th>
                <th el="106" ek="fproductauthlist" visible="-1" id="fproductid" fn="fproductid" pn="fproductid" cn="商品" refid="ydj_product" sformid="" dfld="fnumber,fbrandid,fseriesid,fcategoryid" lix="11"></th>
                <th el="107" ek="fproductauthlist" visible="-1" id="fforbrandid" fn="fforbrandid" pn="fforbrandid" cn="所属品牌" ctlfk="fproductid" dispfk="fbrandid" lock="-1" lix="12"></th>
                <th el="107" ek="fproductauthlist" visible="-1" id="fforseriesid" fn="fforseriesid" pn="fforseriesid" cn="所属系列" ctlfk="fproductid" dispfk="fseriesid" lock="-1" lix="13"></th>
                <th el="107" ek="fproductauthlist" visible="-1" id="fcategoryid" fn="fcategoryid" pn="fcategoryid" cn="商品类别" ctlfk="fproductid" dispfk="fcategoryid" lock="-1" lix="14"></th>
                <th el="116" ek="fproductauthlist" visible="-1" id="fnopurchase" fn="fnopurchase" pn="fnopurchase" cn="不允许采购" lock="0" lix="15"></th>
            </tr>
        </table>


    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
            <li el="11" clear></li>
            <!--<li el="11" id="save_valid_fnumber" cn="保存时授予组织，城市唯一" vid="500" data="fmainorgid,forgid,fcityid,fforbidstatus" precon=""></li>-->
        </ul>
        <ul el="10" ek="fbillhead" id="unforbid" op="unforbid" opn="反禁用" data="" permid="">
            <li el="11" clear></li>
            <li el="11" id="unforbid_valid_fnumber" cn="反禁用时授予组织，城市唯一" vid="500" data="fmainorgid,forgid,fcityid" precon=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="clearexclude" op="clearexclude" opn="清空例外商品" data="" permid="fw_clearexclude"></ul>
        <ul el="10" ek="fbillhead" id="batchzgproduct" op="batchzgproduct" opn="批量调整专供商品" data="" permid=""></ul>
    </div>
    <div id="permList">
        <ul el="12" id="fw_clearexclude" cn="清空例外商品"></ul>
    </div>
</body>
</html>