<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="rpt_stocksynthesize" el="1" basemodel="" cn="库存综合查询表" aqkf="9" rac="true" isolate="1" bftfks="fmaterialid,fmtrlmodel,fattrinfo,fattrinfo_e,fstockstatus,fbrandid,fseriesid,fsubseriesid,fattribute,fstorehouseid,fstorelocationid">
    <div id="fbillhead" el="51" pk="fid" tn="V_STK_INVENTORYLIST" pn="fbillhead" cn="库存综合查询表" isview="true">


        <input lix="1" group="基本信息" el="106" ek="fbillhead" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" visible="-1" cn="商品" lock="-1" copy="0" notrace="true" ts="" refid="ydj_product" filter="" reflvt="0" dfld="fname,fspecifica,fvolume,fgrossload,fpacksize,fsize" defls="true" />
        <input lix="5" group="基本信息" el="107" ek="fbillhead" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="-1" cn="商品编码" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0" defls="true" />
        <input lix="10" group="基本信息" el="107" ek="fbillhead" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="-1" cn="规格型号" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fspecifica" refvt="0" defls="true" />
        <input group="基本信息" el="106" ek="fbillhead" id="fattrinfo_e" fn="fattrinfo_e" pn="fattrinfo_e" refid="bd_auxpropvalue_ext" visible="-1" cn="辅助属性" defls="true" />
        <input lix="15" group="基本信息" el="132" ek="fbillhead" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" visible="32" cn="辅助属性" lock="-1" copy="0" notrace="true" ts="" ctlfk="fmaterialid" pricefk="" defls="true" />
        <input lix="20" group="基本信息" el="107" ek="fbillhead" visible="-1" id="fmtrlsize" fn="fmtrlsize" pn="fmtrlsize" cn="尺寸" ctlfk="fmaterialid" dispfk="fsize" width="140" defls="true" />
        <input lix="25" group="基本信息" el="107" ek="fbillhead" id="fbrandid" fn="fbrandid" pn="fbrandid" visible="-1" cn="品牌" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fbrandid" refvt="0" defls="true" />
        <input lix="30" group="基本信息" el="107" ek="fbillhead" id="fseriesid" fn="fseriesid" pn="fseriesid" visible="-1" cn="系列" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fseriesid" refvt="0" defls="true" />
        <input lix="31" group="基本信息" el="107" ek="fbillhead" id="fsubseriesid" fn="fsubseriesid" cn="子系列" ctlfk="fmaterialid" dispfk="fsubseriesid" visible="1086" sformid="" width="100" refvt="0" lock="-1" defls="true" />
        <input lix="800" group="基本信息" el="107" ek="fbillhead" id="fcategoryid" fn="fcategoryid" pn="fcategoryid" visible="1086" cn="商品类别" lock="-1"
               ctlfk="fmaterialid" dispfk="fcategoryid" defls="true" />

        <input lix="35" group="基本信息" el="100" ek="fbillhead" len="2000" id="fcustomdesc" fn="fcustomdesc" pn="fcustomdesc" visible="-1" cn="定制说明" lock="-1" copy="0" notrace="true" ts="" defls="true" />
        <input lix="40" group="基本信息" el="161" ek="FBillHead" id="fmtrlimage" fn="fmtrlimage" pn="fmtrlimage" visible="0" cn="图片" lock="-1" copy="0" notrace="true" ts="" ctlfk="fmaterialid" defls="true" />
        <input lix="45" group="基本信息" el="106" ek="fbillhead" id="fstorehouseid" fn="fstorehouseid" pn="fstorehouseid" visible="-1" cn="仓库" lock="-1" copy="0" notrace="true" ts="" refid="ydj_storehouse" filter="" reflvt="0" dfld="" defls="true" />
        <input lix="50" group="基本信息" el="153" ek="fbillhead" id="fstorelocationid" fn="fstorelocationid" pn="fstorelocationid" visible="-1" cn="仓位" lock="-1" copy="0" notrace="true" ts="" ctlfk="fstorehouseid" luek="fentity" lunbfk="flocnumber" lunmfk="flocname" defls="true" />
        <input lix="55" group="基本信息" el="106" ek="fbillhead" id="fstockstatus" fn="fstockstatus" pn="fstockstatus" visible="-1" cn="库存状态" lock="-1" copy="0" notrace="true" ts="" refid="ydj_stockstatus" defVal="'311858936800219137'" filter="" reflvt="0" dfld="" defls="true" />
        <input lix="60" group="基本信息" el="100" ek="fbillhead" id="flotno" fn="flotno" pn="flotno" visible="-1" cn="批号" lock="-1" copy="0" notrace="true" ts="" defls="true" />
        <div lix="61" group="基本信息" el="149" ek="fbillhead" id="fownertype" fn="fownertype" pn="fownertype" visible="-1" cn="货主类型" lock="-1" copy="0" notrace="true" ts="" dataviewname="v_bd_ownerdata">
            <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
            <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
            <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
        </div>
        <input lix="65" group="基本信息" el="150" ek="fbillhead" id="fownerid" fn="fownerid" pn="fownerid" visible="-1" cn="货主" lock="-1" copy="0" notrace="true" ts="" ctlfk="fownertype" filter="" />
        <input lix="70" group="基本信息" el="109" ek="fbillhead" id="funitid" fn="funitid" pn="funitid" visible="-1" cn="基本单位" width="80" lock="-1" copy="0" notrace="true" ts="" ctlfk="fmaterialid" refid="ydj_unit" filter="fisbaseunit='1'" reflvt="0" dfld="" defls="true" />
        <input lix="75" group="基本信息" el="109" ek="fbillhead" id="fstockunitid" fn="fstockunitid" pn="fstockunitid" visible="-1" cn="库存单位" lock="-1" copy="0" notrace="true" ts="" ctlfk="fmaterialid" refid="ydj_unit" filter="" reflvt="0" dfld="" defls="true" />
        <!-- <input lix="80" group="基本信息" el="103" ek="fbillhead" id="fqty" fn="fqty" pn="fqty" visible="-1" cn="基本单位数量" width="110" lock="-1" copy="0" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" />
    <input lix="85" group="基本信息" el="103" ek="fbillhead" id="fstockqty" fn="fstockqty" pn="fstockqty" visible="-1" cn="库存单位数量" lock="-1" copy="0" notrace="true" ts="" ctlfk="fstockunitid" basqtyfk="fqty" roundType="0" format="0,000.00" /> -->


        <input el="103" ek="fbillhead" visible="-1" id="fstockreserveqty" fn="fstockreserveqty" pn="fstockreserveqty" cn="预留量"
               ctlfk="fstockunitid" basqtyfk="freserveqty" width="75" format="0,000.00" css="col-clickable" lix="81" defls="true" />
        <input el="103" ek="fbillhead" visible="-1" id="fstockusableqty" fn="fstockusableqty" pn="fstockusableqty" cn="可用量"
               ctlfk="fstockunitid" basqtyfk="fusableqty" width="75" format="0,000.00" lix="90" defls="true" />
        <input el="103" ek="fbillhead" visible="-1" id="fstockqty" fn="fstockqty" pn="fstockqty" cn="库存量"
               ctlfk="fstockunitid" basqtyfk="fqty" width="75" format="0,000.00" lix="100" defls="true" />
        <input el="103" ek="fbillhead" visible="-1" id="fstockintransitqty" fn="fstockintransitqty" pn="fstockintransitqty" cn="采购在途量"
               ctlfk="fstockunitid" basqtyfk="fintransitqty" width="85" format="0,000.00" lix="110" defls="true" />
        <input el="103" ek="fbillhead" visible="-1" id="fstockoutingqty" pn="fstockoutingqty" cn="待出库数" xlsout="0"
               ctlfk="fstockunitid" basqtyfk="foutingqty" width="85" format="0,000.00" lix="120" defls="true" />
        <input el="103" ek="fbillhead" visible="-1" id="findbqty" fn="findbqty" pn="findbqty" cn="分步式调拨在途量" xlsout="0"
               ctlfk="fstockunitid" basqtyfk="fbizindbqty" width="85" format="0,000.00" lix="120" defls="true" />



        <input el="103" ek="fbillhead" visible="-1" id="freserveqty" fn="freserveqty" pn="freserveqty" cn="基本单位预留量"
               ctlfk="funitid" width="110" format="0,000.00" lix="370" defls="true" />
        <input el="103" ek="fbillhead" visible="-1" id="fusableqty" fn="fusableqty" pn="fusableqty" cn="基本单位可用量"
               ctlfk="funitid" width="110" format="0,000.00" lix="380" defls="true" />
        <input el="103" ek="fbillhead" visible="-1" id="fqty" fn="fqty" pn="fqty" cn="基本单位库存量"
               ctlfk="funitid" width="110" format="0,000.00" lix="390" defls="true" />
        <input el="103" ek="fbillhead" visible="-1" id="fintransitqty" fn="fintransitqty" pn="fintransitqty" cn="基本单位采购在途量" xlsout="0"
               ctlfk="funitid" width="135" format="0,000.00" lix="400" defls="true" />
        <input el="103" ek="fbillhead" visible="-1" id="foutingqty" pn="foutingqty" cn="基本单位待出库数" xlsout="0"
               ctlfk="funitid" width="135" format="0,000.00" lix="410" defls="true" />
        <input el="103" ek="fbillhead" visible="-1" id="fbizindbqty" fn="fbizindbqty" pn="fbizindbqty" cn="基本单位分步式调拨在途量" xlsout="0"
               ctlfk="funitid" width="135" format="0,000.00" lix="410" defls="true" />



        <input group="基本信息" el="107" ek="fbillhead" id="fattribute" fn="fattribute" pn="fattribute" visible="0" cn="属性" lock="-1" copy="1" lix="11" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fattribute" refvt="0" defls="true" />

        <input group="基本信息" el="100" ek="fbillhead" id="fmtono" fn="fmtono" pn="fmtono" visible="1150" cn="物流跟踪号" width="140" lock="-1" copy="0" lix="90" notrace="true" ts="" defls="true" />

        <input group="基本信息" el="105" ek="fbillhead" id="famount" fn="famount" pn="famount" visible="0" cn="金额" lock="-1" copy="0" lix="130" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />

        <input group="基本信息" id="fmainorgid" el="148" ek="fbillhead" fn="fmainorgid" pn="fmainorgid" cn="企业主体" xlsin="0" visible="0" copy="0" defls="true" />
        <input group="基本信息" id="ftranid" el="100" ek="FBillHead" fn="ftranid" ts="" cn="交易流水号" visible="0" xlsin="0" copy="0" />

        <!--单位成本，总成本-->
        <input group="基本信息" el="105" ek="fbillhead" id="fcostprice" fn="fcostprice" pn="fcostprice" visible="1086" cn="单位成本(加权平均)" lock="-1" copy="0" lix="130" xlsin="0" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />
        <input group="基本信息" el="105" ek="fbillhead" id="fcostamt" fn="fcostamt" pn="fcostamt" visible="1086" cn="总成本(加权平均)" lock="-1" copy="0" lix="130" notrace="true" xlsin="0" ts="" roundType="0" format="0,000.00" defls="true" />

        <!-- 引用《商品》的【总净重】和【总体积】 -->
        <input lix="800" group="基本信息" el="107" ek="fbillhead" id="fsuttle" fn="fsuttle" pn="fsuttle" visible="1150" cn="总净重(kg)" lock="-1"
               ctlfk="fmaterialid" dispfk="fsuttle" format="0,000.000" sbm="true" defls="true" />
        <input lix="800" group="基本信息" el="107" ek="fbillhead" id="fvolume" fn="fvolume" pn="fvolume" visible="0" cn="总体积(m³)" lock="-1"
               ctlfk="fmaterialid" dispfk="fvolume" format="0,000.000" sbm="true" defls="true" />

        <!--计算规则取综合价目表（金额=单价*库存量）-->
        <input lix="500" el="105" ek="fbillhead" id="fpurfacprice" fn="fpurfacprice" pn="fpurfacprice" visible="1086" cn="采购单价（折前）" lock="-1" copy="0" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />
        <input lix="502" el="105" ek="fbillhead" id="fpurfacamount" fn="fpurfacamount" pn="fpurfacamount" visible="1086" cn="采购折前金额" lock="-1" copy="0" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />
        <input lix="505" el="105" ek="fbillhead" id="fpurdealprice" fn="fpurdealprice" pn="fpurdealprice" visible="1086" cn="采购单价（折后）" lock="-1" copy="0" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />
        <input lix="507" el="105" ek="fbillhead" id="fpurdealamount" fn="fpurdealamount" pn="fpurdealamount" visible="1086" cn="采购折后金额" lock="-1" copy="0" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />
        <input lix="510" el="105" ek="fbillhead" id="funifysaleprice" fn="funifysaleprice" pn="funifysaleprice" visible="1086" cn="统一零售价" lock="-1" copy="0" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />
        <input lix="512" el="105" ek="fbillhead" id="funifysaleamount" fn="funifysaleamount" pn="funifysaleamount" visible="1086" cn="统一零售金额" lock="-1" copy="0" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />
        <input lix="515" el="105" ek="fbillhead" id="fsellprice" fn="fsellprice" pn="fsellprice" visible="1086" cn="经销价（折前）" lock="-1" copy="0" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />
        <input lix="517" el="105" ek="fbillhead" id="fsellamount" fn="fsellamount" pn="fsellamount" visible="1086" cn="经销金额" lock="-1" copy="0" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />
        <input lix="520" el="105" ek="fbillhead" id="freprice" fn="freprice" pn="freprice" visible="0" cn="分销价（折前）" lock="-1" copy="0" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />
        <input lix="522" el="105" ek="fbillhead" id="freamount" fn="freamount" pn="freamount" visible="1086" cn="分销金额" lock="-1" copy="0" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />
        <input lix="525" el="105" ek="fbillhead" id="fterprice" fn="fterprice" pn="fterprice" visible="0" cn="终端零售价（折前）" lock="-1" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />
        <input lix="527" el="105" ek="fbillhead" id="fteramount" fn="fteramount" pn="fteramount" visible="1086" cn="终端零售金额" lock="-1" notrace="true" ts="" roundType="0" format="0,000.00" defls="true" />

        <input group="基本信息" id="fseltypeid" el="107" ek="fbillhead" fn="fseltypeid" ctlfk="fmaterialid" dispfk="fseltypeid" ts="" cn="型号" visible="1086" lix="530" />

        <input lix="529" el="100" ek="fbillhead" id="fvolumeunit" fn="fvolumeunit" pn="fvolumeunit" visible="1086" cn="体积单位" lock="-1" must="0" uaul="true" />
        <input lix="530" el="102" ek="fbillhead" id="ftotalvolume" fn="ftotalvolume" pn="ftotalvolume" visible="1086" cn="总体积" lock="-1" copy="0" ts="" format="0,000.000" width="150" uaul="true" />
        <input lix="531" el="102" ek="fbillhead" id="fsinglevolume" fn="fsinglevolume" pn="fsinglevolume" visible="1086" cn="单位体积" lock="-1" copy="0" ts="" format="0,000.000" width="150" uaul="true" />
        <input group="基本信息" el="152" ek="fbillhead" visible="-1" id="fstockwaterline" fn="fstockwaterline" pn="fstockwaterline" cn="库存水位线状态"
               vals="'0':'','1':'红灯','2':'绿灯','3':'黄灯'" lix="600" align="center" />
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList"> 
       <ul el="10" ek="fbillhead" id="query" op="query" opn="查看" data="" permid="fw_view"></ul>
       <ul el="10" ek="fbillhead" id="viewrpt" op="viewrpt" opn="查看" data="" permid="fw_view"></ul>
       <ul el="10" ek="fbillhead" id="print" op="print" opn="打印" data="" permid="fw_print"></ul>
       <ul el="10" ek="fbillhead" id="listdatatopdf" op="listdatatopdf" opn="导出PDF" data="" permid="fw_export"></ul>
       <ul el="10" ek="fbillhead" id="listdatatoexcel" op="listdatatoexcel" opn="导出Excel" data="" permid="fw_export"></ul>
       <ul el="10" ek="fbillhead" id="producttagprintseting" op="producttagprintseting" opn="标签打印" data="{'parameter':{'ruleParam':{'activeEntityKey':'fbillhead','fieldMappings':[{'id':'fmaterialid','name':'商品','mapType':0,'srcFieldId':'fmaterialid'},{'id':'fattrinfo','name':'辅助属性','mapType':0,'srcFieldId':'fattrinfo'},{'id':'fqty','name':'打印数量','mapType':0,'srcFieldId':'fstockqty'}]}}}"></ul>
       <ul el="10" ek="fbillhead" id="updateextenddata" op="updateextenddata" opn="更新扩展数据" data="" permid="fw_updateextenddata"></ul>
       <ul el="10" ek="fbillhead" id="getoutingqty" op="getoutingqty" opn="获取待出库数量" data="" permid="fw_getoutingqty"></ul>
       <ul el="10" ek="fbillhead" id="getindbqty" op="getindbqty" opn="获取分步式调拨在途量" data="" permid="fw_getindbqty"></ul>
       <ul el="10" ek="fbillhead" id="updatevolume" op="updatevolume" opn="手动更新体积" data="" permid="fw_updatevolume"></ul>
   </div>

   <!--表单所涉及的权限项定义-->
   <div id="permList"> 
       <ul el="12" id="fw_view" cn="查看"></ul>
       <ul el="12" id="fw_print" cn="打印"></ul>
       <ul el="12" id="fw_export" cn="导出"></ul>
       <ul el="12" id="fw_presetfilteredit" cn="预置过滤方案编辑" order="19"></ul>
       <ul el="12" id="fw_updateextenddata" cn="更新扩展数据"></ul>
       <ul el="12" id="fw_getoutingqty" cn="获取待出库数量"></ul>
       <ul el="12" id="fw_getindbqty" cn="获取分步式调拨在途量"></ul>
       <ul el="12" id="fw_updatevolume" cn="手动更新体积"></ul>
   </div>
</body>
</html>