<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="rpt_stockindetail" el="8" basemodel="rpt_basetmpl" cn="商品入库详情" rac="false">
    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="商品入库详情">

        <input lix="10" group="基本信息" el="100" ek="fbillhead" id="flinkbillno" fn="flinkbillno" pn="flinkbillno" visible="-1" cn="入库单据编号" lock="-1" copy="0" notrace="true" ts="" css="col-clickable" width="200" defls="true" />
        <input lix="20" group="基本信息" el="106" ek="fbillhead" id="flinkformid" fn="flinkformid" pn="flinkformid" visible="-1" cn="入库类型" lock="-1" copy="0" notrace="true" ts="" refid="sys_bizobject" filter="" reflvt="0" dfld="" width="150" defls="true" />
        <input lix="30" group="基本信息" el="100" ek="fbillhead" id="flinkbillinterid" fn="flinkbillinterid" pn="flinkbillinterid" visible="0" cn="入库单据内码" lock="-1" copy="0" notrace="true" ts="" defls="true" />

        <input lix="40" group="基本信息" el="100" ek="fbillhead" id="fpoorderinterid" fn="fpoorderinterid" pn="fpoorderinterid" visible="0" cn="采购订单内码" lock="-1" copy="0" notrace="true" ts="" defls="true" />
        <input lix="50" group="基本信息" el="100" ek="fbillhead" id="fpoorderno" fn="fpoorderno" pn="fpoorderno" visible="-1" cn="采购订单编号" lock="-1" copy="0" notrace="true" ts="" width="200" defls="true" />
        <input lix="60" group="基本信息" el="100" ek="fbillhead" id="fsoorderinterid" fn="fsoorderinterid" pn="fsoorderinterid" visible="0" cn="销售合同内码" lock="-1" copy="0" notrace="true" ts="" />
        <input lix="65" group="基本信息" el="100" ek="fbillhead" id="fsoorderentryid" fn="fsoorderentryid" pn="fsoorderentryid" visible="0" cn="销售合同分录内码" lock="-1" copy="0" notrace="true" ts="" />
        <input lix="70" group="基本信息" el="100" ek="fbillhead" id="fsoorderno" fn="fsoorderno" pn="fsoorderno" visible="-1" cn="销售合同编号（未出库）" lock="-1" copy="0" notrace="true" ts="" width="200" />

        <input lix="80" el="103" ek="fbillhead" visible="-1" id="fstockqty" fn="fstockqty" pn="fstockqty" cn="入库数量"
               ctlfk="fstockunitid" width="100" format="0,000.00" />
        <input lix="90" el="103" ek="fbillhead" visible="0" id="fqty" fn="fqty" pn="fqty" cn="基本单位入库数量"
               ctlfk="funitid" width="100" format="0,000.00" />

        <input lix="85" el="103" ek="fbillhead" visible="-1" id="fstockusableqty" fn="fstockusableqty" pn="fstockusableqty" cn="剩余可用数量"
               ctlfk="fstockunitid" width="100" format="0,000.00" />
        <input lix="95" el="103" ek="fbillhead" visible="0" id="fusableqty" fn="fusableqty" pn="fusableqty" cn="基本单位剩余可用数量"
               ctlfk="funitid" width="100" format="0,000.00" />

        <input lix="100" type="text" id="foperatorid" el="106" ek="fbillhead" refId="sec_user" dfld="fname" fn="foperatorid" pn="foperatorid" cn="入库操作人" visible="-1" xlsin="0" copy="0" width="100" />
        <input lix="110" type="datetime" id="foperatedate" el="113" ek="fbillhead" fn="foperatedate" pn="foperatedate" cn="入库操作时间" visible="-1" xlsin="0" copy="0" width="150" />

        <input lix="120" group="基本信息" el="107" ek="fbillhead" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="0" cn="商品编码" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0" />
        <input lix="130" group="基本信息" el="106" ek="fbillhead" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" visible="0" cn="商品" lock="-1" copy="0" notrace="true" ts="" refid="ydj_product" filter="" reflvt="0" dfld="fname,fspecifica,fvolume,fgrossload,fpacksize,fsize" />
        <!--<input lix="140" group="基本信息" el="107" ek="fbillhead" id="fcategoryid" fn="fcategoryid" pn="fcategoryid" visible="0" cn="商品类别" lock="-1" ctlfk="fmaterialid" dispfk="fcategoryid" />-->
        <!--<input lix="150" group="基本信息" el="107" ek="fbillhead" id="fbrandid" fn="fbrandid" pn="fbrandid" visible="0" cn="品牌" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fbrandid" refvt="0" />-->
        <!--<input lix="160" group="基本信息" el="107" ek="fbillhead" id="fseriesid" fn="fseriesid" pn="fseriesid" visible="0" cn="系列" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fseriesid" refvt="0" />-->
        <input lix="170" group="基本信息" el="132" ek="fbillhead" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" visible="0" cn="辅助属性" lock="-1" copy="0" notrace="true" ts="" ctlfk="fmaterialid" pricefk="" />
        <input group="基本信息" el="106" ek="fbillhead" id="fattrinfo_e" fn="fattrinfo_e" pn="fattrinfo_e" refid="bd_auxpropvalue_ext" visible="-1" cn="辅助属性"
               lock="-1" copy="0" lix="30" notrace="true" ts="" />
        <input lix="180" group="基本信息" el="100" ek="fbillhead" len="2000" id="fcustomdesc" fn="fcustomdesc" pn="fcustomdesc" visible="0" cn="定制说明" lock="-1" copy="0" notrace="true" ts="" />

        <!--<input lix="190" group="基本信息" el="107" ek="fbillhead" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="-1" cn="规格型号" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fspecifica" refvt="0" />-->

        <input lix="200" group="基本信息" el="106" ek="fbillhead" id="fstorehouseid" fn="fstorehouseid" pn="fstorehouseid" visible="0" cn="仓库" lock="-1" copy="0" notrace="true" ts="" refid="ydj_storehouse" reflvt="0" dfld="" />

        <input lix="210" group="基本信息" el="153" ek="fbillhead" id="fstorelocationid" fn="fstorelocationid" pn="fstorelocationid" visible="0" cn="仓位" lock="-1" copy="0" notrace="true" ts="" ctlfk="fstorehouseid" luek="fentity" lunbfk="flocnumber" lunmfk="flocname" />

        <input lix="220" group="基本信息" el="106" ek="fbillhead" id="fstockstatus" fn="fstockstatus" pn="fstockstatus" visible="0" cn="库存状态" lock="-1" copy="0" notrace="true" ts="" refid="ydj_stockstatus" defVal="'311858936800219137'" filter="" reflvt="0" dfld="" />

        <input lix="230" group="基本信息" el="109" ek="fbillhead" id="funitid" fn="funitid" pn="funitid" visible="0" cn="基本单位" width="80" lock="-1" copy="0" notrace="true" ts="" ctlfk="fmaterialid" refid="ydj_unit" reflvt="0" dfld="" />
        <input lix="240" group="基本信息" el="109" ek="fbillhead" id="fstockunitid" fn="fstockunitid" pn="fstockunitid" visible="0" cn="库存单位" width="80" lock="-1" copy="0" notrace="true" ts="" ctlfk="fmaterialid" refid="ydj_unit" reflvt="0" dfld="" />

        <!--<input lix="250" group="基本信息" el="107" ek="fbillhead" id="fattribute" fn="fattribute" pn="fattribute" visible="0" cn="属性" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fattribute" refvt="0" />-->
        <!--<input lix="260" group="基本信息" el="100" ek="fbillhead" id="fmtono" fn="fmtono" pn="fmtono" visible="0" cn="物流跟踪号" width="140" lock="-1" copy="0" notrace="true" ts="" />-->
        <!-- 引用《商品》的【总净重】和【总体积】 -->
        <!--<input lix="270" group="基本信息" el="107" ek="fbillhead" id="fsuttle" fn="fsuttle" pn="fsuttle" visible="0" cn="总净重(kg)" lock="-1" ctlfk="fmaterialid" dispfk="fsuttle" format="0,000.000" sbm="true" />-->
        <!--<input lix="280" group="基本信息" el="107" ek="fbillhead" id="fvolume" fn="fvolume" pn="fvolume" visible="0" cn="总体积(m³)" lock="-1" ctlfk="fmaterialid" dispfk="fvolume" format="0,000.000" sbm="true" />-->

    </div>


    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="query" op="query" opn="查看" data="" permid="fw_view"></ul>
        <ul el="10" ek="fbillhead" id="viewrpt" op="viewrpt" opn="查看" data="" permid="fw_view"></ul>
        <ul el="10" ek="fbillhead" id="print" op="print" opn="打印" data="" permid="fw_print"></ul>
        <ul el="10" ek="fbillhead" id="listdatatopdf" op="listdatatopdf" opn="导出PDF" data="" permid="fw_export"></ul>
        <ul el="10" ek="fbillhead" id="listdatatoexcel" op="listdatatoexcel" opn="导出Excel" data="" permid="fw_export"></ul>
    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="fw_view" cn="查看"></ul>
        <ul el="12" id="fw_print" cn="打印"></ul>
        <ul el="12" id="fw_export" cn="导出"></ul>
        <ul el="12" id="fw_presetfilteredit" cn="预置过滤方案编辑" order="19"></ul>
    </div>

    <div id="ListFuzzyFlds" cn="默认支持快捷过滤的字段列表">
        <ul el="14" id="fw_fuzzyfld" fldkeys="flinkbillno,fpoorderno,fsoorderno" cn="默认支持快捷过滤的字段列表，多个用逗号或分号隔开"></ul>
    </div>
</body>
</html>