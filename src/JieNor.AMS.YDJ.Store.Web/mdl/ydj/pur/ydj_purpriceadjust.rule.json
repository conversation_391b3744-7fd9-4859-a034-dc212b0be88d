{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //此项规则表示：单据状态=D或E时，保存操作不可用, 其他操作可用
    {
      "id": "fstatus_DE",
      "expression": "menu:tbSave|fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "fstatus_Audit",
      "expression": "menu:tbAudit|fstatus=='E'"
    },
    {
      "id": "fstatus_Submit",
      "expression": "menu:tbSubmit|fstatus=='D' or fstatus=='E'"
    },
    // 审核状态 = 已提交/已审核DE 时，所有字段不可编辑
    {
      "id": "fstatus_None",
      "expression": "field:*|fstatus=='D' or fstatus=='E'"
    },
    //锁定表体辅助属性字段
    {
      "id": "lock_fattrinfo",
      "expression": "field:fattrinfo| id!='' or id == ''"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [
    //显示表头生效日期和失效日期隐藏
    {
      "id": "hide_date",
      "expression": "other:.y-date|fadjustmode == 'price_adjust_01'"
    },
    {
      "id": "show_date",
      "expression": "other:$.y-date|fadjustmode == 'price_adjust_02'"
    }
  ],
  //定义表单计算规则
  "calcRules": [
    //选择导购员信息，自动带出门店
    { "expression": "fdeptid=fstaffid__fdeptid|fstaffid=='' or 1==1" }

  ]
}