{
  "base": "/mdl/ydj/ste/base_order.menu.json",
  "common": [
    {
      "id": "tbSubmitHQ",
      "caption": "提交总部",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 108,
      "parent": "",
      "opcode": "submithq",
      "param": "'dataChangeWarn':true",
      "group": "standard"
    },
    {
      "id": "tbReNewSubmitHQ",
      "caption": "焕新提交总部",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 108,
      "parent": "",
      "opcode": "renewsubmithq",
      "param": "'dataChangeWarn':true",
      "group": "standard"
    },
    {
      "id": "tbSubmitAgent",
      "caption": "提交一级经销",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 109,
      "parent": "",
      "opcode": "submitagent",
      "param": "",
      "group": "standard"
    }
  ],
  "listmenu": [
    {
      "id": "tbReceiptTask1",
      "caption": "生成收货任务",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 107,
      "parent": "",
      "opcode": "createrecscantask",
      "param": ""
    },
    {
      "id": "tbStockUp",
      "caption": "定制柜备货",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 110,
      "parent": "",
      "opcode": "stockup",
      "param": "",
      "group": "standard",
      "default": "true"
    },
    {
      "id": "tbSwingField",
      "caption": "定制柜摆场",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 111,
      "parent": "",
      "opcode": "swingfield",
      "param": "",
      "group": "standard",
      "default": "true"
    }
  ],
  "billmenu": [
    {
      "id": "tbPayment",
      "caption": "付款",
      "visible": "true",
      "disabled": "false",
      "style": "menugroup",
      "order": 105,
      "parent": "",
      "opcode": "payment",
      "param": ""
    },
    {
      "id": "tbRefund",
      "caption": "退款",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 5,
      "parent": "tbPayment",
      "opcode": "Refund"
    },
    {
      "id": "tbIncomeDisburse",
      "caption": "收支明细",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 107,
      "parent": "tbPayment",
      "opcode": "IncomeDisburse"
    },
    {
      "id": "tbInitiateChangeApply",
      "caption": "发起变更申请",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 109,
      "parent": "",
      "opcode": "initiatechangeapply",
      "param": "",
      "group": "standard"
    },
    {
      "id": "tbBarcode",
      "caption": "条码",
      "visible": "true",
      "disabled": "true",
      "style": "menugroup",
      "order": 120,
      "parent": "",
      "opcode": "",
      "param": ""
    },
    {
      "id": "tbAddCode",
      "caption": "打码",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 121,
      "parent": "tbBarcode",
      "opcode": "addcode"
    },
    {
      "id": "tbReceiptTask",
      "caption": "生成收货任务",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 122,
      "parent": "tbBarcode",
      "opcode": "push2recscantask",
      "param": ""
    },
    {
      "id": "tbStockIn",
      "caption": "入库",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 135,
      "parent": "",
      "opcode": "push2poinstock",
      "param": ""
    },
    {
      "id": "tbReturnGood",
      "caption": "退货",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 140,
      "parent": "",
      "opcode": "push2poreturn",
      "param": "'dialog':{'formId':'pur_poreturndialog'}"
    },
    //{
    //  "id": "tbReceiptNotice",
    //  "caption": "收货",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 130,
    //  "parent": "",
    //  "opcode": "push2receiptnotice",
    //  "param": ""
    //},
    {
      "id": "tbpricefirm",
      "caption": "确认报价",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 108,
      "parent": "",
      "opcode": "pricefirm",
      "param": "",
      "group": "standard"
    },
    {
      "id": "tbcancelconfirm",
      "caption": "取消确认报价",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 108,
      "parent": "",
      "opcode": "cancelconfirm",
      "param": "",
      "group": "standard"
    },
    //更多菜单组
    {
      "id": "tbQueryChange",
      "caption": "变更记录",
      "visible": "true",
      "disabled": "false",
      "style": "menugroup",
      "order": 93,
      "parent": "droplist_more",
      "opcode": "querychange",
      "param": ""
    },
    {
      "id": "tbQueryChangeApply",
      "caption": "变更申请记录",
      "visible": "true",
      "disabled": "false",
      "style": "menugroup",
      "order": 98,
      "parent": "droplist_more",
      "opcode": "querychangeapply",
      "param": ""
    },
    //表体菜单
    {
      "id": "btnShowSelection",
      "caption": "产品选配",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 5,
      "parent": "",
      "opcode": "showselection",
      "param": "listMode:'lookup'",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "tbBizClose",
      "caption": "关闭",
      "visible": "true",
      "disabled": "false",
      "style": "menugroup",
      "order": 42,
      "parent": "droplist_more",
      "opcode": "bizclose",
      "group": "standard",
      "param": "'dataChangeWarn':true",
      "entityKey": "fentity"
    },
    {
      "id": "tbUnClose",
      "caption": "反关闭",
      "visible": "true",
      "disabled": "false",
      "style": "menugroup",
      "order": 43,
      "parent": "droplist_more",
      "opcode": "unclose",
      "group": "standard",
      "param": "'dataChangeWarn':true",
      "entityKey": "fentity"
    },
    {
      "id": "btnStandardCustom",
      "caption": "标准定制",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 100,
      "parent": "",
      "opcode": "showstandardcustom",
      "param": "listMode:'lookup'",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnUnStandardCustom",
      "caption": "非标准定制",
      "order": 105,
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "parent": "",
      "opcode": "showunstandardcustom",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnSuitCustom",
      "caption": "套件选配",
      "order": 110,
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "parent": "",
      "opcode": "showsuitcustom",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "tbUnstdTypeAudit",
      "caption": "非标审批",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 115,
      "parent": "",
      "opcode": "unstdtypeaudit",
      "param": "'dataChangeWarn':true",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnQYG",
      "caption": "添加气压杆",
      "order": 125,
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "parent": "",
      "opcode": "addqyg",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnZCG",
      "caption": "添加支撑杆",
      "order": 130,
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "parent": "",
      "opcode": "addzcg",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnCXDB",
      "caption": "添加床箱底板",
      "order": 135,
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "parent": "",
      "opcode": "addcxdb",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnbindcombnumber",
      "caption": "绑定组合号",
      "order": 140,
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "parent": "",
      "opcode": "bindcombnum",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnunbindcombnumber",
      "caption": "解绑组合号",
      "order": 145,
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "parent": "",
      "opcode": "unbindcombnum",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnresetsort",
      "caption": "重新排序",
      "order": 200,
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "parent": "",
      "opcode": "resetsort",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnaddpromotion",
      "caption": "促销活动",
      "order": 200,
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "parent": "",
      "opcode": "addpromotion",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnSelectionProc",
      "caption": "计价过程查询",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 10,
      "parent": "",
      "opcode": "selectionproc",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnunbindcontract",
      "caption": "解绑合同产品",
      "visible": "true",
      "disabled": "true",
      "style": "menu",
      "order": 160,
      "parent": "",
      "opcode": "unbindcontract",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnquerybarcode",
      "caption": "条码联查",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 160,
      "parent": "",
      "opcode": "querybarcode",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnPartsListQuery",
      "caption": "部件清单查询",
      "order": 201,
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "parent": "",
      "opcode": "partslistquery",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "btnquodetails",
      "caption": "报价明细",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 202,
      "parent": "",
      "opcode": "quodetails",
      "param": "",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "tbQueryFirstInventory",
      "caption": "查询一级库存",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 150,
      "parent": "",
      "opcode": "queryinventory",
      "param": "listMode:'lookup',selectDataType:'parentorg',fieldMap:{'fcustomdesc':'fcustomdes_e'}",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "tbPushFeedback",
      "caption": "总部售后",
      "visible": "true",
      "disabled": "false",
      "style": "menugroup",
      "order": 112,
      "parent": "droplist_more",
      "opcode": "pushfeedback",
      "param": "ruleId:'ydj_purchaseorder2ste_afterfeedback'"
    }
  ]
}