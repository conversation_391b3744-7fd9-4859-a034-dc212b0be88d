{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "lock_tbPull",
      "expression": "menu:tbPull$|fcustomerid=='' or fcustomerid==' '"
    },
    {
      "id": "unlock_tbPull",
      "expression": "menu:$tbPull|fcustomerid!='' or fcustomerid!=' '"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    //单据体相关
    //{ "expression": "ftaxamount=fsumtaxamount_a-famount_a" }, //单据体税额 = 单据体价税合计 - 单据体不含税金额
    //{ "expression": "famount_a=famount" }, //单据体不含税金额 = 单据头不含税金额

    //单据头相关
    //{ "expression": "fsumtaxamount=fsumtaxamount_a" }, //单据头价税合计 = 单据体价税合计
    //{ "expression": "famount=fsumtaxamount/(1+(ftaxrate*0.01))" }, //单据头不含税金额 = 单据头价税合计 / (1+(单据头税率 * 0.01))
    //{ "expression": "ftax=fsumtaxamount-famount" }, //单据头税额 = 单据头价税合计 - 单据头不含税金额

    //{ "expression": "fsumtaxamount_a=famount_a+ftaxamount" },
    //{ "expression": "famount=fsumtaxamount/(1+(ftaxrate*0.01))" },
    //{ "expression": "ftax=fsumtaxamount-famount" },

    //单据体的 不含税金额先根据税率变然后再向上汇总
    { "expression": "famount_a=fsumtaxamount_a/(1+(ftaxrate_a*0.01))" },
    //{ "expression": "fsumtaxamount_a=famount_a+ftaxamount" },
    { "expression": "famount_a=fsumtaxamount_a-ftaxamount" },
    { "expression": "ftaxamount=fsumtaxamount_a-famount_a" },

    //客户基础资料值变化时，携带客户属性字段到页面指定的字段上面
    { "expression": "finvoicetitle=fcustomerid__fname" },
    { "expression": "fopenbank=fcustomerid__fbank" },
    { "expression": "fbanknumber=fcustomerid__fbanknumber" },
    { "expression": "fphone=fcustomerid__fphone" },
    { "expression": "faddress=fcustomerid__faddress" },
    { "expression": "ftaxrate=fcustomerid__fcommontaxrate" },
    { "expression": "ftaxrate_a=fcustomerid__fcommontaxrate" },
    { "expression": "ftaxpayernumber=fcustomerid__ftaxpayernumber" }
  ]
}