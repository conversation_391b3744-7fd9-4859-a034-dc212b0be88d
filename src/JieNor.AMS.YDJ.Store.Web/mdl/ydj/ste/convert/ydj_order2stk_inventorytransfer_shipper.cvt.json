{
  "Id": "ydj_order2stk_inventorytransfer_shipper",
  "Number": "ydj_order2stk_inventorytransfer_shipper",
  "Name": "销售合同下推库存调拨（借货）_收货方",
  "SourceFormId": "ydj_order",
  "TargetFormId": "stk_inventorytransfer",
  "Visible": false,
  "ActiveEntityKey": "fentry",
  "FilterString": "fstatus='E' and fshipperagentid=@currentorgid ",
  "Message": "借货失败：<br>1、当前销售合同必须已审核！",
  "FieldMappings": [
    {
      "Id": "fbilltype",
      "Name": "单据类型",
      "MapType": 1,
      "SrcFieldId": "'invtransfer_billtype_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftransfertype",
      "Name": "调拨类型",
      "MapType": 1,
      "SrcFieldId": "'invtransfer_biztype_02'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftransferdirection",
      "Name": "调拨方向",
      "MapType": 1,
      "SrcFieldId": "'0'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdate",
      "Name": "调拨日期",
      "MapType": 1,
      "SrcFieldId": "@currentDate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffid",
      "Name": "发货人",
      "MapType": 1,
      "SrcFieldId": "@currentStaffId",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockdeptid",
      "Name": "发货部门",
      "MapType": 1,
      "SrcFieldId": "@currentDeptId",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffidto",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockdeptidto",
      "Name": "收货部门",
      "MapType": 0,
      "SrcFieldId": "",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkaddress",
      "Name": "收货地址",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forgtypeidto",
      "Name": "收货单位类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_customer'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forgidto",
      "Name": "收货单位",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forgresponsorto",
      "Name": "收货单位联系人",
      "MapType": 0,
      "SrcFieldId": "flinkstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeliverywayid",
      "Name": "货运方式",
      "MapType": 0,
      "SrcFieldId": "",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "flogisticsitems",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    /*以下是表体字段*****************************************************/
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fproductid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "调出辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfoto",
      "Name": "调入辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdesc",
      "Name": "调出定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdes_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcallupcustomdescto",
      "Name": "调入定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdes_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "调拨单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
        "Id": "fqty",
        "Name": "基本单位调拨数量",
        "MapType": 0,
        "SrcFieldId": "fqty",
        "MapActionWhenGrouping": 0,
        "Order": 0,
        "IgnoreChangeValidation": true
    },
    {
      "Id": "fstockstatus",
      "Name": "调出状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatus",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseid",
      "Name": "调出仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorelocationid",
      "Name": "调出仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "调出物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtonoto",
      "Name": "调入物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceformid",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid_h",
      "Name": "源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "源单明细内码",
      "MapType": 0,
      "SrcFieldId": "fentry.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertype",
      "Name": "调出货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertypeto",
      "Name": "调入货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "调出货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fowneridto",
      "Name": "调入货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }

  ],
  "BillGroups": [
    {
      "Id": "fentry_fentryid",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentry_fentryid",
      "Order": 1
    }
  ]
}