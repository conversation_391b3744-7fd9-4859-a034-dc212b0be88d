<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_category" basemodel="bd_basetmpl" el="3" cn="商品类别" ludt="ListTree" isolate="0"  IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="ser_ydj_category" pn="fbillhead" cn="商品类别">

        <!--基本信息-->
        <input group="基本信息" el="106" type="text" id="fparentid" ek="fbillhead" fn="fparentid" refid="ydj_category" ts="" visible="-1" cn="父级类别" />

        <!--类别ID串，用于存放某个类别的所有子类别ID，包括自己的ID，以便前端做树形快捷过滤-->
        <input group="基本信息" type="text" id="fpath" el="100" len="2000" ek="fbillhead" fn="fpath" ts="" visible="0" cn="类别ID串" />

        <input group="基本信息" ek="fbillhead" type="text" el="116" id="ffittings" fn="ffittings" dispfk="ffittings" ts="" cn="配件" visible="1150" sformid="" lock="0" lix="45" />

        <input group="基本信息" ek="fbillhead" type="text" el="116" id="fdisplayinapps" fn="fdisplayinapps" dispfk="fdisplayinapps" ts="" cn="小程序显示" visible="1150" sformid="" lock="0" lix="45" />

        <input group="基本信息" ek="fbillhead" el="116" id="feditprice" fn="feditprice" dispfk="feditprice" ts="" cn="零售价可编辑" visible="1150" sformid="" lock="0" lix="45" />
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
            <li el="11" clear></li>
            <li el="11" id="save_valid_fnumber" cn="保存时编码唯一" vid="500" data="fmainorgid,fnumber" precon=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fnumber','fname','fparentid','fdescription','fforbidstatus']}" permid=""></ul>
    </div>

</body>
</html>