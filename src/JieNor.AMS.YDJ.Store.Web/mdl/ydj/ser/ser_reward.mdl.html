<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ser_reward" basemodel="bill_basetmpl" el="1" cn="奖惩处理单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ser_reward" pn="fbillhead" cn="奖惩处理单">
    	
    	<!--基本信息-->
    	<select group="基本信息" el="122" ek="fbillhead" visible="-1" id="frewardpunish" fn="frewardpunish" pn="frewardpunish" cn="奖惩类型" cg="奖惩类型" defval="'reward01'" refid="bd_enum" dfld="fenumitem"></select>
    	
    	<input group="基本信息" el="112" type="date" id="forderdate" ek="fbillhead" fn="forderdate" pn="forderdate" cn="开单日期" visible="-1" defval="@currentshortdate"/>
		
		<input group="基本信息" el="100" type="text" id="fdescription" ek="fbillhead" fn="fdescription" ts="" visible="-1" len="500" cn="情况说明" />
        
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fteamid" fn="fteamid" pn="fteamid" cn="所属团队" cg="所属团队" refid="ydj_team"/>
        
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdutyid" fn="fdutyid" pn="fdutyid" cn="责任人" cg="责任人" refid="ydj_master"/>
        <!--处理方式-->
        <select group="处理方式" el="122" ek="fbillhead" visible="-1" id="frewardtype" fn="frewardtype" pn="frewardtype" cn="奖励方式" cg="奖励方式" refid="bd_enum" dfld="fenumitem"></select>
        
        <select group="处理方式" el="122" ek="fbillhead" visible="-1" id="fpunishtype" fn="fpunishtype" pn="fpunishtype" cn="处罚方式" cg="处罚方式" refid="bd_enum" dfld="fenumitem"></select>
        
        <input group="处理方式" el="100" ek="fbillhead" visible="-1" id="frewardmoney" fn="frewardmoney" pn="frewardmoney" cn="并奖励现金" />
        
        <input group="处理方式" el="100" ek="fbillhead" visible="-1" id="fpunishmoney" fn="fpunishmoney" pn="fpunishmoney" cn="并处罚现金" />
		
    </div>
	
	
</body>
</html>