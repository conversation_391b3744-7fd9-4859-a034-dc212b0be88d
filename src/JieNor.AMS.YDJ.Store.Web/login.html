<!DOCTYPE html>
<!--[if IE 8]> <html lang="zh-CN" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="zh-CN" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="zh-CN" style="height: 100%;background: url(/fw/images/oem/myhome-default-bg.jpg) 50% 50% repeat;">
<!--<![endif]-->
<head>
    <meta charset="utf-8" />
    <title></title>
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="cache-control" content="no-store" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <link href="/favicon.ico" rel="shortcut icon" />
    <!--字体图标样式库-->
    <link href="/fw/cdn/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" />
    <link href="/fw/cdn/simple-line-icons/2.4.1/css/simple-line-icons.min.css" rel="stylesheet" />
    <link href="/fw/cdn/bootstrap/3.3.5/css/bootstrap.min.css" rel="stylesheet" />
    <!--全站页面公共样式-->
    <link href="/fw/min/css/bundle.common.yi.min.css?v=********" rel="stylesheet" />
    <link href="/fw/css/ydj/myhome_login_register.css" rel="stylesheet" />
</head>
<body class="login">
    <header>
        <div style="background: #F2F2F2" class="showText">
            <span class="closeX">X</span>
            <p class="divShowText"></p>
        </div>
        <div class="header_box">
            <div class="logo-image">
                <img src="/fw/images/oem/myhome-logo.png" />
            </div>
            <div class="title">
                健康睡眠资源整合者
            </div>
            <div class="right-mes">
                <a href="http://www.91myhome.com/" target="_blank">易到家官网</a>
                客服热线：0755-2665 5994
            </div>
        </div>
    </header>
    <div class="login-box">
        <div class="login-center-box">
            <div class="switch-box">
                <ul class="switch-lab">
                    <li class="active" style="width:100%;padding-top: 15px;font-size: initial;">用户登录</li>
                    <!--<li>加入企业</li>-->
                </ul>
                <form class="login-form switch-lab1" method="post">
                    <div class="alert alert-danger display-hide">
                        <button class="close" data-close="alert"></button>
                        <span></span>
                    </div>
                    <div class="input-icon login-input">
                        <i class="fa fa-user"></i>
                        <input class="placeholder-no-fix default-input icon-input" type="text"
                               autocomplete="off" placeholder="请填写登录账号/手机号" id="loginUserName" name="username" />
                    </div>
                    <div class="input-icon login-input">
                        <i class="fa fa-lock"></i>
                        <input class="placeholder-no-fix default-input icon-input" type="password"
                               autocomplete="off" placeholder="密码" id="loginPassword" name="password" />
                        <i id="loginPasswordEye" class="fa fa-eye-slash pwd-eye"></i>
                    </div>
                    <div class="checkbox-list auto-login">
                        <label class="checkbox-inline">
                            <input type="checkbox" id="ck_rmbUser">记住密码
                        </label>
                    </div>
                    <a class="forget-pwd" href="/forgetpwd.html" id="forget-password" title="找回密码">忘记密码？</a>
                    <button type="submit" id="ydjlogin" class="login-btn blue-btn marT30">登录</button>
                    <div class="login-options">
                        <h5>或使用以下帐号登录</h5>
                        <ul class="social-icons">
                            <li class="sso_wechat">
                                <img src="/fw/images/login/wechat.png" title="用微信账号登录" />
                            </li>
                            <!--<li>
                                <a id="sso_ac" class="yahoo login-type" data-original-title="Twitter" href="javascript:;" title="用云链账号登录"></a>
                            </li>-->
                            <!--<li>
                                <a id="sso_llbpm" class="twitter login-type" data-original-title="Twitter" href="javascript:;" title="用蓝凌BPM账号登录"></a>
                            </li>-->
                        </ul>
                    </div>
                </form>
                <div class="login-form switch-lab2 display-hide">
                    <!-- 注册表单——填写用户信息 -->
                    <form action="javascript:void(0);" class="register-form" method="post">
                        <div class="alert alert-danger display-hide">
                            <button class="close" data-close="alert"></button>
                            <span></span>
                        </div>
                        <div class="input-icon">
                            <!-- ie8 ie9 不支持 html5 占位符，所以我们只显示字段标题 -->
                            <label class="control-label visible-ie8 visible-ie9">用户名</label>
                            <div class="input-icon login-input">
                                <i class="fa fa-user"></i>
                                <input class="placeholder-no-fix default-input icon-input" type="text"
                                       autocomplete="off" placeholder="用户名" id="txtUserName" name="userName" />
                            </div>
                            <div id="unError" class="error-msg"></div>
                        </div>
                        <div class="input-icon">
                            <label class="control-label visible-ie8 visible-ie9">密码</label>
                            <div class="input-icon login-input">
                                <i class="fa fa-lock"></i>
                                <input class="placeholder-no-fix default-input icon-input" type="password"
                                       autocomplete="off" placeholder="密码" id="txtPassword" name="password" />
                            </div>
                            <div id="pwError" class="error-msg"></div>
                        </div>
                        <div class="input-icon">
                            <label class="control-label visible-ie8 visible-ie9">确认密码</label>
                            <div class="input-icon login-input">
                                <i class="fa fa-lock"></i>
                                <input class="placeholder-no-fix default-input icon-input" type="password"
                                       autocomplete="off" placeholder="确认密码" id="txtRePassword" name="rePassword" />
                            </div>
                            <div id="repwError" class="error-msg"></div>
                        </div>
                        <div class="input-icon">
                            <label class="control-label visible-ie8 visible-ie9">企业代码</label>
                            <div class="input-icon login-input">
                                <i class="fa fa fa-credit-card"></i>
                                <input class="placeholder-no-fix default-input icon-input" type="text"
                                       autocomplete="off" placeholder="输入要加入的企业代码" id="txtJoinEnterpriseId" name="joinEnterpriseId" />
                            </div>
                            <div id="entError" class="error-msg"></div>
                        </div>
                        <div class="input-icon" style="position: relative;">
                            <label class="control-label visible-ie8 visible-ie9">核验码</label>
                            <div class="input-icon login-input">
                                <i class="fa fa-key"></i>
                                <input class="placeholder-no-fix default-input icon-input" type="text"
                                       autocomplete="off" placeholder="请向管理员索取核验码" id="txtJoinCode" name="joinCode" />
                            </div>
                            <div id="joinCodeError" class="error-msg"></div>
                        </div>
                        <div class="input-icon">
                            <!-- ie8 ie9 不支持 html5 占位符，所以我们只显示字段标题 -->
                            <label class="control-label visible-ie8 visible-ie9">手机号</label>
                            <div class="input-icon login-input">
                                <i class="fa fa-phone"></i>
                                <input class="placeholder-no-fix default-input icon-input" type="text"
                                       autocomplete="off" placeholder="手机号" id="txtPhone" name="phone" />
                            </div>
                            <div id="telError" class="error-msg"></div>
                        </div>
                        <div class="input-icon" style="margin-bottom: 0;">
                            <label class="control-label visible-ie8 visible-ie9">验证码</label>
                            <div class="input-icon login-input code txtCode">
                                <div class="col-xs-7 no-pad">
                                    <input class="placeholder-no-fix default-input" type="text"
                                           autocomplete="off" placeholder="验证码" id="txtCode" name="code" />
                                    <div id="recError" class="error-msg" style="margin-top: 10px;"></div>
                                </div>
                                <div class="col-xs-5" style="padding:0 0 0 10px;">
                                    <div class="authcode blue-btn" style="margin-top:0;line-height:38px;font-size:14px;">获取验证码</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <label class="checkbox">
                            </label>
                            <div class="blue-btn to-register marT20">注册</div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="copyright">
        
    </div>
    <!-- JavaScript（将 JavaScript 放在页面底部，可以降低页面加载时间）-->
    <!-- 核心插件 -->
    <!--[if lt IE 9]>
    <script src="/fw/include/other/respond.min.js"></script>
    <script src="/fw/include/other/excanvas.min.js"></script>
    <![endif]-->
    <script src="/fw/cdn/jquery/1.11.3/jquery.min.js"></script>
    <!--迁移jq库-->
    <script src="/fw/cdn/jquery-migrate/1.4.1/jquery-migrate.min.js"></script>
    <script src="/fw/cdn/jquery-cookie/1.4.1/jquery.cookie.min.js"></script>
    <script src="/fw/cdn/jquery-validate/1.17.0/jquery.validate.min.js"></script>
    <script src="/fw/cdn/jquery-validate/1.17.0/localization/messages_zh.min.js"></script>
    <!--提供遮罩效果,metronic.blockUi依赖这个-->
    <script src="/fw/cdn/jquery.blockUI/2.70/jquery.blockUI.min.js"></script>
    <!--bootstrap-->
    <script src="/fw/cdn/bootstrap/3.3.5/js/bootstrap.min.js"></script>
    <!--基础公共组件-->
    <script src="/fw/min/js/bundle.common.yi.min.js?v=********"></script>
    <script src="/fw/js/ydj/account/login.js?v=********"></script>
    <script src="/fw/js/ydj/account/browser.js"></script>
    <script>
        var info = browser();
        const container = document.querySelector(".showText");
        const closeBtn = container.querySelector(".closeX");
        const text = container.querySelector(".divShowText");
        if (info.browser !== "Chrome") {
            $('.divShowText').html("请用最新版本谷歌浏览器登录， 谷歌浏览器Chrome下载地址：<a href='https://www.google.cn/chrome/' target='_Blank'>https://www.google.cn/chrome/</a>")
        } else {
            $(".login header").css("height", "75px");
            text.style.display = "none";
            closeBtn.style.display = "none";
        }
       
        closeBtn.addEventListener("click", () => {
            text.style.display = "none";
            closeBtn.style.display = "none";
            $(".login header").css("height","75px");
        });

        $('#loginPasswordEye').on('click', handleEye)

        function handleEye() {
            if ($(this).hasClass('fa-eye-slash')) {
                $(this).removeClass('fa-eye-slash').addClass('fa-eye')
                $(this).siblings('input').attr('type', 'text')
            } else {
                $(this).removeClass('fa-eye').addClass('fa-eye-slash')
                $(this).siblings('input').attr('type', 'password')
            }
        }

    </script>
</body>
</html>