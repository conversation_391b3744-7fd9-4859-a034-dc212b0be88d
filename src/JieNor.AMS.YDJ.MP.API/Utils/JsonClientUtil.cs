using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using Newtonsoft.Json.Linq;
using ServiceStack;
using ServiceStack.Web;

namespace JieNor.AMS.YDJ.MP.API.Utils
{
    /// <summary>
    /// 外部服务访问接口响应工具类
    /// </summary>
    public static class JsonClientUtil
    {
        /// <summary>
        /// 检查操作结果
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="response">外部服务的响应文本</param>
        /// <param name="resp">操作响应结果</param>
        /// <returns></returns>
        public static bool CheckSuccess<T>(string response, BaseResponse<T> resp) where T : class, new()
        {
            var jObj = JObject.Parse(response);
            var result = jObj["operationResult"];
            if (result == null || !result["isSuccess"].Value<bool>() || ((JArray)result["complexMessage"]["errorMessages"]).Count > 0)
            {
                var complexMessage = result["complexMessage"] as JObject;

                var errorMessages = complexMessage?["errorMessages"] as JArray;

                if (errorMessages != null && errorMessages.Count > 0)
                {
                    resp.Message = string.Join(",", errorMessages);
                    return false;
                }

                string simpleMessage = result["simpleMessage"].Value<string>();
                if (!simpleMessage.IsNullOrEmptyOrWhiteSpace())
                {
                    resp.Message = simpleMessage;
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 解析出uiData
        /// </summary>
        /// <param name="response">外部服务的响应文本</param>
        /// <returns></returns>
        public static JToken ParseUiData(string response)
        {
            var jObj = JObject.Parse(response);

            return jObj?["operationResult"]?["htmlActions"]?.First["actionParams"]?["fsp"]?["uiData"];
        }

        public static Dictionary<string, string> AddBillLockParams(this Dictionary<string, string> simpleData, IRequest request)
        {
            if (simpleData == null)
            {
                simpleData = new Dictionary<string, string>();
            }

            simpleData["__UseBillLock__"] = "true";
            simpleData["__LockToken__"] = GetBillLockToken(request);

            return simpleData;
        }

        public static Dictionary<string, object> AddMSAPIDefaultParams(this Dictionary<string, object> option)
        {
            if (option == null)
            {
                option = new Dictionary<string, object>();
            }

            option["callerTerminal"] = "MPAPI";

            return option;
        }

        public static string GetBillLockToken(this IRequest request)
        {
            //单据锁定
            var lockToken = request?.GetHeader("X-LockToken");

            return lockToken;
        }
    }
}
