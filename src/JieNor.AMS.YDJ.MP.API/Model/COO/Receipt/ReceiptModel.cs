using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model.BAS.CollectingUnit;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 收款单列表数据模型
    /// </summary>
    public class ReceiptModel : BaseBillModel
    {
        /// <summary>
        /// 业务状态
        /// </summary>
        public string BizStatus { get; set; }

        /// <summary>
        /// 审核状态
        /// </summary>
        public bool AuditStatus { get; set; }

        /// <summary>
        /// 客户姓名
        /// </summary>
        public string Customer { get; set; }

        /// <summary>
        /// 客户手机号
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 银行账号
        /// </summary>
        public string Mybankid { get; set; }

        /// <summary>
        /// 收款金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 确认已收
        /// </summary>
        public string ConfirmedAmount { get; set; }

        /// <summary>
        /// 收款待确认
        /// </summary>
        public decimal ReceivableToBeConfirmed { get; set; }

        /// <summary>
        /// 待结算金额
        /// </summary>
        public decimal UnsettleAmount { get; set; }

        /// <summary>
        /// 收款人
        /// </summary>
        public string Creator { get; set; }

        /// <summary>
        /// 用途
        /// </summary>
        public string Purpose { get; set; }

        /// <summary>
        /// 支付日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 支付方式
        /// </summary>
        public string Way { get; set; }

        /// <summary>
        /// 款项说明
        /// </summary>
        public string PaymentDesc { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public List<BaseImageModel> Images { get; set; }

        /// <summary>
        /// 凭证数组 fimage
        /// </summary>
        public List<BaseDataSaveDTO> Certificates { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 货款金额
        /// </summary>
        public decimal LoanAmount { get; set; }
        /// <summary>
        /// 销售部门
        /// </summary>
        public string Deptid { get; set; }
        /// <summary>
        /// 销售员
        /// </summary>
        public string Staffid { get; set; }

        /// <summary>
        /// 辅助资料、简单枚举、单据类型 下拉框数据源
        /// </summary>
        public Dictionary<string, List<Dictionary<string, object>>> ComboData { get; set; }

        /// <summary>
        /// 代收单位类型Id
        /// </summary>
        public string UnitType { get; set; }
        /// <summary>
        /// 代收单位Id
        /// </summary>
        public string Unitid { get; set; }

        /// <summary>
        /// 收款小票号
        /// </summary>
        public string ReceiptNo { get; set; }
        public string cusacount { get; set; }

        /// <summary>
        /// 代收单位
        /// </summary>
        public List<CollectionUnitModel> CollectionUnit { get; set; } = new List<CollectionUnitModel>();
        /// <summary>
        /// 客户来源门店
        /// </summary>
        public string CustomerSrcStoreId { get; set; }

        /// <summary>
        /// 联合开单
        /// </summary>
        public List<OrderJoinStaffModel> JoinStaffs { get; set; } = new List<OrderJoinStaffModel>();
    }
}