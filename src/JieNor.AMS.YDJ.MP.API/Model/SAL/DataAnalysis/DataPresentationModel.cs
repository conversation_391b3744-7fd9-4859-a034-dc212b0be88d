using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model.SAL.DataAnalysis
{
    /// <summary>
    /// 数据简报
    /// </summary>
    public class DataPresentationModel: DataBaseModel
    {
        /// <summary>
        /// 报备商机数
        /// </summary>
        public int BusinessNum { get; set; }
        /// <summary>
        /// 成交客户数
        /// </summary>
        public int CustomerNum { get; set; }
        /// <summary>
        /// 新增销售意向数
        /// </summary>
        public int NewintentionNum { get; set; }
        /// <summary>
        /// 未成单意向额
        /// </summary>
        public decimal IntentionMoney { get; set; }
        /// <summary>
        /// 新增合同数
        /// </summary>
        public int NewContractNum { get; set; }
        /// <summary>
        /// 已审核销售额
        /// </summary>
        public decimal SaleMoney { get; set; }
    }

}
