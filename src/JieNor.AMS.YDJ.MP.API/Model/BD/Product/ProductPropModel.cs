using JieNor.AMS.YDJ.DataTransferObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 商品属性数据模型
    /// </summary>
    public class ProductPropModel
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 商品编码
        /// </summary>
        public string Number { get; set; } = string.Empty;

        /// <summary>
        /// 商品名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 辅助属性列表
        /// </summary>
        public List<PropModel> AuxPropInfo { get; set; } = new List<PropModel>();
    }
}
