using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 客户匹配数据模型
    /// </summary>
    public class CustomerMatchModel
    {
        /// <summary>
        /// id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string Contact { get; set; }

        /// <summary>
        /// 客户手机号
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 微信号
        /// </summary>
        public string Wechat { get; set; }

        /// <summary>
        /// 省
        /// </summary>
        public ComboDataModel Province { get; set; } = new ComboDataModel();
        /// <summary>
        /// 市
        /// </summary>
        public ComboDataModel City { get; set; } = new ComboDataModel();

        /// <summary>
        /// 区域
        /// </summary>
        public ComboDataModel Region { get; set; } = new ComboDataModel();

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public ComboDataModel Gender { get; set; } = new ComboDataModel();

        /// <summary>
        /// 年龄段
        /// </summary>
        public ComboDataModel Age { get; set; } = new ComboDataModel();

        /// <summary>
        /// 客户来源
        /// </summary>
        public ComboDataModel Source { get; set; } = new ComboDataModel();

        /// <summary>
        /// 楼盘
        /// </summary>
        public BaseDataSimpleModel House { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 合作渠道
        /// </summary>
        public BaseDataSimpleModel Channel { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 附件
        /// </summary>
        public List<BaseImageModel> Images { get; set; } = new List<BaseImageModel>();
    }
}