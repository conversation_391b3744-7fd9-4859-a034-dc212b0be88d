using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 客户详情数据模型
    /// </summary>
    public class CustomerDetailModel : BaseDataModel
    {
        public new ComboDataModel Status { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string Contact { get; set; }

        /// <summary>
        /// 客户手机号
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 消费金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 当前积分
        /// </summary>
        public decimal Integral { get; set; }

        /// <summary>
        /// 微信号
        /// </summary>
        public string Wechat { get; set; }

        /// <summary>
        ///  国家
        /// </summary>
        public ComboDataModel Country { get; set; }

        /// <summary>
        /// 所在区域
        /// </summary>
        public string District { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public ComboDataModel Gender { get; set; }

        /// <summary>
        /// 年龄段
        /// </summary>
        public ComboDataModel Age { get; set; }

        /// <summary>
        /// 客户性质
        /// </summary>
        public ComboDataModel Nature { get; set; }

        /// <summary>
        /// 客户来源
        /// </summary>
        public ComboDataModel Source { get; set; }

        /// <summary>
        /// 客户等级
        /// </summary>
        public BaseDataSimpleModel Level { get; set; }

        /// <summary>
        /// 楼盘
        /// </summary>
        public BaseDataSimpleModel House { get; set; }

        /// <summary>
        /// 合作渠道
        /// </summary>
        public BaseDataSimpleModel Channel { get; set; }

        ///// <summary>
        ///// 负责人
        ///// </summary>
        //public BaseDataSimpleModel Duty { get; set; }

        ///// <summary>
        ///// 部门
        ///// </summary>
        //public BaseDataSimpleModel Dept { get; set; }

        /// <summary>
        /// 负责人列表
        /// </summary>
        public List<CustomerDutyModel> DutyList { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public List<BaseImageModel> Images { get; set; }

        /// <summary>
        /// 服务信息列表
        /// </summary>
        public List<CustomerServiceModel> ServiceList { get; set; }

        /// <summary>
        /// 客户类型（ftype）
        /// </summary>
        public ComboDataModel Type { get; set; }

        /// <summary>
        /// 客户分类（fcustomertype）
        /// </summary>
        public ComboDataModel CustomerType { get; set; }

        /// <summary>
        /// 最后跟进时间
        /// </summary>
        public DateTime? FollowTime { get; set; }


        /// <summary>
        /// 货款余额
        /// </summary>
        public decimal LoanAmount { get; set; }

        /// <summary>
        /// 企业微信用户编码
        /// </summary>
        public string WorkWxUserid { get; set; }

        /// <summary>
        /// 推荐人
        /// </summary>
        public object Referrer { get; set; } = new
        {
            Id = "",
            Number = "",
            Name = "",
            Phone = ""
        };


        public BaseDataSimpleModel Building { get; set; } = new BaseDataSimpleModel();

        public string Memberno { get; set; }

        /// <summary>
        /// 来源部门
        /// </summary>
        public BaseDataSimpleModel SourceStore { get; set; }
        
        /// <summary>
        /// 开票信息
        /// </summary>
        public List<InvoiceEntry> InvoiceEntries { get; set; }
    }

    /// <summary>
    /// 客户服务信息数据模型
    /// </summary>
    public class CustomerServiceModel
    {
        /// <summary>
        /// 商机标识
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 商机编号
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// 意向金额
        /// </summary>
        public decimal WishAmount { get; set; }

        /// <summary>
        /// 合同金额
        /// </summary>
        public decimal PactAmount { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        public BaseDataSimpleModel Duty { get; set; }

        /// <summary>
        /// 阶段
        /// </summary>
        public ComboDataModel Phase { get; set; }

        /// <summary>
        /// 报备时间
        /// </summary>
        public DateTime CreateDate { get; set; }
    }

    /// <summary>
    /// 客户负责人数据模型
    /// </summary>
    public class CustomerDutyModel
    {
        /// <summary>
        /// id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        public BaseDataSimpleModel Duty { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public BaseDataSimpleModel Dept { get; set; }

        /// <summary>
        /// 加入时间
        /// </summary>
        public DateTime JoinTime { get; set; }

        /// <summary>
        /// 说明
        /// </summary>
        public string Description { get; set; }

    }
}