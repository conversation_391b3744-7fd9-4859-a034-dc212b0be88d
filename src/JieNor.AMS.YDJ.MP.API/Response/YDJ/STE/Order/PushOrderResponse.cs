using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Response.YDJ.STE.Order.PushOrder
{
    public class PushOrderResponse
    {
        public string id { get; set; }
        public string fbillno { get; set; }
        public string fdescription { get; set; }
        public Fcreatorid fcreatorid { get; set; }
        public string fcreatedate { get; set; }
        public Fmodifierid fmodifierid { get; set; }
        public string fmodifydate { get; set; }
        public Fstatus fstatus { get; set; }
        public Fapproveid fapproveid { get; set; }
        public string fapprovedate { get; set; }
        public bool fcancelstatus { get; set; }
        public Fcancelid fcancelid { get; set; }
        public string fcanceldate { get; set; }
        public Fchangestatus fchangestatus { get; set; }
        public Fclosestatus fclosestatus { get; set; }
        public Fcloseid fcloseid { get; set; }
        public string fclosedate { get; set; }
        public string fnextprocnode { get; set; }
        public Fmainorgid fmainorgid { get; set; }
        public string fbizruleid { get; set; }
        public string fflowinstanceid { get; set; }
        public string ftranid { get; set; }
        public string ffromtranid { get; set; }
        public string froottranid { get; set; }
        public Fsourcetype fsourcetype { get; set; }
        public string fsourcenumber { get; set; }
        public Fpublishcid fpublishcid { get; set; }
        public string fdataorigin { get; set; }
        public int fprintcount { get; set; }
        public Fprintid fprintid { get; set; }
        public string fprintdate { get; set; }
        public string frecordcontent { get; set; }
        public string freminddate { get; set; }
        public Fbilltype fbilltype { get; set; }
        public Ftype ftype { get; set; }
        public Facceptstatus facceptstatus { get; set; }
        public Facceptperson facceptperson { get; set; }
        public string fsign { get; set; }
        public string forderdate { get; set; }
        public string facceptdate { get; set; }
        public string fdeliverydate { get; set; }
        public Fcustomerid fcustomerid { get; set; }
        public string flinkstaffid { get; set; }
        public string fphone { get; set; }
        public Fprovince fprovince { get; set; }
        public Fcity fcity { get; set; }
        public Fregion fregion { get; set; }
        public Fbuildingid fbuildingid { get; set; }
        public string faddress { get; set; }
        public Fbrandid_E fbrandid_e { get; set; }
        public string flogisticsitems { get; set; }
        public Fdeptid fdeptid { get; set; }
        public Fstaffid fstaffid { get; set; }
        public Fchannel fchannel { get; set; }
        public Fstylistid fstylistid { get; set; }
        public string fdesignscheme { get; set; }
        public string fscalerecord { get; set; }
        public string foldorderno { get; set; }
        public string fmallorderno { get; set; }
        public string fhqderno { get; set; }
        public string fwithin { get; set; }
        public Finnercustomerid finnercustomerid { get; set; }
        public string fscancode { get; set; }
        public string fcommercebillno { get; set; }
        public bool fstricttrack { get; set; }
        public bool fisapplypur { get; set; }
        public bool fissubjoin { get; set; }
        public bool fenablenotice { get; set; }
        public Flockstate flockstate { get; set; }
        public string flockdate { get; set; }
        public Flockpeople flockpeople { get; set; }
        public string fisitlocked { get; set; }
        public Fclosestate fclosestate { get; set; }
        public string fdept { get; set; }
        public string fstaff { get; set; }
        public string fstylist { get; set; }
        public Finnercustomerid fterminalcustomer { get; set; }
        public string fcoophone { get; set; }
        public string fprovincecityregion { get; set; }
        public string fcooaddress { get; set; }
        public Fduty fduty { get; set; }
        public string fsubjoincause { get; set; }
        public bool fiswholedis { get; set; }
        public Fdisopt fdisopt { get; set; }
        public decimal fdiscscale { get; set; }
        public float fdiscscale_temp { get; set; }
        public Ffavoropt ffavoropt { get; set; }
        public bool fisremovetail { get; set; }
        public bool fisfixedprice { get; set; }
        public float ffixedprice { get; set; }
        public bool fdontreflect { get; set; }
        public decimal fdistsumamount { get; set; }
        public decimal fdistsumrate { get; set; }

        public decimal fdistamount { get; set; }
        public decimal ffaceamount { get; set; }
        public decimal fdealamount_h { get; set; }
        public decimal freceivable { get; set; }
        public float funreceived { get; set; }
        public decimal fsumreceivable { get; set; }
        public float fexpense { get; set; }
        public float frefundamount { get; set; }
        public float factrefundamount { get; set; }
        public Freceiptstatus freceiptstatus { get; set; }
        public float faccountscale { get; set; }
        public float fconfirmamount { get; set; }
        public float fcollectamount { get; set; }
        public float fcollectedamount { get; set; }
        public float fbrokerage { get; set; }
        public float fplanbrokerage { get; set; }
        public float finvoiceamount { get; set; }
        public float ffirstamount { get; set; }
        public decimal fsumamount { get; set; }
        public float fexpenditure { get; set; }
        public float fprofit { get; set; }
        public float fsumcost { get; set; }
        public decimal fproductcost { get; set; }
        public Fcostsource fcostsource { get; set; }
        public float fsumoutqty { get; set; }
        public float fbizsumoutqty { get; set; }
        public string fcostid { get; set; }
        public bool fiscost { get; set; }
        public float fverifyamount { get; set; }
        public string fparenttranid { get; set; }
        public string ftoptranid { get; set; }
        public object[] sealimg { get; set; }
        public Sumfield sumfield { get; set; }
        public Fdutyentry[] fdutyentry { get; set; }
        public object[] fsubjoinentry { get; set; }
        public Fentry[] fentry { get; set; }
        public Fdrawentity[] fdrawentity { get; set; }
        public object[] fexpenseentry { get; set; }
        public object[] fdisburseentry { get; set; }
    }

    public class Fcreatorid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fmodifierid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fstatus
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fapproveid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fcancelid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fchangestatus
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fclosestatus
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fcloseid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fmainorgid
    {
        public string id { get; set; }
        public string name { get; set; }
        public string productId { get; set; }
    }

    public class Fsourcetype
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fpublishcid
    {
        public string id { get; set; }
        public string name { get; set; }
        public string productId { get; set; }
    }

    public class Fprintid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fbilltype
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Ftype
    {
        public string fenumitem { get; set; }
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Facceptstatus
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Facceptperson
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fcustomerid
    {
        public string fphone { get; set; }
        public string femail { get; set; }
        public string flegalperson { get; set; }
        public string fcardid { get; set; }
        public string fbank { get; set; }
        public string faccountname { get; set; }
        public string fbanknumber { get; set; }
        public string fcontacts { get; set; }
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fprovince
    {
        public string fenumitem { get; set; }
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fcity
    {
        public string fenumitem { get; set; }
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fregion
    {
        public string fenumitem { get; set; }
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fbuildingid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fbrandid_E
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fdeptid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fstaffid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fchannel
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fstylistid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Finnercustomerid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Flockstate
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Flockpeople
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fclosestate
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fduty
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fdisopt
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Ffavoropt
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Freceiptstatus
    {
        public string fenumitem { get; set; }
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fcostsource
    {
        public string fenumitem { get; set; }
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Sumfield
    {
    }

    public class Fdutyentry
    {
        public string id { get; set; }
        public int FSeq { get; set; }
        public bool fismain { get; set; }
        public Fdutyid fdutyid { get; set; }
        public decimal fratio { get; set; }
        public decimal famount_ed { get; set; }
        public string fdescription_ed { get; set; }
        public string fdutyentry_ftranid { get; set; }
        public string fdutyentry_fparenttranid { get; set; }
        public string fdutyentry_ftoptranid { get; set; }
        public string fdutyentry_fdataorigin { get; set; }
        public Fdeptid fdeptid { get; set; }
        
        public decimal fdeptperfratio { set; get; }
    }

    public class Fdutyid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fentry
    {
        public string id { get; set; }
        public int FSeq { get; set; }
        public bool fisself { get; set; }
        public Fsuitid fsuitid { get; set; }
        public bool fissplit { get; set; }
        public bool fisgiveaway { get; set; }
        public Fproductid fproductid { get; set; }
        public string fmtrlnumber { get; set; }
        public Fmulfile fmulfile { get; set; }
        public string fmtrlmodel { get; set; }
        public string fattribute { get; set; }
        public Fmtrlimage fmtrlimage { get; set; }
        public string fbrandid { get; set; }
        public string fseriesid { get; set; }
        public Fattrinfo fattrinfo { get; set; }
        public string fcustomdes_e { get; set; }
        public string fcustom { get; set; }
        public string fispresetprop { get; set; }
        public Funitid funitid { get; set; }
        public float fqty { get; set; }
        public Fbizunitid fbizunitid { get; set; }
        public float fbizqty { get; set; }
        public decimal fprice { get; set; }
        public string fperfdesc { get; set; }
        public decimal famount { get; set; }
        public decimal fdistrate { get; set; }
        public decimal fdistamount_e { get; set; }
        public decimal fdealprice { get; set; }
        public decimal fdealamount_e { get; set; }
        public Fsupplierid fsupplierid { get; set; }
        public Flogisticscompanyid flogisticscompanyid { get; set; }
        public Fdeptid_E fdeptid_e { get; set; }
        public bool fisreqiured { get; set; }
        public Forderstatus forderstatus { get; set; }
        public bool fisdelivery { get; set; }
        public Fdeliverystatus fdeliverystatus { get; set; }
        public bool fisoutspot { get; set; }
        public Fdeliverymode fdeliverymode { get; set; }
        public Fstockstatus fstockstatus { get; set; }
        public Fstorehouseid fstorehouseid { get; set; }
        public Fstorelocationid fstorelocationid { get; set; }
        public string fmtono { get; set; }
        public Fownertype fownertype { get; set; }
        public Fownerid fownerid { get; set; }
        public bool fstricttrack_e { get; set; }
        public Foperationmode foperationmode { get; set; }
        public string fdescription_e { get; set; }
        public string fsourceentryid_e { get; set; }
        public string fsourcenumber_e { get; set; }
        public fsourcetype_e fsourcetype_e { get; set; }
        public string fexdeliverydate { get; set; }
        public float fdeliveryqty { get; set; }
        public float foutqty { get; set; }
        public float fbizdeliveryqty { get; set; }
        public float fbizoutqty { get; set; }
        public float fpurqty { get; set; }
        public float fbizpurqty { get; set; }
        public float fsellprice { get; set; }
        public float freturnnoticeqty { get; set; }
        public float freturnqty { get; set; }
        public float frefundqty { get; set; }
        public float fbizreturnnoticeqty { get; set; }
        public float fbizreturnqty { get; set; }
        public float fbizrefundqty { get; set; }
        public Fclosestatus_E fclosestatus_e { get; set; }
        public float flength { get; set; }
        public float fwidth { get; set; }
        public float fthick { get; set; }
        public float farea { get; set; }
        public bool fisnsc { get; set; }
        public float fnsc { get; set; }
        public bool fissub { get; set; }
        public bool fissize { get; set; }
        public bool fpromotion { get; set; }
        public string flinkpro { get; set; }
        public int flinkweight { get; set; }
        public float fstandardprice { get; set; }
        public float fcostprice { get; set; }
        public float fcost { get; set; }
        public float fgain { get; set; }
        public string fsuitentryid { get; set; }
        public float fscheduledqty { get; set; }
        public string fissuit { get; set; }
        public string fselectionnumber { get; set; }
        public Fforproductid fforproductid { get; set; }
        public Fforsuiteselectionid fforsuiteselectionid { get; set; }
        public string fdescription_suite { get; set; }
        public string fpackagedescription { get; set; }
        public string fentry_ftranid { get; set; }
        public string fentry_fparenttranid { get; set; }
        public string fentry_ftoptranid { get; set; }
        public string fentry_fdataorigin { get; set; }
        public object[] fdetail { get; set; }
        public Fspace fspace { get; set; }

        public bool fisclearstock { get; set; }

        public bool fissuitflag { get; set; }
        public string fsuitcombnumber { get; set; }
    }

    public class Fsuitid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fproductid
    {
        public decimal fguideprice { get; set; }

        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fmulfile
    {
        public string id { get; set; }
        public string name { get; set; }
    }

    public class Fmtrlimage
    {
        public string id { get; set; }
        public string name { get; set; }
    }

    public class Fattrinfo
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
        public string fdescription { get; set; }
        public Fcreatorid1 fcreatorid { get; set; }
        public string fcreatedate { get; set; }
        public Fmodifierid1 fmodifierid { get; set; }
        public string fmodifydate { get; set; }
        public bool fispreset { get; set; }
        public Fstatus1 fstatus { get; set; }
        public Fapproveid1 fapproveid { get; set; }
        public string fapprovedate { get; set; }
        public bool fforbidstatus { get; set; }
        public Fforbidid fforbidid { get; set; }
        public string fforbiddate { get; set; }
        public Fchangestatus1 fchangestatus { get; set; }
        public string fnextprocnode { get; set; }
        public Fmainorgid1 fmainorgid { get; set; }
        public string fprimitiveid { get; set; }
        public string fbizruleid { get; set; }
        public string fflowinstanceid { get; set; }
        public string ftranid { get; set; }
        public string ffromtranid { get; set; }
        public string froottranid { get; set; }
        public int fprintcount { get; set; }
        public Fprintid1 fprintid { get; set; }
        public string fprintdate { get; set; }
        public string frecordcontent { get; set; }
        public string freminddate { get; set; }
        public string fsendstatus { get; set; }
        public string fdataorigin { get; set; }
        public Fpublishcid1 fpublishcid { get; set; }
        public string fsenddate { get; set; }
        public string fdownloaddate { get; set; }
        public string fchaindataid { get; set; }
        public string ffromchaindataid { get; set; }
        public Fmaterialid fmaterialid { get; set; }
        public string fmtrlnumber { get; set; }
        public string fparenttranid { get; set; }
        public string ftoptranid { get; set; }
        public string fname_py { get; set; }
        public string fname_py2 { get; set; }
        public object[] sealimg { get; set; }
        public Sumfield1 sumfield { get; set; }
        public object[] fentity { get; set; }
    }

    public class Fcreatorid1
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fmodifierid1
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fstatus1
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fapproveid1
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fforbidid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fchangestatus1
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fmainorgid1
    {
        public string id { get; set; }
        public string name { get; set; }
        public string productId { get; set; }
    }

    public class Fprintid1
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fpublishcid1
    {
        public string id { get; set; }
        public string name { get; set; }
        public string productId { get; set; }
    }

    public class Fmaterialid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Sumfield1
    {
    }

    public class Funitid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fbizunitid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fsupplierid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Flogisticscompanyid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fdeptid_E
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Forderstatus
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fdeliverystatus
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fdeliverymode
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fstockstatus
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fstorehouseid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class fsourcetype_e
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fspace
    {
        public string fenumitem { get; set; }
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fstorelocationid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fownertype
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fownerid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Foperationmode
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fclosestatus_E
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fforproductid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fforsuiteselectionid
    {
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fdrawentity
    {
        public string id { get; set; }
        public int FSeq { get; set; }
        public string fsourceentryid { get; set; }
        public string ffilename { get; set; }
        public Ffileid ffileid { get; set; }
        public string ffileformat { get; set; }
        public string ffilesize { get; set; }
        public string fnote_d { get; set; }
        public string fuploader { get; set; }
        public string fuploaderid { get; set; }
        public string fuptime { get; set; }
        public string foplist { get; set; }
        public string fdrawentity_ftranid { get; set; }
        public string fdrawentity_fparenttranid { get; set; }
        public string fdrawentity_ftoptranid { get; set; }
        public string fdrawentity_fdataorigin { get; set; }
    }

    public class Ffileid
    {
        public string id { get; set; }
        public string name { get; set; }
    }


}
