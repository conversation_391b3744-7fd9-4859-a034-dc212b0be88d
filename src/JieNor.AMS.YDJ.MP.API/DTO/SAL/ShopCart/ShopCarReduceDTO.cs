using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.SAL.ShopCart
{
    /// <summary>
    /// 购物车清空接口
    /// </summary>
    [Api("购物车扣减接口")]
    [Route("/mpapi/shopcart/reduce")]
    [Authenticate]
    public class ShopCarReduceDTO: BaseDetailDTO
    {
        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; } = string.Empty;
        /// <summary>
        /// 商品ID
        /// </summary>
        public string ProductId { get; set; } = string.Empty;

        /// <summary>
        /// 仓库
        /// </summary>
        public string StoreHouseId { get; set; } = string.Empty;
        /// <summary>
        ///仓位
        /// </summary>
        public string StoreLocationId { get; set; } = string.Empty;
        /// <summary>
        /// 商品辅助属性
        /// </summary>
        public string attrinfo { get; set; } = string.Empty;
        /// <summary>
        /// 库存状态
        /// </summary>
        public string StockStatus { get; set; } = string.Empty;
        //辅助属性组合值

        public List<AuxPropObj> AuxPropVals { get; set; } = new List<AuxPropObj>();

    }

    public class AuxPropObj
    {
        public string AuxPropId { get; set; }


        public string ValueId { get; set; }
    }
}
