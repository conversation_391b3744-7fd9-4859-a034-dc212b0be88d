using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.DTO.BD.BillType
{
    //// <summary>
    /// 获取单据类型扩展参数接口
    /// </summary>
    [Api("获取单据类型扩展参数接口")]
    [Route("/mpapi/billtype/getuibilltypeparam")]
    [Authenticate]
    public class GetUiBillTypeParamDTO
    {
        /// <summary>
        /// 单据类型id
        /// </summary>
        public string BillTypeId { get; set; }
    }
}
