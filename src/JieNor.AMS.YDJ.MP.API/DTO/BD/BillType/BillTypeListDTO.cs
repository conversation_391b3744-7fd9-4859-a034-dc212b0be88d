using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 单据类型列表取数接口
    /// </summary>
    [Api("单据类型列表取数接口")]
    [Route("/mpapi/billtype/list")]
    [Authenticate]
    public class BillTypeListDTO
    {
        /// <summary>
        /// 表单标识
        /// </summary>
        public string FormId { get; set; }
    }
}