using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 客户编辑取数接口
    /// </summary>
    [Api("客户编辑取数接口")]
    [Route("/mpapi/customer/edit")]
    [Authenticate]
    public class CustomerEditDTO : BaseDetailDTO
    {
        /// <summary>
        /// 是否返回所有部门列表 0否  1是（默认0 不返回）
        /// </summary>
        public int IsReturnDepts { get; set; }
    }
}