using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 统计分析-员工列表取数接口
    /// </summary>
    [Api("员工列表取数接口")]
    [Route("/mpapi/report/stafflist")]
    [Authenticate]
    public class ReportStaffListDto : BaseListPageDTO
    {
        /// <summary>
        /// 条件类型 (本人，本部门，本企业)
        /// </summary>
        public string PermCaption { get; set; }
    }
}
