using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.DTO.BAS.CollectingUnit;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Model.BAS.CollectingUnit;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MP.API.Controller.BAS.CollectingUnit
{
    /// <summary>
    /// 微信小程序：会员版小程序参数取数接口
    /// </summary>
    public class CollectingUnitController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CollectingUnitDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<UnitListModel>();

            //代收单位
            resp.Data.CollectionUnit =this.Context.CollectionUnitList();
            resp.Message = "操作成功！";
            resp.Success = true;

            return resp;
        }

    }
}
