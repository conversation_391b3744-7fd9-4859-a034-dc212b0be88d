using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Response.YDJ.STE.Order.PushOrder;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order
{
    /// <summary>
    /// 微信小程序：合同单编辑取数接口
    /// </summary>
    public class OrderEditController : BaseController
    {
        public override string FormId { get; } = "ydj_order";

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(OrderEditDTO dto)
        {
            base.InitializeOperationContext(dto);

            return DoExecute(this.Context, dto);
        }

        /// <summary>
        /// 执行编辑接口逻辑
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        internal static BaseResponse<OrderEditModel> DoExecute(UserContext userCtx, OrderEditDTO dto)
        {
            var resp = new BaseResponse<OrderEditModel>();

            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(userCtx, "ydj_order");

            var expenseItemForm = metaModelService.LoadFormModel(userCtx, "ydj_expenseitem");
            var expenseItemsWhere = " (fmainorgid=@fmainorgid or fmainorgid=@ftopmainorgid) and fforbidstatus='0'";
            var expenseItems = expenseItemForm.GetBizDataByWhere(userCtx, expenseItemsWhere, new List<SqlParam>() {
                new SqlParam("@fmainorgid",System.Data.DbType.String,userCtx.Company),
                new SqlParam("@ftopmainorgid",System.Data.DbType.String,userCtx.TopCompanyId)
            });

            bool isNew = dto.Id.IsNullOrEmptyOrWhiteSpace();

            if (isNew)
            {
                // 默认值
                var currStaff = userCtx.GetCurrentStaff();
                resp.Data.Staff = new BaseDataSimpleModel
                {
                    Id = currStaff.Id,
                    Name = currStaff.Name,
                    Number = currStaff.Number
                };
                var currDept = userCtx.GetCurrentDept();
                if (currDept != null)
                {
                    resp.Data.Dept = new BaseDataSimpleModel
                    {
                        Id = currDept.Id,
                        Name = currDept.Name,
                        Number = currDept.Number
                    };
                }

                resp.Data.JoinStaffs.Add(new OrderJoinStaffModel
                {
                    Id = "",
                    Duty = resp.Data.Staff,
                    Ratio = 100,
                    Amount = resp.Data.SumAmount,
                    IsMain = true,
                    Dept = resp.Data.Dept,
                    DeptPerfRatio = 100
                });

                // 费用收入
                foreach (var expenseItem in expenseItems.Where(s => JNConvert.ToStringAndTrim(s["ftype"]) == "expensetype_01"))
                {
                    resp.Data.ExpenseItems.Add(new OrderExpenseItemModel
                    {
                        Item = new BaseDataSimpleModel(expenseItem)
                    });
                }

                // 费用支出
                foreach (var expenseItem in expenseItems.Where(s => JNConvert.ToStringAndTrim(s["ftype"]) == "expensetype_02"))
                {
                    resp.Data.DisburseItems.Add(new OrderExpenseItemModel
                    {
                        Item = new BaseDataSimpleModel(expenseItem)
                    });
                }

                resp.Data.DiscScale = 10;
                resp.Data.DistSumRate = 1;

                #region 单据类型

                InitBillType(userCtx, htmlForm, resp.Data);

                #endregion

            }
            else
            {
                var orderObj = htmlForm.GetBizDataById(userCtx, dto.Id, true);

                if (orderObj == null)
                {
                    resp.Message = "此合同不存在或已删除！";
                    resp.Success = false;
                    return resp;
                }
                else if (!orderObj["fbilltype"].IsNullOrEmpty() && orderObj["fbilltype"].Equals("ydj_order_vsix"))
                {
                    resp.Message = "对不起，当前订单为v6定制柜合同，暂不支持编辑！";
                    resp.Success = false;
                    return resp;
                }

                // 合同附件
                var image = Convert.ToString(orderObj["fimage"]);
                var imageTxt = Convert.ToString(orderObj["fimage_txt"]);
                var images = ImageFieldUtil.ParseImages(image, imageTxt, true);
                resp.Data.ImageList = images;
                resp.Data.Id = JNConvert.ToStringAndTrim(orderObj["id"]);
                resp.Data.Number = JNConvert.ToStringAndTrim(orderObj["fbillno"]);
                resp.Data.CreateDate = Convert.ToDateTime(orderObj["fcreatedate"]);
                resp.Data.DeliveryDate = Convert.ToDateTime(orderObj["fdeliverydate"]);
                resp.Data.OrderDate = Convert.ToDateTime(orderObj["forderdate"]);
                resp.Data.LogisticsItems = JNConvert.ToStringAndTrim(orderObj["flogisticsitems"]);
                resp.Data.Description = JNConvert.ToStringAndTrim(orderObj["fdescription"]);
                //需转单
                resp.Data.Needtransferorder = Convert.ToBoolean(orderObj["fneedtransferorder"]);
                //是否销售转单
                resp.Data.IsSaletransferorder = Convert.ToBoolean(orderObj["fissaletransferorder"]);
                resp.Data.IsSecOrder = Convert.ToBoolean(orderObj["fisresellorder"]);
                resp.Data.Within = JNConvert.ToStringAndTrim(orderObj["fwithin"]);
                resp.Data.SumSellAmount = Convert.ToDecimal(orderObj["fsumsellamount"]);
                resp.Data.CloseState = new ComboDataModel
                {
                    Id = Convert.ToString(orderObj["fclosestate"]),
                    Name = Convert.ToString(orderObj["fclosestate"]) == "1" ? "已关闭" : "未关闭"
                };
                resp.Data.Status = new ComboDataModel(orderObj["fstatus_ref"] as DynamicObject);
                var staffObj = orderObj["fstaffid_ref"] as DynamicObject;
                var deptObj = orderObj["fdeptid_ref"] as DynamicObject;
                var customerObj = orderObj["fcustomerid_ref"] as DynamicObject;
                var secCustomerObj = orderObj["fterminalcustomer_ref"] as DynamicObject;
                var productObjs = orderObj["fentry"] as DynamicObjectCollection;
                var designerObj = orderObj["fstylistid_ref"] as DynamicObject;
                var channelObj = orderObj["fchannel_ref"] as DynamicObject;
                var channeltypeObj = orderObj["fchanneltype_ref"] as DynamicObject;
                var buildingObj = orderObj["fbuildingid_ref"] as DynamicObject;
                var activityObj = orderObj["factivityid_ref"] as DynamicObject;
                if (resp.Data.IsSecOrder)
                {
                    resp.Data.MemberId = JNConvert.ToStringAndTrim(secCustomerObj?["fmemberno"]);
                }
                else
                {
                    resp.Data.MemberId = JNConvert.ToStringAndTrim(customerObj?["fmemberno"]);
                }
                resp.Data.Staff = new BaseDataSimpleModel(staffObj);
                resp.Data.Dept = new BaseDataSimpleModel(deptObj);
                resp.Data.Customer = new CustomerSimpleModel(customerObj);
                resp.Data.TerminalCustomer = new CustomerSimpleModel(secCustomerObj);


                resp.Data.Designer = new StaffListModel(designerObj);
                resp.Data.Channel = new
                {
                    Id = JNConvert.ToStringAndTrim(channelObj?["id"]),
                    Number = JNConvert.ToStringAndTrim(channelObj?["fnumber"]),
                    Name = JNConvert.ToStringAndTrim(channelObj?["fname"]),
                    Type = new
                    {
                        Id = JNConvert.ToStringAndTrim(channeltypeObj?["id"]),
                        Name = JNConvert.ToStringAndTrim(channeltypeObj?["fenumitem"]),
                    }
                };
                resp.Data.Building = new BaseDataSimpleModel(buildingObj);//楼盘
                resp.Data.Activity = new BaseDataSimpleModel(activityObj);//活动
                resp.Data.MemberDesc = JNConvert.ToStringAndTrim(orderObj["fmemberdesc"]);//未注册会员原因
                resp.Data.CustomerSource = new ComboDataModel(orderObj["fcustomersource_ref"] as DynamicObject);

                // 加载引用数据
                var refMg = userCtx.Container.GetService<LoadReferenceObjectManager>();
                var productForm = metaModelService.LoadFormModel(userCtx, "ydj_product");
                refMg.Load(userCtx, productForm.GetDynamicObjectType(userCtx), productObjs?.Select(s => s["fproductid_ref"] as DynamicObject), false);
                refMg.Load(userCtx, orderObj.GetDataEntityType(), orderObj, false);

                // 商品明细
                resp.Data.Products = OrderProductModel.Init(productObjs, htmlForm, userCtx);

                #region 判断商品能否编辑零售价
                var isresellorder = Convert.ToBoolean(orderObj["fisresellorder"]);
                //是自建商品，则获取经销商参数【自建商品零售价可编辑】判断
                var profileService = userCtx.Container.GetService<ISystemProfile>();
                var isorderusable = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fisorderusable", false);//参数【二级分销合同零售价可编辑】
                if (isresellorder && isorderusable)
                {
                    foreach (var item in resp.Data.Products)
                    {
                        item.IsEditPrice = true;
                    }
                }
                else
                {
                    var owneditprice = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fowneditprice", false);//参数【自建商品零售价可编辑】

                    var productIds = resp.Data.Products.Select(x => x.ProductId);
                    var sql = $@"select t1.fid,t1.fnumber,t1.fmainorgid,t2.feditprice from t_bd_material t1 with(nolock)
                        left join ser_ydj_category t2 with(nolock) on t1.fcategoryid=t2.fid
                        where t1.fid in ({productIds.JoinEx(",", true)})";
                    var dbService = userCtx.Container.GetService<IDBService>();
                    var productinfo = dbService.ExecuteDynamicObject(userCtx, sql);

                    foreach (var item in resp.Data.Products)
                    {
                        var result = productinfo.Where(x => Convert.ToString(x["fid"]) == item.ProductId).FirstOrDefault();
                        var fmainorgid = Convert.ToString(result["fmainorgid"]);//商品数据来源
                                                                                //判断商品是自建商品还是总部商品
                        if (fmainorgid == userCtx.Company)
                        {
                            if (owneditprice) item.IsEditPrice = true;
                        }
                        else
                        {
                            //是总部商品，则判断商品.商品类别.零售价可编辑=“是”
                            if (Convert.ToString(result["feditprice"]) == "1")
                            {
                                item.IsEditPrice = true;
                            }
                        }
                    }
                }
                #endregion


                // 源单
                resp.Data.SourceType = JNConvert.ToStringAndTrim(orderObj["fsourcetype"]);
                resp.Data.SourceNumber = JNConvert.ToStringAndTrim(orderObj["fsourcenumber"]);
                resp.Data.StrictTrack = Convert.ToBoolean(orderObj["fstricttrack"]);
                resp.Data.NeedTransferOrder = Convert.ToBoolean(orderObj["fneedtransferorder"]);
                //商场合同号
                resp.Data.FmallOrderno = JNConvert.ToStringAndTrim(orderObj["fmallorderno"]);
                resp.Data.RenewalFlag = Convert.ToBoolean(orderObj["frenewalflag"]);
                #region 金额相关
                resp.Data.DealAmount = Convert.ToDecimal(orderObj["fdealamount"]);
                //resp.Data.Unreceived = Convert.ToDecimal(orderObj["funreceived"]);
                resp.Data.Receivable = Convert.ToDecimal(orderObj["freceivable"]);
                resp.Data.SumAmount = Convert.ToDecimal(orderObj["fsumamount"]);
                resp.Data.ProductCost = Convert.ToDecimal(orderObj["fproductcost"]);
                //resp.Data.ExpenDiture = Convert.ToDecimal(orderObj["fexpenditure"]);
                //resp.Data.SumCost = Convert.ToDecimal(orderObj["fsumcost"]);
                //resp.Data.Profit = Convert.ToDecimal(orderObj["fprofit"]);
                //resp.Data.RefundAmount = Convert.ToDecimal(orderObj["frefundamount"]);
                //resp.Data.ActreFundAmount = Convert.ToDecimal(orderObj["factrefundamount"]);
                resp.Data.FaceAmount = Convert.ToDecimal(orderObj["ffaceamount"]);
                resp.Data.DistAmount = Convert.ToDecimal(orderObj["fdistamount"]);
                //resp.Data.DistSumAmount = Convert.ToDecimal(orderObj["fdistsumamount"]);
                resp.Data.DistSumRate = Convert.ToDecimal(orderObj["fdistsumrate"]);
                if (resp.Data.DistSumRate <= 0) resp.Data.DistSumRate = 1;
                //resp.Data.Expense = Convert.ToDecimal(orderObj["fexpense"]);
                resp.Data.SumReceivable = Convert.ToDecimal(orderObj["fsumreceivable"]);

                resp.Data.NoPromotionDistRate = Convert.ToDecimal(orderObj["fnopromotiondistrate"]);
                if (resp.Data.NoPromotionDistRate <= 0) resp.Data.NoPromotionDistRate = 1;

                #endregion

                #region 销售员
                var dutyObjs = orderObj["fdutyentry"] as DynamicObjectCollection;
                if (dutyObjs != null)
                {
                    foreach (var dutyObj in dutyObjs)
                    {
                        var obj = dutyObj["fdutyid_ref"] as DynamicObject;
                        var dept = dutyObj["fdeptid_ref"] as DynamicObject;
                        resp.Data.JoinStaffs.Add(new OrderJoinStaffModel
                        {
                            Id = JNConvert.ToStringAndTrim(dutyObj["id"]),
                            Duty = new BaseDataSimpleModel
                            {
                                Id = JNConvert.ToStringAndTrim(dutyObj["fdutyid"]),
                                Name = JNConvert.ToStringAndTrim(obj?["fname"]),
                                Number = JNConvert.ToStringAndTrim(obj?["fnumber"]),
                            },
                            Ratio = Convert.ToDecimal(dutyObj["fratio"]),
                            Amount = Convert.ToDecimal(dutyObj["famount"]),
                            Description = JNConvert.ToStringAndTrim(dutyObj["fdescription"]),
                            IsMain = Convert.ToBoolean(dutyObj["fismain"]),
                            Dept = new BaseDataSimpleModel()
                            {
                                Id = JNConvert.ToStringAndTrim(dutyObj["fdeptid"]),
                                Name = JNConvert.ToStringAndTrim(dept?["fname"]),
                                Number = JNConvert.ToStringAndTrim(dept?["fnumber"]),
                            },
                            DeptPerfRatio = Convert.ToDecimal(dutyObj["fdeptperfratio"])
                        });
                    }
                }
                #endregion

                #region 费用项目
                // 费用收入
                var expenseentryObjs = orderObj["fexpenseentry"] as DynamicObjectCollection;
                foreach (var expenseItem in expenseItems.Where(s => JNConvert.ToStringAndTrim(s["ftype"]) == "expensetype_01"))
                {
                    var expenseentryObj = expenseentryObjs.SingleOrDefault(s => JNConvert.ToStringAndTrim(s["fexpenseitemid"]) == JNConvert.ToStringAndTrim(expenseItem["id"]));
                    resp.Data.ExpenseItems.Add(new OrderExpenseItemModel
                    {
                        Item = new BaseDataSimpleModel(expenseItem),
                        Id = JNConvert.ToStringAndTrim(expenseentryObj?["id"]),
                        Amount = Convert.ToDecimal(expenseentryObj?["famount"])
                    });
                }

                // 费用支出
                var disburseentryObjs = orderObj["fdisburseentry"] as DynamicObjectCollection;
                foreach (var expenseItem in expenseItems.Where(s => JNConvert.ToStringAndTrim(s["ftype"]) == "expensetype_02"))
                {
                    var disburseentryObj = disburseentryObjs.SingleOrDefault(s => JNConvert.ToStringAndTrim(s["fexpenseitemid"]) == JNConvert.ToStringAndTrim(expenseItem["id"]));
                    resp.Data.DisburseItems.Add(new OrderExpenseItemModel
                    {
                        Item = new BaseDataSimpleModel(expenseItem),
                        Id = JNConvert.ToStringAndTrim(disburseentryObj?["id"]),
                        Amount = Convert.ToDecimal(disburseentryObj?["famount"])
                    });
                }
                #endregion

                #region 收货地址相关

                resp.Data.Province = new ComboDataModel(orderObj["fprovince_ref"] as DynamicObject);
                resp.Data.City = new ComboDataModel(orderObj["fcity_ref"] as DynamicObject);
                resp.Data.Region = new ComboDataModel(orderObj["fregion_ref"] as DynamicObject);
                resp.Data.Address = JNConvert.ToStringAndTrim(orderObj["faddress"]);
                resp.Data.District = JNConvert.ToStringAndTrim(htmlForm.GetDistrictFullText(userCtx, orderObj));
                resp.Data.CustomercontactId = JNConvert.ToStringAndTrim(orderObj["fcustomercontactid"]);
                resp.Data.Consignee = JNConvert.ToStringAndTrim((orderObj["fcustomercontactid_ref"] as DynamicObject)?["fcontacter"]);
                resp.Data.Phone = JNConvert.ToStringAndTrim(orderObj["fphone"]);

                #endregion

                #region 优惠设置

                resp.Data.IsRemoveTail = Convert.ToBoolean(orderObj["fisremovetail"]);
                resp.Data.FavorOpt = FavorOptToCombo(JNConvert.ToStringAndTrim(orderObj["ffavoropt"]));

                resp.Data.IsFixedPrice = Convert.ToBoolean(orderObj["fisfixedprice"]);

                #endregion

                #region 折扣设置

                resp.Data.IsWholeDis = Convert.ToBoolean(orderObj["fiswholedis"]);
                resp.Data.DiscScale = Convert.ToDecimal(orderObj["fdiscscale"]);
                if (resp.Data.DiscScale <= 0) resp.Data.DiscScale = 10;

                #endregion

                #region 单据类型

                resp.Data.BillType = new BaseDataSimpleModel(orderObj["fbilltype_ref"] as DynamicObject);

                #endregion

                #region 开票信息

                SetInvoiceInfo(orderObj, resp);

                #endregion

                #region Sap信息

                SetSapInfo(orderObj, resp);

                #endregion

                resp.Data.piecesendtag = Convert.ToBoolean(orderObj["fpiecesendtag"]);
                //model.relevanceorderno = Convert.ToString(orderObj["frelevanceorderno"]);

                resp.Data.relevanceorderno = new ComboDataModel
                {
                    Id = Convert.ToString(orderObj["frelevanceorderno"]),
                    Name = userCtx.LoadBizDataById("ydj_order", Convert.ToString(orderObj["frelevanceorderno"]))?["fbillno"]?.ToString()
                };
            }
            //根据单据类型处理销售部门默认值（创新新渠道逻辑）
            //DealDeptByBillType(userCtx, resp.Data);

            // 查询所有门店
            if (dto.SrcFormId.IsNullOrEmptyOrWhiteSpace())
            {
                dto.SrcFormId = htmlForm.Id;
            }
            //获取部门对应门店是否勾选新渠道
            resp.Data.ComboData = userCtx.GetDeptDataSource(dto);

            FilterDept(userCtx, resp.Data);

            resp.Data.ComboData.Merge(htmlForm.GetComboDataSource(userCtx, "fdeliverymode"));

            //单据类型  
            resp.Data.ComboData.Merge(BillUtil.GetBillTypeByBizobject(userCtx, "ydj_order"));

            if (isNew)
            {
                // 获取合同的辅助资料和简单下拉框,如果为新增，则隐藏v6定制柜合同对应单据类型
                resp.Data.ComboData.Merge(htmlForm.GetComboDataSource(userCtx, "fdeliverymode"));
                var billtypes = resp.Data.ComboData.Where(x => x.Key.EqualsIgnoreCase("billtype")).FirstOrDefault();
                if (!billtypes.IsNullOrEmpty())
                {
                    List<Dictionary<string, object>> tempData = new List<Dictionary<string, object>>();
                    foreach (var item in billtypes.Value)
                    {
                        var search = false;
                        foreach (var key in item.Keys)
                        {
                            if (key.EqualsIgnoreCase("id") && !item[key].IsNullOrEmpty() && item[key].Equals("ydj_order_vsix"))
                            {
                                billtypes.Value.Remove(item);
                                search = true;
                                break;
                            }
                        }
                        if (search)
                        {
                            break;
                        }
                    }
                }
            }

            SetOrderBillNoRule(userCtx, resp.Data);

            if (resp.Data.BillType != null && !resp.Data.BillType.Id.IsNullOrEmptyOrWhiteSpace())
            {
                var res = userCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(userCtx, "bd_billtype", null, "getuibillTypeparam", new Dictionary<string, object>() { { "billTypeId", resp.Data.BillType.Id } });


                if (res.IsSuccess)
                {
                    resp.Success = true;
                    resp.Data.BillType.ExtendParam = res.SrvData.ToJson();
                }
                else
                {
                    resp.Success = false;
                    resp.Data.BillType.ExtendParam = res.SrvData.ToJson();
                    resp.Message = $"获取单据类型扩展参数失败: " + res.SimpleMessage;
                    return resp;
                }

            }
            #region 直营销售合同赠品成交金额能不能为0

            SetGiveAwayParamIsNotZero(userCtx,resp);

            #endregion
            resp.Message = "操作成功！";
            resp.Success = true;
            return resp;
        }

        private static void DealDeptByBillType(UserContext userCtx, OrderEditModel model)
        {
            var billtype = model.BillType.Name;
            var dept = model.Dept.Id;
            //部门对应门店是否创新渠道
            bool Ischennal = IsChannelDept(userCtx, dept, billtype);
            //if (billtype == "大客户销售合同")
            //{
            //    if (!Ischennal)
            //    {
            //        model.Dept = new BaseDataSimpleModel
            //        {
            //            Id = "",
            //            Name = "",
            //            Number = ""
            //        };
            //    }
            //}
            //else
            if (billtype != "大客户销售合同" && billtype != "期初销售合同")
            {
                if (Ischennal)
                {
                    model.Dept = new BaseDataSimpleModel
                    {
                        Id = "",
                        Name = "",
                        Number = ""
                    };
                }
            }
        }
        /// <summary>
        /// 判断部门对应门店是否勾选创新渠道标记
        /// </summary>
        /// <param name="fdeptid"></param>
        /// <returns></returns>
        private static bool IsChannelDept(UserContext userCtx, string fdeptid, string billtype)
        {
            if (!fdeptid.IsNullOrEmptyOrWhiteSpace())
            {
                var sql = $@" SELECT 1 FROM t_bd_department with(nolock)
                            WHERE EXISTS (SELECT 1 FROM t_bas_store c with(nolock) WHERE fstore=c.fid AND ISNULL(fisnewchannel,0) =1) AND fid = '{fdeptid}'";
                var count = userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql).Count;
                //跟pc保持一致 3640，单据类型="大客户订单"时, 门店如果是经典的就不用清空
                if (count > 0)
                {
                    return true;
                }
                else if (billtype == "大客户销售合同")
                {
                    //判断部门是不是慕思经典的店门
                    sql = $@"select 1 from  t_bd_department with(nolock) where fid = '{fdeptid}' and exists(SELECT 1 FROM t_ydj_productauth t1 with (nolock) 
                            INNER JOIN t_ydj_productauthbs t2 with(nolock) ON t1.fid = t2.fid
                            inner join t_ydj_series t3 with(nolock) on CHARINDEX(t3.fid,t2.fserieid) > 0
                            where fstore = t1.forgid and CHARINDEX('慕思经典', t3.fname) > 0 AND t1.fforbidstatus = 0) ";
                    return userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql).Count > 0;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        //给部门列表添加过滤。
        private static void FilterDept(UserContext userCtx, OrderEditModel model)
        {
            var billtype = model.BillType.Name;
            List<Dictionary<string, object>> deptObj = null;
            model.ComboData.TryGetValue("fdept", out deptObj);
            List<string> deptList = deptObj.Select(o => Convert.ToString(o["id"])).ToList<string>();
            string sql = $@"  SELECT d.fid AS fdeptid  FROM t_bd_department d with(nolock) 
                             LEFT join t_bas_store c with(nolock) on d.fstore=c.fid /*fstoreid 为门店的编码*/
                             WHERE d.fid in ('{string.Join("','", deptList)}') AND c.fisnewchannel =1";
            //勾选了新零售的门店对应的部门
            List<string> result = userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql).Select(o => Convert.ToString(o["fdeptid"])).ToList();
            //if (billtype == "大客户销售合同")
            //{
            //    var InterList = result.Intersect(deptList).ToList();
            //    //取交集
            //    deptObj = deptObj.Where(o => InterList.Contains(o["id"].ToString())).ToList<Dictionary<string, object>>();
            //    model.ComboData["fdept"] = deptObj;
            //}
            //else 
            if (billtype != "大客户销售合同" && billtype != "期初销售合同")
            {
                //取差集 deptList存在 但是result不存在的 即取没勾选新零售的门店对应的部门 或者没有关联门店的部门
                var ExceptList = deptList.Except(result).ToList();

                deptObj = deptObj.Where(o => ExceptList.Contains(o["id"].ToString())).ToList<Dictionary<string, object>>();
                model.ComboData["fdept"] = deptObj;
            }
        }

        /// <summary>
        /// 初始化单据类型
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="model"></param>
        private static void InitBillType(UserContext userCtx, HtmlForm htmlForm, OrderEditModel model)
        {
            var billTypeService = userCtx.Container.GetService<IBillTypeService>();
            var billType = GetDefaultBillType(userCtx, billTypeService, htmlForm);
            if (billType != null)
            {
                model.BillType = new BaseDataSimpleModel(billType);

                var billTypeParam = GetDefaultBillTypeParam(userCtx, billTypeService, htmlForm, billType);
                if (billTypeParam != null)
                {
                    model.EnableNotice = Convert.ToBoolean(billTypeParam["fenablenotice"]);
                    model.IsApplyPur = Convert.ToBoolean(billTypeParam["fisapplypur"]);
                    model.StrictTrack = Convert.ToBoolean(billTypeParam["fstricttrack"]);

                    // 没有日期时，使用配置
                    if (!model.DeliveryDate.HasValue)
                    {
                        // 根据单据类型-销售合同-自定义参数，当<交货日期天数>配置为0时，不返回日期到前端
                        int deliveryDays = Convert.ToInt32(billTypeParam["fdeliverydays"]);
                        if (deliveryDays > 0)
                        {
                            model.DeliveryDate = BeiJingTime.Today.AddDays(deliveryDays);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取合同订单编码规则参数
        /// </summary>
        private static void SetOrderBillNoRule(UserContext userCtx, OrderEditModel model)
        {
            bool fbillreset = false;
            bool fisdefault = false;
            var sql = "select top 1  fid,fbillreset,fisdefault from t_bas_nosetting with(nolock) where fworkobject='ydj_order' and fstatus='E' order by fcreatedate desc";
            var res = userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql);
            if (res != null && res.Any())
            {
                fbillreset = Convert.ToString(res[0]["fbillreset"]) == "1" ? true : false;
                fisdefault = Convert.ToString(res[0]["fisdefault"]) == "1" ? true : false;
            }

            model.BillReset = fbillreset;
            model.IsDefault = fisdefault;
        }

        private static bool Isdoorderstatus(UserContext userCtx, string fsourceentryid_e)
        {
            var sql = $@"SELECT 1 FROM t_ydj_saleentry with(nolock) WHERE fentryid = '{fsourceentryid_e}' AND fdoorderstatus ='1'";
            //标识合同商品明细已成单
            if (userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql).Count > 0)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 通过下推生成
        /// </summary>
        /// <param name="resp"></param>
        /// <param name="userCtx"></param>
        internal static OrderEditModel Push(PushOrderResponse resp, UserContext userCtx)
        {
            OrderEditModel model = new OrderEditModel();

            model.DeliveryDate = resp.fdeliverydate.IsNullOrEmptyOrWhiteSpace() ? (DateTime?)null : Convert.ToDateTime(resp.fdeliverydate);
            model.OrderDate = resp.forderdate.IsNullOrEmptyOrWhiteSpace() ? BeiJingTime.Now : Convert.ToDateTime(resp.forderdate);
            model.LogisticsItems = resp.flogisticsitems;
            model.Description = resp.fdescription;

            #region 金额相关

            model.DealAmount = resp.fdealamount_h;
            //model.Unreceived = Convert.ToDecimal(orderObj["funreceived"]);
            model.Receivable = resp.freceivable;
            model.SumAmount = resp.fsumamount;
            model.ProductCost = resp.fproductcost;
            //model.ExpenDiture = Convert.ToDecimal(orderObj["fexpenditure"]);
            //model.SumCost = Convert.ToDecimal(orderObj["fsumcost"]);
            //model.Profit = Convert.ToDecimal(orderObj["fprofit"]);
            //model.RefundAmount = Convert.ToDecimal(orderObj["frefundamount"]);
            //model.ActreFundAmount = Convert.ToDecimal(orderObj["factrefundamount"]);
            model.FaceAmount = resp.ffaceamount;
            model.DistAmount = resp.fdistamount;
            //model.DistSumAmount = resp.fdistsumamount;
            model.DistSumRate = resp.fdistsumrate <= 0 ? 1 : resp.fdistsumrate;
            //model.Expense = Convert.ToDecimal(orderObj["fexpense"]);
            model.SumReceivable = resp.fsumreceivable;
            #endregion

            model.Staff = new BaseDataSimpleModel
            {
                Id = resp.fstaffid.id,
                Name = resp.fstaffid.fname,
                Number = resp.fstaffid.fnumber,
            };
            model.Dept = new BaseDataSimpleModel
            {
                Id = resp.fdeptid.id,
                Name = resp.fdeptid.fname,
                Number = resp.fdeptid.fnumber
            };
            model.Customer = new CustomerSimpleModel
            {
                Id = resp.fcustomerid.id,
                Name = resp.fcustomerid.fname,
                Number = resp.fcustomerid.fnumber,
                Phone = resp.fcustomerid.fphone,
            };
            model.Designer = new StaffListModel
            {
                Id = resp.fstylistid.id,
                Name = resp.fstylistid.fname,
            };
            model.Channel = new BaseDataSimpleModel
            {
                Id = resp.fchannel.id,
                Name = resp.fchannel.fname
            };
            model.Building = new BaseDataSimpleModel
            {
                Id = resp.fbuildingid.id,
                Name = resp.fbuildingid.fname
            };

            // 源单
            model.SourceType = resp.fsourcetype.id;
            model.SourceNumber = resp.fsourcenumber;

            #region 商品明细
            var productForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "ydj_product");
            foreach (var product in resp.fentry)
            {
                if (product.fproductid.id.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                //商品明细已成单则过滤掉
                if (Isdoorderstatus(userCtx, Convert.ToString(product.fsourceentryid_e)))
                {
                    continue;
                }

                OrderProductModel orderProductModel = new OrderProductModel
                {
                    Id = product.id,
                    Qty = Convert.ToInt32(product.fbizqty),
                    Amount = product.famount,
                    Price = product.fprice,
                    DealPrice = product.fdealprice,
                    DealAmount = product.fdealamount_e,
                    DistRate = product.fdistrate,
                    DistAmount = product.fdistamount_e,
                    IsOutSpot = product.fisoutspot,
                    Description = product.fdescription_e,
                    fsourcetype_e = product.fsourcetype_e.id,
                    fsourcenumber_e = product.fsourcenumber_e,
                    fsourceentryid_e = product.fsourceentryid_e,
                    ProductId = product.fproductid.id,
                    ProductName = product.fproductid.fname,
                    ProductNumber = product.fproductid.fnumber,
                    GuidePrice = product.fproductid.fguideprice,

                    ImageList = ImageFieldUtil.ParseImages(product.fmtrlimage.id, product.fmtrlimage.name, true),

                    CustomDesc = product.fcustomdes_e,
                };
                orderProductModel.IsSuite = product.fissuitflag;
                orderProductModel.SuitCombNumber = product.fsuitcombnumber;

                orderProductModel.DeliveryMode = new ComboDataModel
                {
                    Id = product.fdeliverymode.id,
                    Name = product.fdeliverymode.fname,
                };
                orderProductModel.StoreHouse = new ComboDataModel
                {
                    Id = product.fstorehouseid.id,
                    Name = product.fstorehouseid.fname
                };
                orderProductModel.StoreLocation = new ComboDataModel
                {
                    Id = product.fstorelocationid.id,
                    Name = product.fstorelocationid.fname
                };
                orderProductModel.Space = new ComboDataModel
                {
                    Id = product.fspace.id,
                    Name = product.fspace.fname
                };
                orderProductModel.StockStatus = new ComboDataModel
                {
                    Id = product.fstockstatus.id,
                    Name = product.fstockstatus.fname
                };

                var productObj = productForm.GetBizDataById(userCtx, orderProductModel.ProductId, true);

                var brandObj = productObj["fbrandid_ref"] as DynamicObject;
                orderProductModel.Brand.Id = Convert.ToString(brandObj?["id"]);
                orderProductModel.Brand.Name = Convert.ToString(brandObj?["fname"]);

                var seriesObj = productObj["fseriesid_ref"] as DynamicObject;
                orderProductModel.Series.Id = Convert.ToString(seriesObj?["id"]);
                orderProductModel.Series.Name = Convert.ToString(seriesObj?["fname"]);

                orderProductModel.AuxPropValId = product.fattrinfo.id;

                //var setNumer = product.fattrinfo.fnumber;
                //orderProductModel.AuxPropVals = ProductUtil.PackAuxPropFieldValue(setNumer);

                orderProductModel.IsClearStock = product.fisclearstock;
                model.Products.Add(orderProductModel);
            }

            //加载辅助属性信息
            var auxPropValIds = model.Products.Select(o => o.AuxPropValId).ToList();
            var auxPropValsKv = ProductUtil.PackAuxPropFieldValue(userCtx, auxPropValIds);
            foreach (var item in model.Products)
            {
                item.AuxPropVals = auxPropValsKv.GetValue(item.AuxPropValId) ?? new List<Dictionary<string, string>>();
            }

            #endregion

            #region 销售员信息（联合开单）
            foreach (var dutyeEtry in resp.fdutyentry)
            {
                string id = dutyeEtry.id;
                if (id.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                OrderJoinStaffModel orderJoinStaffModel = new OrderJoinStaffModel
                {
                    Id = id,
                    Ratio = dutyeEtry.fratio,
                    Amount = dutyeEtry.famount_ed,
                    Description = dutyeEtry.fdescription_ed,
                    IsMain = dutyeEtry.fismain,
                    DeptPerfRatio = dutyeEtry.fdeptperfratio
                };

                orderJoinStaffModel.Duty = new StaffSimpleModel
                {
                    Id = dutyeEtry.fdutyid.id,
                    Name = dutyeEtry.fdutyid.fname,
                    Number = dutyeEtry.fdutyid.fnumber
                };
                orderJoinStaffModel.Dept = new StaffSimpleModel
                {
                    Id = dutyeEtry.fdeptid.id,
                    Name = dutyeEtry.fdeptid.fname,
                    Number = dutyeEtry.fdeptid.fnumber
                };

                model.JoinStaffs.Add(orderJoinStaffModel);
            }

            if (!model.JoinStaffs.Any())
            {
                model.JoinStaffs.Add(new OrderJoinStaffModel
                {
                    IsMain = true,
                    Ratio = 100,
                    Amount = model.SumAmount,
                    Duty = model.Staff,
                    Dept = model.Dept,
                    DeptPerfRatio = 100
                });
            }
            #endregion

            #region 费用

            var expenseItemForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "ydj_expenseitem");
            var expenseItems = expenseItemForm.GetBizDataByWhere(userCtx, string.Empty, new List<SqlParam>());

            // 费用收入
            foreach (var expenseItem in expenseItems.Where(s => JNConvert.ToStringAndTrim(s["ftype"]) == "expensetype_01"))
            {
                model.ExpenseItems.Add(new OrderExpenseItemModel
                {
                    Item = new BaseDataSimpleModel(expenseItem)
                });
            }

            // 费用支出
            foreach (var expenseItem in expenseItems.Where(s => JNConvert.ToStringAndTrim(s["ftype"]) == "expensetype_02"))
            {
                model.DisburseItems.Add(new OrderExpenseItemModel
                {
                    Item = new BaseDataSimpleModel(expenseItem)
                });
            }

            #endregion

            #region 收货地址相关

            model.Province = new ComboDataModel
            {
                Id = resp.fprovince.id,
                Name = resp.fprovince.fenumitem,
            };
            model.City = new ComboDataModel
            {
                Id = resp.fcity.id,
                Name = resp.fcity.fenumitem,
            };
            model.Region = new ComboDataModel
            {
                Id = resp.fregion.id,
                Name = resp.fregion.fenumitem,
            };
            model.Address = JNConvert.ToStringAndTrim(resp.faddress);
            model.District = JNConvert.ToStringAndTrim(HtmlFormExtentions.GetDistrictText(model.Province.Name, model.City.Name, model.Region.Name, model.Address));

            model.Consignee = resp.flinkstaffid;
            model.Phone = resp.fphone;
            // 如果下推时没有手机号，就取客户上的手机号
            if (model.Phone.IsNullOrEmptyOrWhiteSpace())
            {
                model.Phone = model.Customer.Phone;
            }

            #endregion

            #region 优惠设置

            model.IsRemoveTail = resp.fisremovetail;
            model.FavorOpt = FavorOptToCombo(resp.ffavoropt.fnumber);
            model.IsFixedPrice = resp.fisfixedprice;

            #endregion

            #region 折扣设置

            model.IsWholeDis = resp.fiswholedis;
            model.DiscScale = resp.fdiscscale <= 0 ? 10 : resp.fdiscscale;

            #endregion

            #region 单据类型

            var orderForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "ydj_order");
            InitBillType(userCtx, orderForm, model);

            #endregion

            //获取编码规则参数
            SetOrderBillNoRule(userCtx, model);

            //根据单据类型处理销售部门默认值（创新新渠道逻辑）
            //DealDeptByBillType(userCtx, model);

            return model;
        }

        private static ComboDataModel FavorOptToCombo(string favorOpt)
        {
            ComboDataModel combo = new ComboDataModel();
            combo.Id = favorOpt;
            switch (combo.Id?.ToLowerInvariant())
            {
                case "favoropt_x": combo.Name = "小数"; break;
                case "favoropt_g": combo.Name = "个位"; break;
                case "favoropt_s": combo.Name = "十位"; break;
                case "favoropt_b": combo.Name = "百位"; break;
                case "favoropt_q": combo.Name = "千位"; break;
            }

            return combo;
        }

        /// <summary>
        /// 获取默认单据类型
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="billTypeService"></param>
        /// <param name="orderForm"></param>
        /// <returns></returns>
        private static DynamicObject GetDefaultBillType(UserContext userCtx, IBillTypeService billTypeService, HtmlForm orderForm)
        {
            var billTypeId = billTypeService.GetDefaultBillTypeId(userCtx, orderForm);
            if (billTypeId.IsNullOrEmptyOrWhiteSpace()) return null;
            return billTypeService.GetBillTypeById(userCtx, billTypeId);
        }

        /// <summary>
        /// 获取自定义参数
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="billTypeService"></param>
        /// <param name="orderForm"></param>
        /// <param name="billType"></param>
        /// <returns></returns>
        private static DynamicObject GetDefaultBillTypeParam(UserContext userCtx, IBillTypeService billTypeService, HtmlForm orderForm, DynamicObject billType)
        {
            if (billType == null) return null;

            string billTypeId = Convert.ToString(billType["id"]);

            return billTypeService.GetBillTypeParamSet(userCtx, orderForm, billTypeId);
        }

        /// <summary>
        /// 设置开票信息
        /// </summary>
        /// <param name="orderObj"></param>
        /// <param name="resp"></param>
        private static void SetInvoiceInfo(DynamicObject orderObj, BaseResponse<OrderEditModel> resp)
        {
            resp.Data.IsInvoiceNeed = Convert.ToBoolean(Convert.ToInt32(orderObj["fisinvoiceneed"]));
            if (resp.Data.InvoiceInfo == null)
            {
                resp.Data.InvoiceInfo = new InvoiceEntry();
            }

            resp.Data.InvoiceInfo.InvoiceType = JNConvert.ToStringAndTrim(orderObj["finvoicetype"]);
            resp.Data.InvoiceInfo.BuyerFullName = JNConvert.ToStringAndTrim(orderObj["fbuyerfullname"]);
            resp.Data.InvoiceInfo.TaxpayerIdentify = JNConvert.ToStringAndTrim(orderObj["ftaxpayeridentify"]);
            resp.Data.InvoiceInfo.InvoiceEmail = JNConvert.ToStringAndTrim(orderObj["finvoiceemail"]);
            resp.Data.InvoiceInfo.InvoiceAddress = JNConvert.ToStringAndTrim(orderObj["finvoiceaddress"]);
            resp.Data.InvoiceInfo.InvoicePhone = JNConvert.ToStringAndTrim(orderObj["finvoicephone"]);
            resp.Data.InvoiceInfo.DepositBankName = JNConvert.ToStringAndTrim(orderObj["fdepositbankname"]);
            resp.Data.InvoiceInfo.BankAccount = JNConvert.ToStringAndTrim(orderObj["fbankaccount"]);
        }

        private static void SetSapInfo(DynamicObject orderObj, BaseResponse<OrderEditModel> resp)
        {
            //提交SAP时间
            var fsubmithtime = Convert.ToDateTime(orderObj["fsubmithtime"]);

            if (fsubmithtime != null && fsubmithtime != default(DateTime))
            {
                resp.Data.SubmitHeadTime = fsubmithtime;
            }
            else
            {
                resp.Data.SubmitHeadTime = null;
            }

            //协同SAP状态
            resp.Data.SapStatus = JNConvert.ToStringAndTrim(orderObj["fchstatus"]);

            //SAP合同类型
            resp.Data.HeadContractType = JNConvert.ToStringAndTrim(orderObj["fheadcontracttype"]);

            //SAP合同号
            resp.Data.HeadQuartNo = JNConvert.ToStringAndTrim(orderObj["fheadquartno"]);

            //SAP终审时间
            var fheadquartfrtime = Convert.ToDateTime(orderObj["fheadquartfrtime"]);

            if (fheadquartfrtime != null && fheadquartfrtime != default(DateTime))
            {
                resp.Data.SapFinalTime = fheadquartfrtime;
            }
            else
            {
                resp.Data.SapFinalTime = null;
            }

            resp.Data.HeadQuartSyncMessage = JNConvert.ToStringAndTrim(orderObj["fheadquartsyncmessage"]);
        }

        /// <summary>
        /// 直营销售合同赠品成交金额能不能为0
        /// </summary>
        private static void SetGiveAwayParamIsNotZero(UserContext userCtx,BaseResponse<OrderEditModel> resp)
        {
            var companyId = userCtx.Company;

            var agentDy = userCtx.LoadBizBillHeadDataById("bas_agent", companyId, "fnumber,fdirectsalesgiveawaynotzero");
            if (agentDy != null)
            {
                resp.Data.IsDirectSaleGiveAwayIsNotZero = Convert.ToBoolean(Convert.ToInt32(agentDy["fdirectsalesgiveawaynotzero"]));
            }
        }
    }
}