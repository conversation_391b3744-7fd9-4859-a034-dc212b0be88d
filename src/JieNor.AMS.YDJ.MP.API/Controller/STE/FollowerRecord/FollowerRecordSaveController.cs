using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.FollowerRecord
{
    /// <summary>
    /// 微信小程序：跟进记录保存接口
    /// </summary>
    public class FollowerRecordSaveController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(FollowerRecordSaveDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            // 获取源单
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, dto.SourceType);
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            var data = dm.Select(dto.SourceId) as DynamicObject;

            if (data == null)
            {
                resp.Message = $"源单类型【{dto.SourceType}】对应的源单Id【{dto.SourceId}】的单据不存在";
                resp.Success = false;
                return resp;
            }

            dto.ObjectType = "objecttype01";          
            if ( dto.SourceType== "ydj_customer")
            {
                dto.SourceNumber = JNConvert.ToStringAndTrim(data["fnumber"]);
            }
            if (dto.SourceType == "ydj_customerrecord")
            {
                dto.SourceNumber = JNConvert.ToStringAndTrim(data["fbillno"]);
            }
            dto.TranId = JNConvert.ToStringAndTrim(data["ftranid"]);

            if(dto.SourceType == "ste_channel")
            {
                 dto.SourceNumber=JNConvert.ToStringAndTrim(data["fnumber"]);
            }
            if (dto.SourceType == "ydj_order")
            {
                dto.SourceNumber = JNConvert.ToStringAndTrim(data["fbillno"]);
            }
            if (dto.SourceType == "ydj_service")
            {
                dto.SourceNumber = JNConvert.ToStringAndTrim(data["fbillno"]);
            }
            return this.Context.AddFollowerRecord(this.Request, dto);
        }
    }
}