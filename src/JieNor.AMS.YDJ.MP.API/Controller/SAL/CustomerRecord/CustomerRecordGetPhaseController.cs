using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Config;
using ServiceStack;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.CustomerRecord
{
    /// <summary>
    /// 微信小程序：商机阶段取数接口
    /// </summary>
    public class CustomerRecordGetPhaseController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerRecordGetPhaseDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<CustomerRecordGetPhaseModel>
            {
                Data = new CustomerRecordGetPhaseModel()
            };

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data.Phases = ConfigExtentions.GetCustomerRecordPhases();

            return resp;
        }
    }
}