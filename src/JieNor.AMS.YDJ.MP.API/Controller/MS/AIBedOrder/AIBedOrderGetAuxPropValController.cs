using System;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MP.API.Controller.MS.AIBedOrder
{
    /// <summary>
    /// 床垫选配单获取辅助属性接口
    /// </summary>
    public class AIBedOrderGetAuxPropValController : BaseController
    {
        public override string FormId => "ms_aibedorder";

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(AIBedOrderGetAuxPropValDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            var aiBedOrders = this.Context.LoadBizDataById(this.FormId, dto.Ids);
            if (aiBedOrders == null || aiBedOrders.Count == 0)
            {
                resp.Success = false;
                resp.Message = "床垫选配单不存在！";
                return resp;
            }

            var muSiAiService = this.Container.GetService<IMuSiAIService>();

            var cids = muSiAiService.GetCIDs(this.Context, aiBedOrders);
            var auxPropInfos = muSiAiService.GetAuxPropVals(this.Context, aiBedOrders, cids);

            resp.Success = true;
            resp.Message = "取数成功！";
            resp.Data = auxPropInfos;

            return resp;
        }
    }
}