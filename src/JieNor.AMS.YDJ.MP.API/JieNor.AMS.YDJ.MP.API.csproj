<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{18E7268B-D122-4398-A796-************}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>JieNor.AMS.YDJ.MP.API</RootNamespace>
    <AssemblyName>JieNor.AMS.YDJ.MP.API</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\lib\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="JieNor.AMS.YDJ.Core, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.AMS.YDJ.Core.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.AMS.YDJ.DataTransferObject, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.AMS.YDJ.DataTransferObject.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.DataEntity, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.DataEntity.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.DataTransferObject, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.DataTransferObject.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.Interface, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.Interface.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.MetaCore, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.MetaCore.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.SuperOrm, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.SuperOrm.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Api.Swagger, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Api.Swagger.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Client, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Client.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Common, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Common.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Interfaces, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Text, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Text.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controller\AFT\Vist\VistDetailEditNoAuthController.cs" />
    <Compile Include="Controller\AFT\Vist\VistDetailEditController.cs" />
    <Compile Include="Controller\AFT\Vist\VistDetailListController.cs" />
    <Compile Include="Controller\AFT\Vist\VistDetailSaveNoAuthController.cs" />
    <Compile Include="Controller\AFT\Vist\VistDetailSaveController.cs" />
    <Compile Include="Controller\Auth\DataRangPermissionsController.cs" />
    <Compile Include="Controller\Auth\GetPDAVersionController.cs" />
    <Compile Include="Controller\Auth\GetPwdPolicyController.cs" />
    <Compile Include="Controller\Auth\UpdateUser.cs" />
    <Compile Include="Controller\BAS\FieldAstrictParamController.cs" />
    <Compile Include="Controller\BD\BillType\GetUiBillTypeParamController.cs" />
    <Compile Include="Controller\BD\Customer\CustomerChangeFieldController.cs" />
    <Compile Include="Controller\COO\IncomeDisburse\CheckSourceOrderController.cs" />
    <Compile Include="Controller\COO\IncomeDisburse\IncomeDisburseRefundController.cs" />
    <Compile Include="Controller\COO\IncomeDisburse\RefundController.cs" />
    <Compile Include="Controller\IM\NoticeList\MyNoticeListController.cs" />
    <Compile Include="Controller\IM\NoticeList\MyNoticeDetailController.cs" />
    <Compile Include="Controller\IM\NoticeList\MyNoticeSetReaderController.cs" />
    <Compile Include="Controller\STE\Channel\ChannelSubmitController.cs" />
    <Compile Include="Controller\STE\Order\CheckCanQueryParentInventoryController.cs" />
    <Compile Include="Controller\STE\Order\CheckProductDelistingController.cs" />
    <Compile Include="Controller\STE\Order\CheckWithInController.cs" />
    <Compile Include="Controller\STE\Order\GetOrderInvoiceInfoController.cs" />
    <Compile Include="Controller\STE\Order\OrderCheckStoreController.cs" />
    <Compile Include="Controller\STE\Order\OrderPushRefundController.cs" />
    <Compile Include="Controller\STE\Order\OrderSubmitHeadQuartController.cs" />
    <Compile Include="DTO\Auth\GetSignatureDTO.cs" />
    <Compile Include="Controller\BaseNotAuthController.cs" />
    <Compile Include="Controller\Common\ReportLocationController.cs" />
    <Compile Include="Controller\SER\Service\StockoutLinkSerController.cs" />
    <Compile Include="Controller\STE\Order\CheckOrderProductsController.cs" />
    <Compile Include="Controller\ValidataAgentController.cs" />
    <Compile Include="Controller\BAS\Activity\ActivityListController.cs" />
    <Compile Include="Controller\BAS\Agent\AgentListController.cs" />
    <Compile Include="Controller\BAS\City\CityListController.cs" />
    <Compile Include="Controller\BAS\CrmAgent\CrmAgentListController.cs" />
    <Compile Include="Controller\BAS\Deliver\DeliverListController.cs" />
    <Compile Include="Controller\BAS\MiniProgramController.cs" />
    <Compile Include="Controller\BAS\StoreHouse\StoreHouseListController.cs" />
    <Compile Include="Controller\BAS\StoreHouse\StoreHouseLocationListController.cs" />
    <Compile Include="Controller\BAS\Supplier\SupplierListController.cs" />
    <Compile Include="Controller\BD\Customer\CustomerPhoneChangeController.cs" />
    <Compile Include="Controller\BD\Customer\CustomerCheckPhoneController.cs" />
    <Compile Include="Controller\BD\Dept\CheckisnewchannelOrder.cs" />
    <Compile Include="Controller\BD\Dept\FuzzyQueryOrder.cs" />
    <Compile Include="Controller\Common\ReleaseLockController.cs" />
    <Compile Include="Controller\COO\IncomeDisburse\CheckInvoiceNumberController.cs" />
    <Compile Include="Controller\EWC\EwcConfigController.cs" />
    <Compile Include="Controller\EWC\GetSignatureController.cs" />
    <Compile Include="Controller\Grafana\AgentStatisticeController.cs" />
    <Compile Include="Controller\Grafana\UsCityStatisticsController.cs" />
    <Compile Include="Controller\MS\AIBedOrder\AIBedOrderListController.cs" />
    <Compile Include="Controller\MS\AIBedOrder\AIBedOrderGetAuxPropValController.cs" />
    <Compile Include="Controller\MS\AIBedOrder\AIBedOrderSaveController.cs" />
    <Compile Include="Controller\MS\AIBedOrder\AIBedOrderDetailController.cs" />
    <Compile Include="Controller\MS\AIBedOrder\AIBedOrderCancelController.cs" />
    <Compile Include="Controller\MS\AIBedSelectAdvise\AiBedSelectAdviseController.cs" />
    <Compile Include="Controller\MS\AIProduct\AiProductController.cs" />
    <Compile Include="Controller\MS\AIQuestion\AiQuestionController.cs" />
    <Compile Include="Controller\MS\CrmdistributorController.cs" />
    <Compile Include="Controller\Report\ReportCusRankController.cs" />
    <Compile Include="Controller\Report\ReportDeptListController.cs" />
    <Compile Include="Controller\Report\ReportOrderRankController.cs" />
    <Compile Include="Controller\Report\ReportPurRankController.cs" />
    <Compile Include="Controller\Report\ReportStaffListController.cs" />
    <Compile Include="Controller\Report\ReportStockRankController.cs" />
    <Compile Include="Controller\Report\ReportStoreHouseController.cs" />
    <Compile Include="Controller\SAL\Promotion\BasePromotionController.cs" />
    <Compile Include="Controller\SAL\Promotion\PromotionListController.cs" />
    <Compile Include="Controller\SAL\Promotion\PromotionDetailController.cs" />
    <Compile Include="Controller\SAL\Promotion\PromotionTabCountController.cs" />
    <Compile Include="Controller\SER\Service\SerGetAuthCityController.cs" />
    <Compile Include="Controller\SAL\DataAnalysis\RankingDeptListController.cs" />
    <Compile Include="Controller\SAL\DataAnalysis\RankingDeptController.cs" />
    <Compile Include="Controller\SER\Service\ServiceEditController.cs" />
    <Compile Include="Controller\SER\Service\ServiceItemListController.cs" />
    <Compile Include="Controller\SER\Service\ServiceOperateController.cs" />
    <Compile Include="Controller\SER\Service\ServicePushAfterFeedBackController.cs" />
    <Compile Include="Controller\SER\Service\ServiceSaveController.cs" />
    <Compile Include="Controller\STE\AfterFeedback\AfterFeedBackDetailController.cs" />
    <Compile Include="Controller\STE\AfterFeedback\AfterFeedBackEditController.cs" />
    <Compile Include="Controller\STE\AfterFeedback\AfterFeedBackListController.cs" />
    <Compile Include="Controller\STE\AfterFeedback\AfterFeedBackOperateController.cs" />
    <Compile Include="Controller\STE\AfterFeedback\AfterFeedBackPushServiceController.cs" />
    <Compile Include="Controller\STE\AfterFeedback\AfterFeedBackSaveController.cs" />
    <Compile Include="Controller\STE\AfterFeedback\AfterFeedGetCityController.cs" />
    <Compile Include="Controller\STE\AfterFeedback\AfterFeedGetHqNoController.cs" />
    <Compile Include="Controller\STE\AfterFeedback\FeedBackEngineerListController.cs" />
    <Compile Include="Controller\STE\AfterFeedback\FeedBackTabCountController.cs" />
    <Compile Include="Controller\STE\Dashboard\DashboardFeedBackController.cs" />
    <Compile Include="Controller\STE\Dashboard\DashboardServiceController.cs" />
    <Compile Include="Controller\STE\Order\OrderCheckMemberController.cs" />
    <Compile Include="Controller\STE\Order\Promotion\FillPromotionDataController.cs" />
    <Compile Include="Controller\STE\Order\Promotion\GetComboPromotionController.cs" />
    <Compile Include="Controller\STE\Order\Promotion\GetProductPromotionController.cs" />
    <Compile Include="Controller\SYS\DeleteRowController.cs" />
    <Compile Include="DTO\AFT\Vist\VistDetailListDTO.cs" />
    <Compile Include="DTO\AFT\Vist\VisteDetailEditNoAuthDTO.cs" />
    <Compile Include="DTO\AFT\Vist\VisteDetailEditDTO.cs" />
    <Compile Include="DTO\AFT\Vist\VisteDetailSaveNoAuthDTO.cs" />
    <Compile Include="DTO\AFT\Vist\VisteDetailSaveDTO.cs" />
    <Compile Include="DTO\ApplyBillLockDTO.cs" />
    <Compile Include="DTO\Auth\DataRangPermissionsDto.cs" />
    <Compile Include="DTO\Auth\GetPDAVersionDTO.cs" />
    <Compile Include="DTO\Auth\GetPwdPolicyDTO.cs" />
    <Compile Include="DTO\Auth\ModifyPwdByPcDTO.cs" />
    <Compile Include="DTO\BaseNotAuthDTO.cs" />
    <Compile Include="DTO\BAS\Activity\ActivityListDTO.cs" />
    <Compile Include="DTO\BAS\Agent\AgentListDTO.cs" />
    <Compile Include="DTO\BAS\City\CityListDTO.cs" />
    <Compile Include="DTO\BAS\CrmAgent\CrmAgentListDTO.cs" />
    <Compile Include="DTO\BAS\Deliver\DeliverListDTO.cs" />
    <Compile Include="DTO\BAS\FieldAstrictParamDTO.cs" />
    <Compile Include="DTO\BAS\MiniProgramDTO.cs" />
    <Compile Include="DTO\BAS\StoreHouse\StoreHouseListDTO.cs" />
    <Compile Include="DTO\BAS\StoreHouse\StoreHouseLocationListDTO.cs" />
    <Compile Include="DTO\BAS\Supplier\SupplierListDTO.cs" />
    <Compile Include="DTO\BD\BillType\GetUiBillTypeParamDTO.cs" />
    <Compile Include="DTO\BD\Customer\CustomerChangeFieldDTO.cs" />
    <Compile Include="DTO\BD\Customer\CustomerPhoneChangeDTO.cs" />
    <Compile Include="DTO\BD\Customer\CustomerCheckPhoneDTO.cs" />
    <Compile Include="DTO\BD\Dept\CheckisnewchannelDTO.cs" />
    <Compile Include="DTO\BD\Dept\FuzzyQueryOrderDTO.cs" />
    <Compile Include="DTO\CheckBillLockDTO.cs" />
    <Compile Include="DTO\Common\ReleaseLockDTO.cs" />
    <Compile Include="DTO\Common\ReportLocationDTO.cs" />
    <Compile Include="DTO\COO\IncomeDisburse\CheckInvoiceNumberDTO.cs" />
    <Compile Include="DTO\COO\IncomeDisburse\CheckSourceOrderDTO.cs" />
    <Compile Include="DTO\COO\IncomeDisburse\IncomeDisburseRefundDTO.cs" />
    <Compile Include="DTO\COO\IncomeDisburse\RefundDTO.cs" />
    <Compile Include="DTO\EWC\EwcConfigDTO.cs" />
    <Compile Include="DTO\EWC\EwcSendSignatureDTO.cs" />
    <Compile Include="DTO\Grafana\AgentStatisticeDto.cs" />
    <Compile Include="DTO\Grafana\UsCityStatisticsDTO.cs" />
    <Compile Include="DTO\IM\NoticeList\MyNoticeListDTO.cs" />
    <Compile Include="DTO\IM\NoticeList\MyNoticeDetailDTO.cs" />
    <Compile Include="DTO\IM\NoticeList\MyNoticeSetReaderDTO.cs" />
    <Compile Include="DTO\MS\AIBedOrder\AIBedOrderCancelDTO.cs" />
    <Compile Include="DTO\MS\AIBedOrder\AIBedOrderGetAuxPropValDTO.cs" />
    <Compile Include="DTO\MS\AIBedOrder\AIBedOrderSaveDTO.cs" />
    <Compile Include="DTO\MS\AIBedOrder\AIBedOrderDetailDTO.cs" />
    <Compile Include="DTO\MS\AIBedOrder\AIBedOrderListDTO.cs" />
    <Compile Include="DTO\MS\AIBedSelectAdvise\AiSelectAdviseDTO.cs" />
    <Compile Include="DTO\MS\AIProduct\AiProductDTO.cs" />
    <Compile Include="DTO\MS\AIQuestion\AiQuestionDTO.cs" />
    <Compile Include="DTO\MS\CrmdistributorDTO.cs" />
    <Compile Include="DTO\Report\ReportCusRankDTO.cs" />
    <Compile Include="DTO\Report\ReportDeptListDTO.cs" />
    <Compile Include="DTO\Report\ReportOrderRankDTO.cs" />
    <Compile Include="DTO\Report\ReportPurRankDTO.cs" />
    <Compile Include="DTO\Report\ReportStaffListDto.cs" />
    <Compile Include="DTO\Report\ReportStockRankDTO.cs" />
    <Compile Include="DTO\Report\ReportStoreHouseDto.cs" />
    <Compile Include="DTO\SAL\Promotion\PromotionDetailDTO.cs" />
    <Compile Include="DTO\SAL\Promotion\PromotionTabCountDTO.cs" />
    <Compile Include="DTO\SAL\Promotion\PromotionListDTO.cs" />
    <Compile Include="DTO\SER\Service\StockoutLinkSerDTO.cs" />
    <Compile Include="DTO\SER\Service\SerGetAuthCityDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceDetailDTO.cs" />
    <Compile Include="DTO\SAL\DataAnalysis\RankingDeptDto.cs" />
    <Compile Include="DTO\SAL\DataAnalysis\RankingDeptListDto.cs" />
    <Compile Include="DTO\SER\Service\ServiceEditDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceItemListDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceOperateDTO.cs" />
    <Compile Include="DTO\SER\Service\ServicePushAfterFeedBackDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceSaveDTO.cs" />
    <Compile Include="DTO\STE\AfterFeedback\AfterFeedBackDetailDTO.cs" />
    <Compile Include="DTO\STE\AfterFeedback\AfterFeedBackEditDTO.cs" />
    <Compile Include="DTO\STE\AfterFeedback\AfterFeedBackListDTO.cs" />
    <Compile Include="DTO\STE\AfterFeedback\AfterFeedBackOperateDTO.cs" />
    <Compile Include="DTO\STE\AfterFeedback\AfterFeedBackPushServiceDTO.cs" />
    <Compile Include="DTO\STE\AfterFeedback\AfterFeedBackSaveDTO.cs" />
    <Compile Include="DTO\STE\AfterFeedback\AfterFeedGetCityDTO.cs" />
    <Compile Include="DTO\STE\AfterFeedback\AfterFeedGetHqNoDTO.cs" />
    <Compile Include="DTO\STE\AfterFeedback\FeedBackEngineerListDTO.cs" />
    <Compile Include="DTO\STE\AfterFeedback\FeedBackTabCountDTO.cs" />
    <Compile Include="DTO\STE\Channel\ChannelSubmitDTO.cs" />
    <Compile Include="DTO\STE\Order\CheckCanQueryParentInventoryDTO.cs" />
    <Compile Include="DTO\STE\Order\CheckOrderProductsDto.cs" />
    <Compile Include="DTO\STE\Order\CheckWithInDto.cs" />
    <Compile Include="DTO\STE\Order\GetOrderInvoiceInfoDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderCheckMemberDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderCheckStoreDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderPushRefundDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderSubmitHeadQuartDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderUnSubmitchangeDTO.cs" />
    <Compile Include="DTO\STE\Order\ProductDelistingDTO.cs" />
    <Compile Include="DTO\STE\Order\Promotion\FillPromotionDataDTO.cs" />
    <Compile Include="DTO\STE\Order\Promotion\GetComboPromotionDTO.cs" />
    <Compile Include="DTO\STE\Order\Promotion\GetProductPromotionDTO.cs" />
    <Compile Include="DTO\STK\DashBoard\DashboardFeedBackDTO.cs" />
    <Compile Include="DTO\STK\DashBoard\DashboardServiceDTO.cs" />
    <Compile Include="DTO\SYS\DeleteRowDTO.cs" />
    <Compile Include="Filter\BaseResponseFilter.cs" />
    <Compile Include="Filter\BillLock\ApplyBillLockFilter.cs" />
    <Compile Include="Filter\BillLock\CheckBillLockFilter.cs" />
    <Compile Include="Model\AFT\Vist\VistDetailEditModel.cs" />
    <Compile Include="Model\AFT\Vist\VistDetailListModel.cs" />
    <Compile Include="Model\Auth\DataRangPermissionsModel.cs" />
    <Compile Include="Model\Auth\GetPwdPolicyModel.cs" />
    <Compile Include="Model\BAS\Activity\ActivityListModel.cs" />
    <Compile Include="Model\BAS\Agent\AgentListModel.cs" />
    <Compile Include="Model\BAS\City\CityListModel.cs" />
    <Compile Include="Model\BAS\CrmAgent\CrmAgentListModel.cs" />
    <Compile Include="Model\BAS\Deliver\DeliverListModel.cs" />
    <Compile Include="Model\BAS\StoreHouse\StoreHouseListModel.cs" />
    <Compile Include="Model\BAS\StoreHouse\StoreHouseLocationListModel.cs" />
    <Compile Include="Model\BAS\Supplier\SupplierListModel.cs" />
    <Compile Include="Model\DashBoard\DashboardServiceModel.cs" />
    <Compile Include="Model\IM\NoticeList\MyNoticeListModel.cs" />
    <Compile Include="Model\IM\NoticeList\MyNoticeDetailModel.cs" />
    <Compile Include="Model\MS\AIBedOrder\AIBedOrderDetailModel.cs" />
    <Compile Include="Model\MS\AIProduct\AiProductModel.cs" />
    <Compile Include="Model\MS\AIProduct\AiProductsModel.cs" />
    <Compile Include="Model\MS\AISelectAdvise\AiSelectAdviseModel.cs" />
    <Compile Include="Model\MS\CrmdistributorModel.cs" />
    <Compile Include="Model\Report\ReportDeptListModel.cs" />
    <Compile Include="Model\Report\ReportSalesInfoModel.cs" />
    <Compile Include="Model\Report\ReportStaffListModel.cs" />
    <Compile Include="Model\SAL\DataAnalysis\RankingDeptModel.cs" />
    <Compile Include="Model\SER\Service\ServiceEditModel.cs" />
    <Compile Include="Model\SER\Service\ServiceItemListModel.cs" />
    <Compile Include="Model\SER\Service\ServiceReserveModel.cs" />
    <Compile Include="Model\STE\AfterFeedback\AfterFeedBackDetailModel.cs" />
    <Compile Include="Model\STE\AfterFeedback\AfterFeedBackEditModel.cs" />
    <Compile Include="Model\STE\AfterFeedback\AfterFeedBackListModel.cs" />
    <Compile Include="Model\STE\Dashboard\ServiceCountModel.cs" />
    <Compile Include="Model\STE\AfterFeedback\FeedBackEngineerListModel.cs" />
    <Compile Include="Model\STE\Order\OrderCheckStoremModel.cs" />
    <Compile Include="Model\STE\Order\OrderPushRefundModel.cs" />
    <Compile Include="Response\PDA\PdaResponse.cs" />
    <Compile Include="SimpleSSODTO.cs" />
    <Compile Include="Config\CustomerRecordPhaseConfig.cs" />
    <Compile Include="Controller\Assist\AllComboListController.cs" />
    <Compile Include="Controller\Assist\ComboListController.cs" />
    <Compile Include="Controller\Assist\AssistListController.cs" />
    <Compile Include="Controller\Auth\HasPermissionsController.cs" />
    <Compile Include="Controller\Auth\AuthMenuListController.cs" />
    <Compile Include="Controller\Auth\ToggleCompanyController.cs" />
    <Compile Include="Controller\Auth\UnBindUserController.cs" />
    <Compile Include="Controller\Auth\CheckUserController.cs" />
    <Compile Include="Controller\Auth\LogoutController.cs" />
    <Compile Include="Controller\Auth\LoginController.cs" />
    <Compile Include="Controller\BaseController.cs" />
    <Compile Include="Controller\BAS\AppletParamController.cs" />
    <Compile Include="Controller\BAS\Case\BaseCaseController.cs" />
    <Compile Include="Controller\BAS\Case\CaseAddToCustomerController.cs" />
    <Compile Include="Controller\BAS\Case\CaseCopyController.cs" />
    <Compile Include="Controller\BAS\Case\CaseDeleteController.cs" />
    <Compile Include="Controller\BAS\Case\CaseDetailController.cs" />
    <Compile Include="Controller\BAS\Case\CaseEditController.cs" />
    <Compile Include="Controller\BAS\Case\CaseListController.cs" />
    <Compile Include="Controller\BAS\Case\CaseMycaseListController.cs" />
    <Compile Include="Controller\BAS\Case\CaseSaveController.cs" />
    <Compile Include="Controller\BAS\Case\CaseUnCopyController.cs" />
    <Compile Include="Controller\BAS\CollectingUnit\CollectingUnitController.cs" />
    <Compile Include="Controller\BAS\EnumDataListController.cs" />
    <Compile Include="Controller\BAS\GetCurrAreaController.cs" />
    <Compile Include="Controller\BAS\MulClassType\MulClassTypeDataListController.cs" />
    <Compile Include="Controller\BAS\MulClassType\MulClassTypeListController.cs" />
    <Compile Include="Controller\BAS\StockSysParamController.cs" />
    <Compile Include="Controller\BAS\StoreSysParamController.cs" />
    <Compile Include="Controller\BAS\Building\BuildingListController.cs" />
    <Compile Include="Controller\BAS\Building\BuildingDeleteController.cs" />
    <Compile Include="Controller\BAS\Building\BuildingDetailController.cs" />
    <Compile Include="Controller\BAS\Building\BuildingEditController.cs" />
    <Compile Include="Controller\BAS\Building\BuildingSaveController.cs" />
    <Compile Include="Controller\BD\BillType\BillTypeListController.cs" />
    <Compile Include="Controller\BD\Brand\BrandListController.cs" />
    <Compile Include="Controller\BD\Category\TreeListController.cs" />
    <Compile Include="Controller\BD\Customer\Consumption\ConsumptionListController.cs" />
    <Compile Include="Controller\BD\Customer\CustomerCheckMergeController.cs" />
    <Compile Include="Controller\BD\Customer\AddressListController.cs" />
    <Compile Include="Controller\BD\Customer\CustomerMergeController.cs" />
    <Compile Include="Controller\BD\Customer\Duty\CustomerDutyRemoveController.cs" />
    <Compile Include="Controller\BD\Customer\Duty\CustomerDutyReplaceController.cs" />
    <Compile Include="Controller\BD\Customer\Duty\CustomerDutyAddController.cs" />
    <Compile Include="Controller\BD\Customer\Contacts\CustomerContactsDeleteController.cs" />
    <Compile Include="Controller\BD\Customer\Contacts\CustomerContactsSaveController.cs" />
    <Compile Include="Controller\BD\Customer\Contacts\CustomerContactsEditController.cs" />
    <Compile Include="Controller\BD\Customer\Contacts\CustomerContactsListController.cs" />
    <Compile Include="Controller\BD\Customer\CustomerMatchController.cs" />
    <Compile Include="Controller\BD\Customer\CustomerValidateController.cs" />
    <Compile Include="Controller\BD\Customer\Image\CustomerImageListController.cs" />
    <Compile Include="Controller\BD\Dept\DeptListController.cs" />
    <Compile Include="Controller\BD\Position\PositionAllController.cs" />
    <Compile Include="Controller\BD\Product\ProductClearStockAuxPropController.cs" />
    <Compile Include="Controller\BD\Product\ProductAuxPropController.cs" />
    <Compile Include="Controller\BD\Product\ProductPropCheckController.cs" />
    <Compile Include="Controller\BD\Product\ProductPropController.cs" />
    <Compile Include="Controller\BD\Product\ProductStandardMapController.cs" />
    <Compile Include="Controller\BD\Product\ProductStockProfileController.cs" />
    <Compile Include="Controller\BD\Product\ProductSubAuxPropController.cs" />
    <Compile Include="Controller\BD\Product\ProductProfileController.cs" />
    <Compile Include="Controller\BD\Product\ProductSaleDetailController.cs" />
    <Compile Include="Controller\BD\Product\ProductScanCodeController.cs" />
    <Compile Include="Controller\BD\Product\ProductStockDetailController.cs" />
    <Compile Include="Controller\BD\Series\SeriesListController.cs" />
    <Compile Include="Controller\BD\Staff\BaseStaffController.cs" />
    <Compile Include="Controller\BD\Staff\StaffDetailController.cs" />
    <Compile Include="Controller\BD\Staff\StaffFollowUpCustomerController.cs" />
    <Compile Include="Controller\BD\Staff\StaffHandoverController.cs" />
    <Compile Include="Controller\BPM\Approval\ApprovalListController.cs" />
    <Compile Include="Controller\BPM\Approval\ApprovalRejectController.cs" />
    <Compile Include="Controller\BPM\Approval\ApprovalDetailController.cs" />
    <Compile Include="Controller\BPM\Approval\ApprovalAuditController.cs" />
    <Compile Include="Controller\Common\Duty\DutyAddController.cs" />
    <Compile Include="Controller\Common\Duty\DutyRemoveController.cs" />
    <Compile Include="Controller\Common\Duty\DutyReplaceController.cs" />
    <Compile Include="Controller\Common\Print\PrintController.cs" />
    <Compile Include="Controller\Common\Print\PrintTemplateListController.cs" />
    <Compile Include="Controller\Common\Share\ShareShopPosterController.cs" />
    <Compile Include="Controller\Common\DownloadFileController.cs" />
    <Compile Include="Controller\Common\UploadFileController.cs" />
    <Compile Include="Controller\COO\IncomeDisburse\IncomeDisburseListController.cs" />
    <Compile Include="Controller\COO\IncomeDisburse\IncomeDisburseReceiptController.cs" />
    <Compile Include="Controller\COO\IncomeDisburse\RechargeController.cs" />
    <Compile Include="Controller\COO\Receipt\ReceiptSaveController.cs" />
    <Compile Include="Controller\COO\Receipt\ReceiptUnSubmitController.cs" />
    <Compile Include="Controller\COO\Receipt\ReceiptSubmitController.cs" />
    <Compile Include="Controller\COO\Receipt\ReceiptUnAuditController.cs" />
    <Compile Include="Controller\COO\Receipt\ReceiptAuditController.cs" />
    <Compile Include="Controller\COO\Receipt\ReceiptConfirmController.cs" />
    <Compile Include="Controller\COO\Receipt\ReceiptDeleteController.cs" />
    <Compile Include="Controller\COO\Receipt\ReceiptDetailController.cs" />
    <Compile Include="Controller\COO\Receipt\ReceiptEditController.cs" />
    <Compile Include="Controller\COO\Receipt\ReceiptListController.cs" />
    <Compile Include="Controller\IM\Notice\NoticeDetailController.cs" />
    <Compile Include="Controller\IM\Notice\NoticeListController.cs" />
    <Compile Include="Controller\IM\Notice\NoticeSetReaderController.cs" />
    <Compile Include="Controller\SAL\DataAnalysis\FunnelSalesStatisticsController.cs" />
    <Compile Include="Controller\SAL\DataAnalysis\RankingEmployeesController.cs" />
    <Compile Include="Controller\SAL\DataAnalysis\RankingEmployeesListController.cs" />
    <Compile Include="Controller\SAL\DataAnalysis\SalesTrendController.cs" />
    <Compile Include="Controller\SAL\DataAnalysis\StatisticAnalysisBulletinController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\PayAccountDataController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\PaymentTypeController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionRejectController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionTerminateController.cs" />
    <Compile Include="Controller\SAL\ShopCart\ShopCarReduceController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordReplaceDutyController.cs" />
    <Compile Include="Controller\SAL\ShopCart\ShopCartBatchSaveController.cs" />
    <Compile Include="Controller\SAL\ShopCart\ShopCartUpdateQtyController.cs" />
    <Compile Include="Controller\SEL\Parts\CombineController.cs" />
    <Compile Include="Controller\SEL\PropValue\PropValueLoadOrCreateController.cs" />
    <Compile Include="Controller\SEL\Suite\SuiteController.cs" />
    <Compile Include="Controller\SER\Service\BaseServiceController.cs" />
    <Compile Include="Controller\SER\Service\BaseServiceFeedController.cs" />
    <Compile Include="Controller\SER\Service\ServiceCategoryListController.cs" />
    <Compile Include="Controller\SER\Service\ServiceChangeController.cs" />
    <Compile Include="Controller\SER\Service\ServiceChangeListController.cs" />
    <Compile Include="Controller\SER\Service\ServiceMasterListController.cs" />
    <Compile Include="Controller\SER\Service\ServiceRefuseController.cs" />
    <Compile Include="Controller\SER\Service\ServiceReceiveController.cs" />
    <Compile Include="Controller\SER\Service\ServiceReportController.cs" />
    <Compile Include="Controller\SER\Service\ServiceReportDetailController.cs" />
    <Compile Include="Controller\SER\Service\ServiceReservationListController.cs" />
    <Compile Include="Controller\SER\Service\ServiceReservationSaveController.cs" />
    <Compile Include="Controller\SER\Service\ServiceTabCountController.cs" />
    <Compile Include="Controller\SER\Service\ServiceDetailController.cs" />
    <Compile Include="Controller\SER\Service\ServiceFeedDetailController.cs" />
    <Compile Include="Controller\SER\Service\ServiceFeedListController.cs" />
    <Compile Include="Controller\SER\Service\ServiceFeedSaveController.cs" />
    <Compile Include="Controller\SER\Service\ServiceListController.cs" />
    <Compile Include="Controller\SER\Service\ServiceTransOrderController.cs" />
    <Compile Include="Controller\STE\Channel\ChannelDetailDataController.cs" />
    <Compile Include="Controller\STE\Dashboard\DashboardSaleGoalController.cs" />
    <Compile Include="Controller\STE\Dashboard\DashboardSaleAchievementController.cs" />
    <Compile Include="Controller\Report\ReportSaleRankController.cs" />
    <Compile Include="Controller\Report\ReportSaleStatController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\BaseCustomerRecordController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordFinishController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordReceiveController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordClaimController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordCloseController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordTabCountController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordPhaseCountController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordPushPhaseController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordGetPhaseController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordPushSaleIntentionController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordPushOrderController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordEditController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordValidateController.cs" />
    <Compile Include="Controller\BD\Customer\CustomerDetailController.cs" />
    <Compile Include="Controller\BD\Customer\CustomerEditController.cs" />
    <Compile Include="Controller\BD\Customer\CustomerListController.cs" />
    <Compile Include="Controller\BD\Customer\CustomerSaveController.cs" />
    <Compile Include="Controller\BD\Product\ProductDetailController.cs" />
    <Compile Include="Controller\BD\Product\ProductListController.cs" />
    <Compile Include="Controller\BD\Staff\StaffListController.cs" />
    <Compile Include="Controller\EWC\EwcReceiveMsgController.cs" />
    <Compile Include="Controller\Demo\DemoController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\BaseSaleIntentionController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionTabCountController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionPushReceiptController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionPushOrderController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionAuditController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionDeleteController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionSubmitController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionListController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionArrangeMeasurerController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionArrangeDesignerController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionDetailController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionEditController.cs" />
    <Compile Include="Controller\SAL\SaleIntention\SaleIntentionSaveController.cs" />
    <Compile Include="Controller\SAL\ShopCart\ShopCartClearController.cs" />
    <Compile Include="Controller\SAL\ShopCart\ShopCartDeleteController.cs" />
    <Compile Include="Controller\SAL\ShopCart\ShopCartListController.cs" />
    <Compile Include="Controller\SAL\ShopCart\ShopCartSaveController.cs" />
    <Compile Include="Controller\STE\Channel\ChannelDeleteController.cs" />
    <Compile Include="Controller\STE\Channel\ChannelDetailController.cs" />
    <Compile Include="Controller\STE\Channel\ChannelEditController.cs" />
    <Compile Include="Controller\STE\Channel\ChannelListController.cs" />
    <Compile Include="Controller\STE\Channel\ChannelSaveController.cs" />
    <Compile Include="Controller\STE\Dashboard\DashboardTaskController.cs" />
    <Compile Include="Controller\STE\Order\BaseOrderController.cs" />
    <Compile Include="Controller\STE\Order\Attachment\OrderAttachmentAddController.cs" />
    <Compile Include="Controller\STE\Order\Attachment\OrderAttachmentDeleteController.cs" />
    <Compile Include="Controller\STE\Order\OrderUnstdAuditController.cs" />
    <Compile Include="Controller\STE\Order\ChangeBillTypeDeptListController.cs" />
    <Compile Include="Controller\STE\Order\ResultBrandListController.cs" />
    <Compile Include="Controller\STE\Order\IsAllowOrdersController.cs" />
    <Compile Include="Controller\STE\Order\OrderChangeController.cs" />
    <Compile Include="Controller\STE\Order\OrderRejectController.cs" />
    <Compile Include="Controller\STE\Order\OrderDetailController.cs" />
    <Compile Include="Controller\STE\Order\OrderEditController.cs" />
    <Compile Include="Controller\STE\Order\OrderSaveController.cs" />
    <Compile Include="Controller\STE\Order\OrderListController.cs" />
    <Compile Include="Controller\STE\Order\OrderAuditController.cs" />
    <Compile Include="Controller\STE\Order\OrderDeleteController.cs" />
    <Compile Include="Controller\STE\Order\OrderPushReceiptController.cs" />
    <Compile Include="Controller\STE\Order\OrderSubmitChangeController.cs" />
    <Compile Include="Controller\STE\Order\OrderSubmitController.cs" />
    <Compile Include="Controller\STE\Order\OrderTabCountController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordShareCardController.cs" />
    <Compile Include="Controller\STE\Order\OrderTerminateController.cs" />
    <Compile Include="Controller\STE\Order\OrderUnChangeController.cs" />
    <Compile Include="Controller\STK\ReserveBill\ReserveBillMatchQtyByIdController.cs" />
    <Compile Include="Controller\STK\ReserveBill\ReserveBillMatchQtyController.cs" />
    <Compile Include="Controller\STK\ReserveBill\ReserveBillSourceEditController.cs" />
    <Compile Include="Controller\STK\ReserveBill\ReserveBillSourceReleaseController.cs" />
    <Compile Include="Controller\STK\ReserveBill\ReserveBillSourceSaveController.cs" />
    <Compile Include="Controller\STK\Rpt\StockSynthesizeSummaryController.cs" />
    <Compile Include="Controller\STK\Rpt\StockSynthesizeListController.cs" />
    <Compile Include="Controller\STK\Stockstatus\StockstatusListControlle.cs" />
    <Compile Include="Controller\SYS\VersionInfoController.cs" />
    <Compile Include="Controller\User\ModifyPwdController.cs" />
    <Compile Include="Controller\User\MyCardController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordSaveController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordDeleteController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordDetailController.cs" />
    <Compile Include="Controller\SAL\CustomerRecord\CustomerRecordListController.cs" />
    <Compile Include="Controller\STE\FollowerRecord\FollowerRecordSaveController.cs" />
    <Compile Include="Controller\STE\FollowerRecord\FollowerRecordListController.cs" />
    <Compile Include="Controller\User\MyCompanyController.cs" />
    <Compile Include="Controller\User\UpdateUserProfileController.cs" />
    <Compile Include="Controller\User\UserProfileController.cs" />
    <Compile Include="DTO\Assist\AllComboDataListDTO.cs" />
    <Compile Include="DTO\Assist\ComboDataListDTO.cs" />
    <Compile Include="DTO\Auth\AuthMenuListDTO.cs" />
    <Compile Include="DTO\Auth\CheckUserDTO.cs" />
    <Compile Include="DTO\Auth\HasPermissionsDTO.cs" />
    <Compile Include="DTO\Auth\LogoutDTO.cs" />
    <Compile Include="DTO\Auth\ToggleCompanyDTO.cs" />
    <Compile Include="DTO\Auth\UnBindUserDTO.cs" />
    <Compile Include="DTO\BaseDataDTO.cs" />
    <Compile Include="DTO\BaseDataSaveDTO.cs" />
    <Compile Include="DTO\BAS\AppletParamDTO.cs" />
    <Compile Include="DTO\BAS\CollectingUnit\CollectingUnitDTO.cs" />
    <Compile Include="DTO\BAS\GetCurrAreaDTO.cs" />
    <Compile Include="DTO\BD\Customer\AddressListDTO.cs" />
    <Compile Include="DTO\BD\Dept\DeptListDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductPropCheckDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductPropDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductStandardMapDTO.cs" />
    <Compile Include="DTO\Common\DownloadFileDTO.cs" />
    <Compile Include="DTO\COO\IncomeDisburse\RechargeDTO.cs" />
    <Compile Include="DTO\COO\Receipt\ReceiptSaveDTO.cs" />
    <Compile Include="DTO\COO\Receipt\ReceiptUnSubmitDTO.cs" />
    <Compile Include="DTO\COO\Receipt\ReceiptSubmitDTO.cs" />
    <Compile Include="DTO\COO\Receipt\ReceiptUnAuditDTO.cs" />
    <Compile Include="DTO\COO\Receipt\ReceiptAuditDTO.cs" />
    <Compile Include="DTO\RepeatedSubmitDTO.cs" />
    <Compile Include="DTO\BaseDTO.cs" />
    <Compile Include="DTO\BaseDetailDTO.cs" />
    <Compile Include="DTO\BaseListPageDTO.cs" />
    <Compile Include="DTO\BaseListDTO.cs" />
    <Compile Include="DTO\BAS\Building\BuildingListDTO.cs" />
    <Compile Include="DTO\BAS\Building\BuildingDeleteDTO.cs" />
    <Compile Include="DTO\BAS\Building\BuildingDetailDTO.cs" />
    <Compile Include="DTO\BAS\Building\BuildingEditDTO.cs" />
    <Compile Include="DTO\BAS\Building\BuildingSaveDTO.cs" />
    <Compile Include="DTO\BAS\Case\CaseAddToCustomerDTO.cs" />
    <Compile Include="DTO\BAS\Case\CaseCopyDTO.cs" />
    <Compile Include="DTO\BAS\Case\CaseDeleteDTO.cs" />
    <Compile Include="DTO\BAS\Case\CaseDetailDTO.cs" />
    <Compile Include="DTO\BAS\Case\CaseEditDTO.cs" />
    <Compile Include="DTO\BAS\Case\CaseListDTO.cs" />
    <Compile Include="DTO\BAS\Case\CaseMycaseListDTO.cs" />
    <Compile Include="DTO\BAS\Case\CaseSaveDTO.cs" />
    <Compile Include="DTO\BAS\Case\CaseUnCopyDTO.cs" />
    <Compile Include="DTO\BAS\EnumDataListDTO.cs" />
    <Compile Include="DTO\BAS\MulClassType\MulClassTypeDataListDTO.cs" />
    <Compile Include="DTO\BAS\MulClassType\MulClassTypeListDTO.cs" />
    <Compile Include="DTO\BAS\StockSysParamDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductClearStockAuxPropDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductStockProfileDTO.cs" />
    <Compile Include="DTO\BD\Series\SeriesListDTO.cs" />
    <Compile Include="DTO\Common\UploadFileDTO.cs" />
    <Compile Include="DTO\BD\BillType\BillTypeListDTO.cs" />
    <Compile Include="DTO\BD\Brand\BrandListDTO.cs" />
    <Compile Include="DTO\BD\Category\CategoryTreeListDTO.cs" />
    <Compile Include="DTO\BD\Customer\Consumption\ConsumptionListDto.cs" />
    <Compile Include="DTO\BD\Customer\CustomerCheckMergeDTO.cs" />
    <Compile Include="DTO\BD\Customer\CustomerMergeDTO.cs" />
    <Compile Include="DTO\BD\Customer\Duty\CustomerDutyRemoveDTO.cs" />
    <Compile Include="DTO\BD\Customer\Duty\CustomerDutyAddDTO.cs" />
    <Compile Include="DTO\BD\Customer\Duty\CustomerDutyReplaceDTO.cs" />
    <Compile Include="DTO\BD\Customer\Contacts\CustomerAddressDeleteDTO.cs" />
    <Compile Include="DTO\BD\Customer\Contacts\CustomerContactsSaveDTO.cs" />
    <Compile Include="DTO\BD\Customer\Contacts\CustomerAddressEditDTO.cs" />
    <Compile Include="DTO\BD\Customer\CustomerMatchDTO.cs" />
    <Compile Include="DTO\BD\Customer\CustomerValidateDTO.cs" />
    <Compile Include="DTO\BD\Customer\Image\CustomerImageListDTO.cs" />
    <Compile Include="DTO\BD\Position\PositionAllDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductAuxPropDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductSubAuxPropDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductProfileDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductSaleDetailDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductScanCodeDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductStockDetailDTO.cs" />
    <Compile Include="DTO\BD\Staff\StaffDetailDTO.cs" />
    <Compile Include="DTO\BD\Staff\StaffFollowUpCustomerDTO.cs" />
    <Compile Include="DTO\BD\Staff\StaffHandoverDTO.cs" />
    <Compile Include="DTO\BPM\Approval\ApprovalRejectDTO.cs" />
    <Compile Include="DTO\BPM\Approval\ApprovalDetailDTO.cs" />
    <Compile Include="DTO\BPM\Approval\ApprovalListDTO.cs" />
    <Compile Include="DTO\BPM\Approval\ApprovalAuditDTO.cs" />
    <Compile Include="DTO\BAS\StoreSysParamDTO.cs" />
    <Compile Include="DTO\Common\Duty\DutyAddDTO.cs" />
    <Compile Include="DTO\Common\Duty\DutyRemoveDTO.cs" />
    <Compile Include="DTO\Common\Duty\DutyReplaceDTO.cs" />
    <Compile Include="DTO\Common\Print\PrintTemplateListDTO.cs" />
    <Compile Include="DTO\Common\Share\ShareShopPosterDTO.cs" />
    <Compile Include="DTO\COO\IncomeDisburse\IncomeDisburseListDTO.cs" />
    <Compile Include="DTO\COO\IncomeDisburse\IncomeDisburseReceiptDTO.cs" />
    <Compile Include="DTO\COO\Receipt\ReceiptConfirmDTO.cs" />
    <Compile Include="DTO\COO\Receipt\ReceiptDeleteDTO.cs" />
    <Compile Include="DTO\COO\Receipt\ReceiptDetailDTO.cs" />
    <Compile Include="DTO\COO\Receipt\ReceiptEditDTO.cs" />
    <Compile Include="DTO\COO\Receipt\ReceiptListDTO.cs" />
    <Compile Include="DTO\EWC\EwcGetConsumerQrCodeDTO.cs" />
    <Compile Include="DTO\IM\Notice\NoticeDetailDTO.cs" />
    <Compile Include="DTO\IM\Notice\NoticeListDTO.cs" />
    <Compile Include="DTO\IM\Notice\NoticeSetReaderDTO.cs" />
    <Compile Include="DTO\Ops\OpsBaseDTO.cs" />
    <Compile Include="DTO\Ops\OpsUnBindUserDTO.cs" />
    <Compile Include="DTO\Ops\OpsBindUserDTO.cs" />
    <Compile Include="DTO\SAL\DataAnalysis\FunnelSalesStatisticsDTO.cs" />
    <Compile Include="DTO\SAL\DataAnalysis\RankingEmployeesDTO.cs" />
    <Compile Include="DTO\SAL\DataAnalysis\RankingEmployeesListDTO.cs" />
    <Compile Include="DTO\SAL\DataAnalysis\SalesTrendDto.cs" />
    <Compile Include="DTO\SAL\DataAnalysis\StatisicDeptField.cs" />
    <Compile Include="DTO\SAL\DataAnalysis\StatisticAnalysisBulletinDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\PayAccountDataDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\PaymentTypeDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionRejectDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionTerminateDTO.cs" />
    <Compile Include="DTO\SAL\ShopCart\ShopCarReduceDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordReplaceDutyDTO.cs" />
    <Compile Include="DTO\SAL\ShopCart\ShopCartBatchSaveDTO.cs" />
    <Compile Include="DTO\SAL\ShopCart\ShopCartUpdateQtyDTO.cs" />
    <Compile Include="DTO\SEL\Combine\CombineBaseDTO.cs" />
    <Compile Include="DTO\SEL\Combine\CombineGetDTO.cs" />
    <Compile Include="DTO\SEL\PropValue\PropValueLoadOrCreateDTO.cs" />
    <Compile Include="DTO\SEL\Suite\SuiteGetByProductDTO.cs" />
    <Compile Include="DTO\SEL\Suite\SuiteBaseDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceCategoryListDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceChangeDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceChangeListDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceFollowSaveDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceMasterListDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceRefuseDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceReceiveDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceReportDetailDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceReportDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceReservationListDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceReservationSaveDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceTabCountDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceFeedDetailDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceFeedListDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceFeedSaveDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceListDTO.cs" />
    <Compile Include="DTO\SER\Service\ServiceTransOrderDTO.cs" />
    <Compile Include="DTO\STE\Channel\ChannelDetailDataDTO.cs" />
    <Compile Include="DTO\STE\Dashboard\DashboardSaleAchievementDTO.cs" />
    <Compile Include="DTO\STE\Dashboard\DashboardSaleGoalDTO.cs" />
    <Compile Include="DTO\Report\ReportSaleRankDTO.cs" />
    <Compile Include="DTO\Report\ReportSaleStatDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordFinishDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordReceiveDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordClaimDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordCloseDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordGetPhaseDTO.cs" />
    <Compile Include="DTO\BD\Customer\Contacts\CustomerContactsListDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordTabCountDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordPhaseCountDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordPushPhaseDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordPushSaleIntentionDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordPushOrderDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordEditDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordValidateDTO.cs" />
    <Compile Include="DTO\BD\Customer\CustomerDetailDTO.cs" />
    <Compile Include="DTO\BD\Customer\CustomerEditDTO.cs" />
    <Compile Include="DTO\BD\Customer\CustomerListDTO.cs" />
    <Compile Include="DTO\BD\Customer\CustomerSaveDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductDetailDTO.cs" />
    <Compile Include="DTO\BD\Product\ProductListDTO.cs" />
    <Compile Include="DTO\Assist\AssistDataListDTO.cs" />
    <Compile Include="DTO\BD\Staff\StaffListDTO.cs" />
    <Compile Include="DTO\EWC\EwcBaseDTO.cs" />
    <Compile Include="DTO\EWC\EwcCode2SessionDTO.cs" />
    <Compile Include="DTO\EWC\EwcReceiveMsgDTO.cs" />
    <Compile Include="DTO\EWC\EwcSendMsgDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordDeleteDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordSaveDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordDetailDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordListDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionTabCountDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionPushReceiptDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionPushOrderDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionAuditDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionDeleteDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionReceiptDTO.cs" />
    <Compile Include="DTO\Common\Print\PrintDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionSubmitDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionListDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionEditDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionSaveDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionArrangeDesignerDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionArrangeMeasurerDTO.cs" />
    <Compile Include="DTO\SAL\SaleIntention\SaleIntentionDetailDTO.cs" />
    <Compile Include="DTO\SAL\ShopCart\ShopCartClearDTO.cs" />
    <Compile Include="DTO\SAL\ShopCart\ShopCartDeleteDTO.cs" />
    <Compile Include="DTO\SAL\ShopCart\ShopCartListDTO.cs" />
    <Compile Include="DTO\SAL\ShopCart\ShopCartSaveDTO.cs" />
    <Compile Include="DTO\STE\Channel\ChannelDeleteDTO.cs" />
    <Compile Include="DTO\STE\Channel\ChannelDetailDTO.cs" />
    <Compile Include="DTO\STE\Channel\ChannelEditDTO.cs" />
    <Compile Include="DTO\STE\Channel\ChannelListDTO.cs" />
    <Compile Include="DTO\STE\Channel\ChannelSaveDTO.cs" />
    <Compile Include="DTO\STE\Dashboard\DashboardTaskDTO.cs" />
    <Compile Include="DTO\STE\FollowerRecord\FollowerRecordSaveDTO.cs" />
    <Compile Include="DTO\STE\FollowerRecord\FollowerRecordListDTO.cs" />
    <Compile Include="DTO\Demo\DemoDTO.cs" />
    <Compile Include="DTO\STE\Order\Attachment\OrderAttachmentAddDTO.cs" />
    <Compile Include="DTO\STE\Order\Attachment\OrderAttachmentDeleteDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderUnstdAuditDTO.cs" />
    <Compile Include="DTO\STE\Order\ChangeBillTypeDeptListDTO.cs" />
    <Compile Include="DTO\STE\Order\ResultBrandListDTO.cs" />
    <Compile Include="DTO\STE\Order\IsAllowOrdersDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderChangeDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderRejectDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderDetailDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderEditDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderSaveDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderListDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderAuditDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderDeleteDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderPushReceiptDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderReceiptDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderSubmitDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderTabCountDTO.cs" />
    <Compile Include="DTO\SAL\CustomerRecord\CustomerRecordShareCardDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderTerminateDTO.cs" />
    <Compile Include="DTO\STE\Order\OrderUnChangeDTO.cs" />
    <Compile Include="DTO\STK\ReserveBill\ReserveBillMatchQtyByIdDTO.cs" />
    <Compile Include="DTO\STK\ReserveBill\ReserveBillMatchQtyDTO.cs" />
    <Compile Include="DTO\STK\ReserveBill\ReserveBillSourceEditDTO.cs" />
    <Compile Include="DTO\STK\ReserveBill\ReserveBillSourceReleaseDTO.cs" />
    <Compile Include="DTO\STK\ReserveBill\ReserveBillSourceSaveDTO.cs" />
    <Compile Include="DTO\STK\Rpt\StockSynthesizeSummaryDTO.cs" />
    <Compile Include="DTO\STK\Rpt\StockSynthesizeListDTO.cs" />
    <Compile Include="DTO\STK\Stockstatus\StockstatusListDTO.cs" />
    <Compile Include="DTO\SYS\VersionInfoDTO.cs" />
    <Compile Include="DTO\User\ModifyPwdDTO.cs" />
    <Compile Include="DTO\User\MyCardDTO.cs" />
    <Compile Include="DTO\User\MyCompanyDTO.cs" />
    <Compile Include="DTO\User\UpdateUserProfileDTO.cs" />
    <Compile Include="DTO\User\UserProfileDTO.cs" />
    <Compile Include="Filter\BaseRequestFilter.cs" />
    <Compile Include="Filter\EnterpriseWeChatFilter.cs" />
    <Compile Include="Filter\CheckPermissionFilter.cs" />
    <Compile Include="Filter\RepeatedSubmitFilter.cs" />
    <Compile Include="Model\Assist\ComboDataModel.cs" />
    <Compile Include="Model\Assist\AssistDataModel.cs" />
    <Compile Include="Model\Auth\HasPermissionsModel.cs" />
    <Compile Include="Model\Auth\AuthMenuListModel.cs" />
    <Compile Include="Model\BaseBillModel.cs" />
    <Compile Include="Model\BaseCountModel.cs" />
    <Compile Include="Model\BaseDataModel.cs" />
    <Compile Include="Model\BaseDataSimpleModel.cs" />
    <Compile Include="Model\BaseImageModel.cs" />
    <Compile Include="Model\BaseModel.cs" />
    <Compile Include="Model\BAS\AppletParamModel.cs" />
    <Compile Include="Model\BAS\Case\CaseDetailModel.cs" />
    <Compile Include="Model\BAS\Case\CaseEditModel.cs" />
    <Compile Include="Model\BAS\Case\CaseListModel.cs" />
    <Compile Include="Model\BAS\Case\CaseMycaseListModel.cs" />
    <Compile Include="Model\BAS\CollectingUnit\CollectionUnitModel.cs" />
    <Compile Include="Model\BAS\GetCurrAreaModel.cs" />
    <Compile Include="Model\BAS\EnumDataListModel.cs" />
    <Compile Include="Model\BAS\StoreSysParamModel.cs" />
    <Compile Include="Model\BAS\Building\BuildingListModel.cs" />
    <Compile Include="Model\BAS\Building\BuildingDetailModel.cs" />
    <Compile Include="Model\BAS\Building\BuildingEditModel.cs" />
    <Compile Include="Model\BD\BillType\BillTypeModel.cs" />
    <Compile Include="Model\BD\Brand\BrandListModel.cs" />
    <Compile Include="Model\BD\Customer\Address\CustomerAddressEditModel.cs" />
    <Compile Include="Model\BD\Customer\Consumption\ConsumptionListModel.cs" />
    <Compile Include="Model\BD\Customer\CustomerDetailModel.cs" />
    <Compile Include="Model\BD\Customer\CustomerEditModel.cs" />
    <Compile Include="Model\BD\Customer\Address\CustomerAddressListModel.cs" />
    <Compile Include="Model\BD\Customer\AddressListModel.cs" />
    <Compile Include="Model\BD\Customer\CustomerMatchModel.cs" />
    <Compile Include="Model\BD\Customer\CustomerListModel.cs" />
    <Compile Include="Model\BD\Customer\CustomerSimpleModel.cs" />
    <Compile Include="Model\BD\Customer\Image\CustomerImageListModel.cs" />
    <Compile Include="Model\BD\Dept\DeptListModel.cs" />
    <Compile Include="Model\BD\Product\ProductAuxPropModel.cs" />
    <Compile Include="Model\BD\Product\ProductClearStockAuxPropModel.cs" />
    <Compile Include="Model\BD\Product\ProductDetailModel.cs" />
    <Compile Include="Model\BD\Product\ProductListModel.cs" />
    <Compile Include="Model\BD\Product\ProductPropModel.cs" />
    <Compile Include="Model\BD\Product\ProductStockProfileModel.cs" />
    <Compile Include="Model\BD\Product\ProductPriceModel.cs" />
    <Compile Include="Model\BD\Product\ProductProfileModel.cs" />
    <Compile Include="Model\BD\Product\ProductSaleDetailModel.cs" />
    <Compile Include="Model\BD\Product\ProductSimpleModel.cs" />
    <Compile Include="Model\BD\Product\ProductStockDetailModel.cs" />
    <Compile Include="Model\BD\Product\ProductTagModel.cs" />
    <Compile Include="Model\BD\Series\SeriesListModel.cs" />
    <Compile Include="Model\BD\Staff\StaffDetailModel.cs" />
    <Compile Include="Model\BD\Staff\StaffFollowUpCustomerModel.cs" />
    <Compile Include="Model\BD\Staff\StaffListModel.cs" />
    <Compile Include="Model\BD\Staff\StaffSimpleModel.cs" />
    <Compile Include="Model\BPM\Approval\ApprovalDetailModel.cs" />
    <Compile Include="Model\BPM\Approval\ApprovalListModel.cs" />
    <Compile Include="Model\Common\Print\PrintModel.cs" />
    <Compile Include="Model\Common\Share\ShareShopPosterModel.cs" />
    <Compile Include="Model\COO\IncomeDisburse\IncomeDisburseSimpleModel.cs" />
    <Compile Include="Model\COO\IncomeDisburse\RechargeModel.cs" />
    <Compile Include="Model\COO\Receipt\ReceiptDetailModel.cs" />
    <Compile Include="Model\COO\Receipt\ReceiptEditModel.cs" />
    <Compile Include="Model\COO\Receipt\ReceiptModel.cs" />
    <Compile Include="Model\COO\Receipt\ReceiptListModel.cs" />
    <Compile Include="Model\IM\Notice\NoticeDetailModel.cs" />
    <Compile Include="Model\IM\Notice\NoticeListModel.cs" />
    <Compile Include="Model\SAL\DataAnalysis\DataBaseModel.cs" />
    <Compile Include="Model\SAL\DataAnalysis\DataPresentationModel.cs" />
    <Compile Include="Model\SAL\DataAnalysis\DataResultModel.cs" />
    <Compile Include="Model\SAL\DataAnalysis\FunnelSalesStatisticsModel.cs" />
    <Compile Include="Model\SAL\DataAnalysis\RankingEmployeesModel.cs" />
    <Compile Include="Model\SAL\DataAnalysis\SalesTrendModel.cs" />
    <Compile Include="Model\SAL\SaleIntention\PayAccountDataModel.cs" />
    <Compile Include="Model\SAL\SaleIntention\PaymentTypeModel.cs" />
    <Compile Include="Model\SER\Service\ServiceCategoryListModel.cs" />
    <Compile Include="Model\SER\Service\ServiceDetailModel.cs" />
    <Compile Include="Model\SER\Service\ServiceFeedDetailModel.cs" />
    <Compile Include="Model\SER\Service\ServiceChangeListModel.cs" />
    <Compile Include="Model\SER\Service\ServiceFeedListModel.cs" />
    <Compile Include="Model\SER\Service\ServiceFeedSaveModel.cs" />
    <Compile Include="Model\SER\Service\ServiceListModel.cs" />
    <Compile Include="Model\SER\Service\ServiceMasterListModel.cs" />
    <Compile Include="Model\SER\Service\ServiceReportDetailModel.cs" />
    <Compile Include="Model\SER\Service\ServiceReservationListModel.cs" />
    <Compile Include="Model\STE\Channel\ChannelDetailDataModel.cs" />
    <Compile Include="Model\STE\Dashboard\DashboardSaleGoalModel.cs" />
    <Compile Include="Model\STE\Dashboard\DashboardSaleAchievementModel.cs" />
    <Compile Include="Model\Report\ReportSaleRankModel.cs" />
    <Compile Include="Model\Report\ReportSaleStatModel.cs" />
    <Compile Include="Model\SAL\CustomerRecord\CustomerRecordEditModel.cs" />
    <Compile Include="Model\SAL\CustomerRecord\CustomerRecordGetPhaseModel.cs" />
    <Compile Include="Model\SAL\CustomerRecord\CustomerRecordPhaseCountModel.cs" />
    <Compile Include="Model\SAL\SaleIntention\SaleIntentionListModel.cs" />
    <Compile Include="Model\SAL\SaleIntention\SaleIntentionEditModel.cs" />
    <Compile Include="Model\SAL\SaleIntention\SaleIntentionDetailModel.cs" />
    <Compile Include="Model\SAL\SaleIntention\SaleIntentionPushReceiptModel.cs" />
    <Compile Include="Model\SAL\ShopCart\ShopCartListModel.cs" />
    <Compile Include="Model\STE\Channel\ChannelDetailModel.cs" />
    <Compile Include="Model\STE\Channel\ChannelEditModel.cs" />
    <Compile Include="Model\STE\Channel\ChannelListModel.cs" />
    <Compile Include="Model\STE\Dashboard\DashboardTaskModel.cs" />
    <Compile Include="Model\STE\Order\ChangeBillTypeDeptListModel.cs" />
    <Compile Include="Model\STE\Order\ResultBrandListModel.cs" />
    <Compile Include="Model\STE\Order\OrderDetailModel.cs" />
    <Compile Include="Model\STE\Order\OrderEditModel.cs" />
    <Compile Include="Model\STE\Order\OrderListModel.cs" />
    <Compile Include="Model\STE\Order\OrderPushReceiptModel.cs" />
    <Compile Include="Model\STK\ReserveBill\ReserveBillSourceEditModel.cs" />
    <Compile Include="Model\STK\Rpt\StockSynthesizeSummaryModel.cs" />
    <Compile Include="Model\STK\Rpt\StockSynthesizeListModel.cs" />
    <Compile Include="Model\STK\Stockstatus\StockstatusListModel.cs" />
    <Compile Include="Model\SYS\VersionInfoModel.cs" />
    <Compile Include="Model\User\MyCardModel.cs" />
    <Compile Include="Model\SAL\CustomerRecord\CustomerRecordDetailModel.cs" />
    <Compile Include="Model\SAL\CustomerRecord\CustomerRecordListModel.cs" />
    <Compile Include="Model\STE\FollowerRecord\FollowerRecordListModel.cs" />
    <Compile Include="Model\User\QyWxUserInfoModel.cs" />
    <Compile Include="Model\User\UserProfileModel.cs" />
    <Compile Include="EwcJsonClient.cs" />
    <Compile Include="OpsJsonClient.cs" />
    <Compile Include="Plugin\BD\Product\GetProductDetailPlugIn.cs" />
    <Compile Include="Plugin\BD\Product\GetProductListPlugIn.cs" />
    <Compile Include="Plugin\SEC\User\UnBindQyWxPlugIn.cs" />
    <Compile Include="Response\BaseResponse.cs" />
    <Compile Include="DTO\Auth\LoginDTO.cs" />
    <Compile Include="RegisterController.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Response\BaseListData.cs" />
    <Compile Include="Response\BaseListPageData.cs" />
    <Compile Include="Response\BD\Customer\QueryDetailResponse.cs" />
    <Compile Include="Response\EWC\EwcBaseResponse.cs" />
    <Compile Include="Response\Ops\OpsBaseResponse.cs" />
    <Compile Include="Response\YDJ\BPM\Approval\ApprovalListResponse.cs" />
    <Compile Include="Response\YDJ\BPM\Approval\ApprovalDetailResponse.cs" />
    <Compile Include="Response\YDJ\MainFW\HasPermissions\HasPermissionsResponse.cs" />
    <Compile Include="Response\YDJ\Report\GoalInfo\GoalInfoResponse.cs" />
    <Compile Include="Response\YDJ\Report\SysOverview\SysOverviewResponse.cs" />
    <Compile Include="Response\YDJ\SAL\SaleIntention\LoadSettleInfoResponse.cs" />
    <Compile Include="Response\YDJ\SAL\SaleIntention\PushSaleIntentionResponse.cs" />
    <Compile Include="Response\YDJ\STE\Order\LoadSettleInfoResponse.cs" />
    <Compile Include="Response\YDJ\STE\Order\PushOrderResponse.cs" />
    <Compile Include="SimpleSSORequestService.cs" />
    <Compile Include="Utils\BeiJingTime.cs" />
    <Compile Include="Utils\MSAIUtil.cs" />
    <Compile Include="Utils\BillUtil.cs" />
    <Compile Include="Utils\CheckSignature.cs" />
    <Compile Include="Config\ConfigExtentions.cs" />
    <Compile Include="Config\ConfigFileMonitor.cs" />
    <Compile Include="Config\CustomerConfig.cs" />
    <Compile Include="Utils\PromotionUtil.cs" />
    <Compile Include="Utils\ReserveBillHelper.cs" />
    <Compile Include="Utils\ListSqlBuilderUtil.cs" />
    <Compile Include="Utils\ResultBrandUtil.cs" />
    <Compile Include="Utils\StringUtil.cs" />
    <Compile Include="Utils\DateTimeUtil.cs" />
    <Compile Include="Utils\ApprovalHelper.cs" />
    <Compile Include="Utils\HtmlFormExtentions.cs" />
    <Compile Include="Utils\ImageFieldUtil.cs" />
    <Compile Include="Utils\IOperationResultExtentions.cs" />
    <Compile Include="Utils\JNConvert.cs" />
    <Compile Include="Utils\ProductUtil_Image.cs" />
    <Compile Include="Utils\ProductUtil.cs" />
    <Compile Include="Utils\JsonClientUtil.cs" />
    <Compile Include="Utils\SuiteUtil.cs" />
    <Compile Include="Utils\TimeHelp.cs" />
    <Compile Include="Utils\UserContextExtentions.cs" />
    <Compile Include="Utils\UserHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="DTO\BaseDetailDTO.json" />
    <None Include="DTO\BaseListPageDTO.json" />
    <None Include="DTO\BaseListDTO.json" />
    <None Include="DTO\BaseDTO.json" />
    <None Include="DTO\BD\Customer\CustomerSaveDTO.json" />
    <None Include="DTO\BD\Product\ProductListDTO.json" />
    <None Include="Response\Auth\CheckUser.json" />
    <None Include="Response\Auth\Login.json" />
    <None Include="Response\BaseListPageData.json" />
    <None Include="Response\BaseListData.json" />
    <None Include="Response\BaseResponse.json" />
    <None Include="Response\BD\Customer\CustomerEdit.json" />
    <None Include="Response\BD\Customer\CustomerList.json" />
    <None Include="Response\BD\Customer\CustomerDetail.json" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Readme.txt" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Controller\BD\Activity\" />
    <Folder Include="Controller\BD\Casus\" />
    <Folder Include="Controller\BD\Channel\" />
    <Folder Include="Controller\BD\House\" />
    <Folder Include="Model\SAL\Promotion\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <!-- <PropertyGroup>
    <PostBuildEvent>xcopy /e /r /y "$(TargetDir)$(TargetName).dll" "$(SolutionDir)JieNor.AMS.YDJ.Store.Web\bin\"
xcopy /e /r /y "$(TargetDir)$(TargetName).pdb" "$(SolutionDir)JieNor.AMS.YDJ.Store.Web\bin\"</PostBuildEvent>
  </PropertyGroup> -->
</Project>