using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.DTO.HeadStatement;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MS.API.Controller.HeadStatement
{
    /// <summary>
    /// 总部对账：作废接口
    /// </summary>
    public class HeadStatementCancelController : BaseController<HeadStatementCancelDTO>
    {
        public string FormId
        {
            get { return "ydj_headstatement"; }
        }

        protected override bool IsAsync => true;

        protected override string UniquePrimaryKey => "billNo";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(HeadStatementCancelDTO dto)
        {
            var resp = new BaseResponse<MuSiData>();

            if (!Valid(dto, resp)) return resp;

            foreach (var group in dto.Data.GroupBy(s => s.AgentId))
            {
                string fagentid = group.Key;
                var agentCtx = this.Context.CreateAgentDBContext(fagentid);

                // 向麦浩系统发送请求
                var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(agentCtx, new CommonBillDTO()
                {
                    FormId = "ydj_headstatement",
                    OperationNo = "MSCancel",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "data", group.Select(s => s.BillNo).ToJson() }
                    }
                });

                var result = response.OperationResult;

                if (result.IsSuccess)
                {
                    resp.Data.SucceedNumbers.AddRange(group.Select(s => s.BillNo));
                    resp.Messages.Add(result.ToString());
                }
                else
                {
                    resp.Data.FailedNumbers.AddRange(group.Select(s => s.BillNo));
                    resp.Messages.Add(result.ToString());
                }
            }

            resp.Code = 200;
            resp.Success = true;
            resp.Message = resp.Messages.JoinEx("\r\n", false);
            return resp;
        }

        private bool Valid(HeadStatementCancelDTO dto, BaseResponse<MuSiData> resp)
        {
            if (dto.Data == null || dto.Data.Count == 0)
            {
                resp.Code = 400;
                resp.Message = "参数data为空！";
                resp.Success = false;

                return false;
            }

            List<string> agentNos = new List<string>();
            List<string> errorMsgs = new List<string>();

            List<HeadStatementCancelDTO.HeadStatementCancelEntry> entrys = dto.Data;

            foreach (var entry in entrys)
            {
                string agentNo = entry.AgentNo;
                string billNo = entry.BillNo;

                if (agentNo.IsNullOrEmptyOrWhiteSpace())
                {
                    resp.Code = 400;
                    resp.Message = "参数agentNo不能为空！";
                    resp.Success = false;

                    return false;
                }

                if (billNo.IsNullOrEmptyOrWhiteSpace())
                {
                    resp.Code = 400;
                    resp.Message = "参数billNo不能为空！";
                    resp.Success = false;

                    return false;
                }

                agentNos.Add(agentNo);
            }

            // 获取业务经销商
            var agents = this.Container.GetService<IAgentService>().GetBizAgentIdByNos(this.Context, agentNos);

            foreach (var entry in entrys)
            {
                string fagentno = entry.AgentNo;
                agents.TryGetValue(fagentno, out string agentId);

                if (agentId.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add($"经销商【{fagentno}】不存在！");
                }

                // 插入经销商id
                entry.AgentId = agentId;
            }

            if (errorMsgs.Any())
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", errorMsgs);
                resp.Success = false;
                resp.Data.Flag = MuSiFlag.FAIL.ToString();
                resp.Data.FailedNumbers = entrys.Select(s => s.BillNo).ToList();

                return false;
            }

            return true;
        }

        protected override Dictionary<string, string> CreateDistributedLocks(HeadStatementCancelDTO dto)
        {
            Dictionary<string, string> dicResult = new Dictionary<string, string>();
            foreach (var item in dto.Data)
            {
                if (dicResult.ContainsKey($"DistributedLock:{this.FormId}:{item.BillNo}"))
                {
                    continue;
                }
                dicResult.Add($"DistributedLock:{this.FormId}:{item.BillNo}", $"总部对账单 {item.BillNo} 正在锁定中，请稍后再操作！");
            }
            return dicResult;
        }
    }

    ///// <summary>
    ///// 总部对账：作废接口
    ///// </summary>
    //public class HeadStatementCancelController : BaseController
    //{
    //    /// <summary>
    //    /// 处理请求
    //    /// </summary>
    //    /// <param name="dto"></param>
    //    /// <returns></returns>
    //    public object Any(HeadStatementCancelDTO dto)
    //    {
    //        base.InitializeOperationContext(dto);

    //        var resp = new BaseResponse<object>();

    //        if (!Valid(dto, resp)) return resp;

    //        Dictionary<string, HeadStatementCancelItemModel> dic = new Dictionary<string, HeadStatementCancelItemModel>();

    //        foreach (var summary in dto.SummaryList)
    //        {
    //            if (dic.TryGetValue(summary.AgentId, out var item))
    //            {
    //                item.SummaryNos.Add(summary.BillNo);
    //            }
    //            else
    //            {
    //                item = new HeadStatementCancelItemModel();
    //                dic[summary.AgentId] = item;
    //                item.SummaryNos.Add(summary.BillNo);
    //            }
    //        }

    //        foreach (var detail in dto.DetailList)
    //        {
    //            if (dic.TryGetValue(detail.AgentId, out var item))
    //            {
    //                item.DetailNos.Add(detail.BillNo);
    //            }
    //            else
    //            {
    //                item = new HeadStatementCancelItemModel();
    //                dic[detail.AgentId] = item;
    //                item.DetailNos.Add(detail.BillNo);
    //            }
    //        }

    //        foreach (var item in dic)
    //        {
    //            string fagentid = item.Key;
    //            var agentCtx = this.Context.CreateAgentDBContext(fagentid);

    //            // 向麦浩系统发送请求
    //            var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(agentCtx, new CommonBillDTO()
    //            {
    //                FormId = "ydj_headstatementsummary",
    //                OperationNo = "MSCancel",
    //                SimpleData = new Dictionary<string, string>
    //                {
    //                    { "data", item.Value.ToJson() }
    //                }
    //            });

    //            var result = response.OperationResult;
    //            if (result.ComplexMessage.ErrorMessages.Any())
    //            {
    //                resp.Code = 500;
    //                resp.Message = result.GetErrorMessage();
    //                return resp;
    //            }
    //        }

    //        resp.Code = 200;
    //        resp.Success = true;
    //        resp.Message = "操作成功！";
    //        return resp;
    //    }

    //    private bool Valid(HeadStatementCancelDTO dto, BaseResponse<object> resp)
    //    {
    //        if ((dto.SummaryList == null || dto.SummaryList.Count == 0) && (dto.DetailList == null || dto.DetailList.Count == 0))
    //        {
    //            resp.Code = 400;
    //            resp.Message = "参数summaryList和detailList不能同时为空！";
    //            resp.Success = false;

    //            return false;
    //        }

    //        List<string> agentNos = new List<string>();

    //        List<HeadStatementCancelDTO.HeadStatementCancelEntry> entrys = new List<HeadStatementCancelDTO.HeadStatementCancelEntry>();
    //        if (dto.SummaryList != null) entrys.AddRange(dto.SummaryList);
    //        if (dto.DetailList != null) entrys.AddRange(dto.DetailList);

    //        foreach (var entry in entrys)
    //        {
    //            string agentNo = entry.AgentNo;
    //            string billNo = entry.BillNo;

    //            if (agentNo.IsNullOrEmptyOrWhiteSpace())
    //            {
    //                resp.Code = 400;
    //                resp.Message = "参数agentNo不能为空！";
    //                resp.Success = false;

    //                return false;
    //            }

    //            if (billNo.IsNullOrEmptyOrWhiteSpace())
    //            {
    //                resp.Code = 400;
    //                resp.Message = "参数billNo不能为空！";
    //                resp.Success = false;

    //                return false;
    //            }

    //            agentNos.Add(agentNo);
    //        }

    //        string errorMsg = string.Empty;

    //        var agents = this.Context.LoadBizDataByNo("bas_agent", "fnumber", agentNos);

    //        foreach (var entry in entrys)
    //        {
    //            string agentNo = entry.AgentNo;
    //            var agent = agents.FirstOrDefault(s => Convert.ToString(s["fnumber"]).EqualsIgnoreCase(agentNo));
    //            if (agent == null)
    //            {
    //                errorMsg += $"经销商【{agentNo}】不存在！\r\n";
    //            }

    //            // 插入经销商id
    //            entry.AgentId = Convert.ToString(agent?["id"]);
    //        }

    //        if (!errorMsg.IsNullOrEmptyOrWhiteSpace())
    //        {
    //            resp.Code = 400;
    //            resp.Message = errorMsg;
    //            resp.Success = false;

    //            return false;
    //        }

    //        return true;
    //    }
    //}
}