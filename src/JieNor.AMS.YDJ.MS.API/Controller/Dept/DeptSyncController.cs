using JieNor.AMS.YDJ.MS.API.DTO.Dept;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Validation;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.Dept
{
    /// <summary>
    /// 部门：同步接口
    /// </summary>
    public class DeptSyncController : BaseAuthController<DeptSyncDto>
    {
        protected HtmlForm HtmlForm { get; set; }

        protected string FormId
        {
            get { return "ydj_dept"; }
        }

        protected override bool IsAsync => false;

        protected override string UniquePrimaryKey => "id";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(DeptSyncDto dto)
        {
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            var resp = new BaseResponse<MuSiData>();

            if (!Valid(dto, resp)) return resp;

            resp.Code = 200;
            resp.Success = true;
            resp.Message = "操作成功！";
            resp.Data.Flag = MuSiFlag.SUCCESS.ToString();

            #region 查询相关数据
            // 根据外部Id查询所有的经销商Ids
            string sql = $@"select fid,ftranid,fstatus,fagentstatus from t_bas_agent with(nolock) where ftranid in ({dto.Data.Select(x => x.agentid)?.JoinEx(",", true)})";
            var agents = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());
            // 根据外部Id查询所有的用户Ids
            sql = $@"select fid,ftranid from t_sec_user with(nolock) where ftranid in ({dto.Data.Select(x => x.operatorid)?.JoinEx(",", true)})";
            var users = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());
            #endregion
            foreach (var group in dto.Data.GroupBy(s => new { agentid = s.agentid, operatorid = s.operatorid } ))
            {
                try
                {
                    //判断经销商和操作人
                    var agentCtx = MusiAuthValidation.GetUserContext(this.Context, agents, Tuple.Create(group.Key.agentid, group?.FirstOrDefault().agentno), users, group.Key.operatorid, resp);
                    if (!resp.Success)
                    {
                        resp.Data.FailedNumbers.AddRange(group.Select(x => x.id));
                        continue;
                    }
                    var datas = this.ConvertToDynsData(agentCtx, group.ToList());

                    // 本批处理的转换数据
                    if (datas != null && datas.Count > 0)
                    {
                        var tranids = datas.Select(x => Convert.ToString(x["Id"]));

                        var prepareSaveDataService = agentCtx.Container.GetService<IPrepareSaveDataService>();
                        prepareSaveDataService.PrepareDataEntity(agentCtx, this.HtmlForm, datas.ToArray(), OperateOption.Create());

                        var result = this.HttpGateway.InvokeBillOperation(agentCtx, this.HtmlForm.Id, datas, "save", new Dictionary<string, object>());
                        if (result.IsSuccess)
                        {
                            resp.Data.SucceedNumbers.AddRange(tranids);
                        }
                        else
                        {
                            resp.Success = false;
                            resp.Data.FailedNumbers.AddRange(tranids);
                            resp.Data.ErrorMsgs.AddRange(result.ToString()?.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries));
                        }
                    }
                }
                catch (Exception e)
                {
                    this.LogService.Error(e);
                    resp.Success = false;
                    resp.Data.ErrorMsgs.Add(e.Message);
                }
            }

            if (!resp.Success)
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", resp.Data.ErrorMsgs);
                resp.Data.Flag = resp.Data.SucceedNumbers.Any()
                    ? MuSiFlag.PARTSUCCESS.ToString()
                    : MuSiFlag.FAIL.ToString();
            }

            return resp;
        }

        //构造数据
        private List<DynamicObject> ConvertToDynsData(UserContext agentCtx, List<DeptData> Datas)
        {
            var dataMeta = this.MetaModelService.LoadFormModel(agentCtx, this.HtmlForm.Id);
            var dataMetaType = dataMeta.GetDynamicObjectType(agentCtx);

            //查询已经存在的数据
            var Ids = Datas.Where(x => !x.id.IsNullOrEmptyOrWhiteSpace()).Select(x => x.id);
            var exsitDatas = agentCtx.LoadBizDataById(this.HtmlForm.Id, Ids);

            var linkstoreNos = Datas.Where(x => !x.linkstore_num.IsNullOrEmptyOrWhiteSpace()).Select(x => x.linkstore_num);
            var storeDatas = agentCtx.LoadBizDataByNo("bas_store", "fnumber", linkstoreNos);

            // 所有的上级部门Ids
            var parentids = Datas?.Select(x => x.parentid)?.ToList();
            var parentDatas = agentCtx.LoadBizDataById(this.HtmlForm.Id, parentids);

            var dataObjs = new List<DynamicObject>();
            foreach (var data in Datas)
            {
                var dynObj = exsitDatas.Where(x => Convert.ToString(x["Id"]) == data.id).FirstOrDefault();
                if (dynObj == null)
                {
                    dynObj = dataMetaType.CreateInstance() as DynamicObject;
                    dynObj["ftranid"] = data.id;
                }
                dynObj["fname"] = data.name;
                dynObj["fparentid"] = parentDatas?.Where(x => Convert.ToString(x["Id"]) == data.parentid).Select(x => Convert.ToString(x["Id"])).FirstOrDefault();
                dynObj["fdescription"] = data.description;
                switch (data.storetype)
                {
                    case "1":
                        dynObj["fdepttype"] = "dept_type_03";
                        break;
                    case "2":
                        dynObj["fdepttype"] = "dept_type_02";
                        break;
                    case "3":
                        dynObj["fdepttype"] = "dept_type_01";
                        break;
                }
                dynObj["fstore"] = storeDatas?.Where(x => Convert.ToString(x["fnumber"]) == data.linkstore_num).Select(x => Convert.ToString(x["Id"])).FirstOrDefault();
                dataObjs.Add(dynObj);
            }
            return dataObjs;
        }

        private bool Valid(DeptSyncDto dto, BaseResponse<MuSiData> resp)
        {
            if (dto.Data == null || dto.Data.Count == 0)
            {
                resp.Code = 400;
                resp.Message = "参数data不能为空！";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);

                return false;
            }

            List<string> agentIds = new List<string>();
            List<string> errorMsgs = new List<string>();

            foreach (var data in dto.Data)
            {
                if (data.id.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数id不能为空！");
                }
                if (data.name.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数name不能为空！");
                }
                else if (data.agentid.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数agentid不能为空！");
                }
                //如果二级组织不为空，说明是二级分销用户组织操作新增修改
                if (!data.secagentid.IsNullOrEmptyOrWhiteSpace())
                {
                    data.agentid = data.secagentid;
                    data.agentno = data.secagentno;
                }
            }

            if (errorMsgs.Any())
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", errorMsgs);
                resp.Success = false;

                resp.Data.ErrorMsgs = errorMsgs;
                resp.Data.Flag = MuSiFlag.FAIL.ToString();

                return false;
            }

            return true;
        }
    }
}
