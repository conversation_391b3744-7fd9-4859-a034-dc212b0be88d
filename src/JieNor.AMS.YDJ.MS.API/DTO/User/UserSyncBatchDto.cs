using JieNor.AMS.YDJ.MS.API.Filter;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.User
{
    /// <summary>
    /// 用户：同步接口
    /// </summary>
    [Api("认证同步接口批量下发")]
    [Route("/msapi/user/syncbatch")]
    [Authenticate]
    [OperationLogFilter("sec_user")]
    public class UserSyncBatchDto: BaseDTO
    {
        public List<UserData> Data { get; set; }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }

        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }

     
}
