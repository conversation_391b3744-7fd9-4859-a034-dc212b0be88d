using JieNor.AMS.YDJ.MS.API.Filter;
using Newtonsoft.Json.Linq;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.LogisticStatement
{
    /// <summary>
    /// 总部物流对账单：同步接口
    /// </summary>
    [Api("同步接口")]
    [Route("/msapi/logisticstatement/sync")]
    [Authenticate]
    [OperationLogFilter("ydj_logisticsstatement")]
    public class LogisticStatementSyncDTO:BaseDTO
    {
        public JArray Data { get; set; }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }

        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }
}
