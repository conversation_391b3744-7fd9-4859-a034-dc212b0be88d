using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.MS.API.Filter;
using Newtonsoft.Json.Linq;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.Category
{
    /// <summary>
    /// 产品包装明细：总部下发接口
    /// </summary>
    [Api("产品包装明细-总部下发接口")]
    [Route("/msapi/ydj_product/productpackpush")]
    [Authenticate]
    [OperationLogFilter("ydj_product")]
    public class ProductPackPushDTO : BaseDTO
    {
        public JArray Data { get; set; }
    }
}