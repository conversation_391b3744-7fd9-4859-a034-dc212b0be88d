using System.Collections.Generic;
using JieNor.AMS.YDJ.MS.API.Filter;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.PurchaseOrder
{
    /// <summary>
    /// 库存水位管控审批接口
    /// </summary>
    [Api("库存水位管控审批接口")]
    [Route("/msapi/purchaseorder/stockwateraudit")]
    [Authenticate]
    [OperationLogFilter("ydj_purchaseorder")]
    public class PurchaseOrderStockWaterAuditDTO : BaseDTO
    {
        public string  BillNo { get; set; }

        public string Status { get; set; }

        public string AgentNo { get; set; }

        internal string BizAgentId { get; set; }
    }
}
