using JieNor.AMS.YDJ.MS.API.DTO;
using JieNor.AMS.YDJ.MS.API.DTO.Order;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface.Log;

namespace JieNor.AMS.YDJ.MS.API.Plugin.AsyncOperationLog
{
    /// <summary>
    /// 异步操作日志：错误重试
    /// </summary>
    [InjectService]
    [FormId("si_asyncoperationlog|si_simpleasynclog")]
    [OperationNo("retry")]
    public class Retry : AbstractOperationServicePlugIn
    {
        protected ILogServiceEx LogServiceEx { get; set; }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || !e.DataEntitys.Any())
            {
                this.Result.ComplexMessage.WarningMessages.Add("请选择一行或多行数据！");
                this.Result.IsSuccess = false;
                return;
            }
            var topCompanyInfo = this.Context.Companys.Find(t => t.CompanyId.EqualsIgnoreCase(this.Context.TopCompanyId));
            if (topCompanyInfo == null)
            {
                this.Result.ComplexMessage.WarningMessages.Add("当前用户没有总部组织权限，无法重试！");
                this.Result.IsSuccess = false;
                return;
            }

            this.Option.TryGetVariableValue("IsSystemRetry", out bool isSystemRetry);

            this.LogServiceEx = this.Container.GetService<ILogServiceEx>();

            //私钥
            var privateKey = this.GetAppConfig("jwt.PrivateKeyXml");
            DateTime tokenExpireAt = DateTime.Now.AddDays(1);
            //创建令牌
            var tokenId = JwtUtil.CreateJwtToken(this.Context, privateKey, tokenExpireAt, this.Context.UserName, this.Context.UserId, topCompanyInfo);

            string domain = "".GetAppConfig("ms.site.domain") ?? "";
            var targetServer = JsonClient.GetMyHomeService(this.GetCurrentRequest());
            targetServer.TokenId = tokenId;
            targetServer.Headers["Authorization"] = "Bearer " + tokenId;

            var dataEntitys = e.DataEntitys;
            if (this.HtmlForm.Id.EqualsIgnoreCase("si_simpleasynclog"))
            {
                //数据转换
                dataEntitys = this.Context.LoadBizDataById("si_asyncoperationlog", e.DataEntitys.Select(t => Convert.ToString(t["id"])), false).ToArray();
            }
            var requestGroup = dataEntitys.GroupBy(t => Convert.ToString(t["freqapi"]));//按接口进行分组
            int successCount = 0;
            List<DynamicObject> saveData = new List<DynamicObject>();
            foreach (var group in requestGroup)
            {
                var orderObjs = group.OrderBy(t => Convert.ToDateTime(t["fmsreqtime"]));//按照慕思请求时间排序
                foreach (var obj in orderObjs)
                {
                    Type T = Type.GetType(Convert.ToString(obj["fapidtotype"]));
                    var reqDto = JsonConvert.DeserializeObject(Convert.ToString(obj["freqcontent"]), T);
                    SetIsRetry(reqDto);
                    var fremarks = $"用户【{this.Context.DisplayName}（{this.Context.UserName}）】在{DateTime.Now}操作重试执行成功！";
                    try
                    {
                        var rep = JsonClient.InvokeThird<object, BaseResponse<object>>(targetServer, reqDto);
                        if (rep.Success)
                        {
                            successCount++;//记录重试成功数量
                            obj["freqstatus"] = "2";
                            obj["fremarks"] = "业务处理成功：" + rep.ToJson();
                            fremarks += "业务处理成功：" + rep.ToJson();
                        }
                        else
                        {
                            obj["freqstatus"] = "3";
                            obj["fremarks"] = "业务处理失败：" + rep.ToJson();
                            fremarks += "业务处理失败：" + rep.ToJson();

                            this.Result.ComplexMessage.ErrorMessages.Add($"请求唯一主键(短)【{obj["fshortuniquepk"]}】重试失败：" + rep.ToJson());
                        }
                    }
                    catch (Exception ex)
                    {
                        this.LogServiceEx.Error($"{this.HtmlForm.Caption}{this.OperationName}异常", ex);

                        obj["freqstatus"] = "3";
                        obj["fremarks"] = "业务处理失败：" + ex.Message + Environment.NewLine + ex.StackTrace;
                        fremarks += "业务处理失败：" + ex.Message + Environment.NewLine + ex.StackTrace;

                        this.Result.ComplexMessage.ErrorMessages.Add($"请求唯一主键(短)【{obj["fshortuniquepk"]}】异常失败：{ex.Message}");
                    }

                    if (isSystemRetry)
                    {
                        obj["fsysretrycount"] = Convert.ToInt32(obj["fsysretrycount"]) + 1;// 系统重试次数
                        if (obj["fstarthandletime"] == null)
                        {
                            obj["fstarthandletime"] = DateTime.Now;//记录开始处理时间
                            obj["fstarthandletime_t"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                        }
                        if (obj["freqfinishtime"] == null)
                        {
                            obj["freqfinishtime"] = DateTime.Now;//记录完成时间
                            obj["freqfinishtime_t"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                        }
                        this.LogServiceEx.Info($"接口计划任务：{fremarks}");
                    }
                    else
                    {
                        obj["fmanualprocess"] = "1";//标记人工处理
                        obj["fretrycount"] = Convert.ToInt32(obj["fretrycount"]) + 1;//重试次数+1
                        obj["fremarks"] = fremarks;
                    }

                    saveData.Add(obj);
                }
            }
            if (saveData.Any())
            {
                this.Context.SaveBizData(this.HtmlForm.Id, saveData);
            }
            var totalCount = e.DataEntitys.Count();
            this.Result.SimpleMessage = $"共{totalCount}个请求发起重试，成功{successCount}个，失败{totalCount - successCount}个！";
            this.AddRefreshPageAction();
        }

        /// <summary>
        /// 发送请求
        /// </summary>
        /// <typeparam name="TResponse"></typeparam>
        /// <typeparam name="TRequest"></typeparam>
        /// <param name="url"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        private TResponse Send<TRequest, TResponse>(TargetServer targetServer, TRequest request)
            where TResponse : class
            where TRequest : class
        {

            var resp = JsonClient.InvokeThird<TRequest, TResponse>(targetServer, request);

            return resp;
        }

        private void SetIsRetry(object obj, string propertyName = "IsRetry")
        {
            Type objType = obj.GetType();
            PropertyInfo propertyInfo = objType.GetProperty(propertyName);

            if (propertyInfo == null)
                return;

            propertyInfo.SetValue(obj, true, null);
        }
    }
}
