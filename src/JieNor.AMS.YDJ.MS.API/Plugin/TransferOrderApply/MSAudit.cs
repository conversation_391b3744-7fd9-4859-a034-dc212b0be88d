using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataEntity.BillType;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.MS.API.Plugin.TransferOrderApply
{
    /// <summary>
    /// 转单申请单：审核通过
    /// </summary>
    [InjectService]
    [FormId("ydj_transferorderapply")]
    [OperationNo("MSAudit")]
    public class MSAudit : AbstractOperationServicePlugIn
    {
        private HtmlForm OrderForm { get; set; }

        private const string OrderFormId = "ydj_order";
        private const string CustomerFormId = "ydj_customer";
        private const string SupplierFormId = "ydj_supplier";
        private const string BuildingFormId = "ydj_building";
        private const string SaleTransferOrderBillType = "ydj_saletransferorder_01";

        /// <summary>
        /// 业务经销商上下文（经销商id->业务经销商上下文） 
        /// </summary>
        protected Dictionary<string, UserContext> BizAgentContexts = new Dictionary<string, UserContext>();

        /// <summary>
        /// 接单方经销商上下文（经销商id->经销商上下文）
        /// </summary>
        protected Dictionary<string, UserContext> ReceiverAgentContexts = new Dictionary<string, UserContext>();

        protected Dictionary<string, string> bizAgents = new Dictionary<string, string>();

        [InjectProperty]
        protected IPrepareSaveDataService PrepareSaveDataService { get; set; }

        /// <summary>
        /// 新渠道系列
        /// </summary>
        protected Dictionary<string, DynamicObject> newChannelSeries = new Dictionary<string, DynamicObject>();

        /// <summary>
        /// 初始化操作上下文数据
        /// </summary>
        /// <param name="e"></param>
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            // 初始化业务经销商上下文
            bizAgents = GetBizAgents(e.DataEntitys);
            foreach (var bizAgent in bizAgents)
            {
                BizAgentContexts[bizAgent.Key] = this.Context.CreateAgentDBContext(bizAgent.Value);
            }
            // 初始化接单方经销商上下文
            foreach (var dataEntity in e.DataEntitys)
            {
                string receiverAgentId = Convert.ToString(dataEntity["freceiveragentid"]);
                if (ReceiverAgentContexts.ContainsKey(receiverAgentId)) continue;

                ReceiverAgentContexts[receiverAgentId] = this.Context.CreateAgentDBContext(receiverAgentId);
            }
            string seriessql = "select fid, fnumber, fbrandid from t_ydj_series with(nolock) where fisnewchannel = '1' and fforbidstatus='0'";
             newChannelSeries = this.Context.ExecuteDynamicObject(seriessql.ToString(), null).ToDictionary(s => s["fid"].ToString(), s => s);
        }

        /// <summary>
        /// 获取经销商->业务经销商映射
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private Dictionary<string, string> GetBizAgents(IEnumerable<DynamicObject> dataEntities)
        {
            HashSet<string> agentIds = new HashSet<string>();

            foreach (var dataEntity in dataEntities)
            {
                var fshipperagentid = Convert.ToString(dataEntity["fshipperagentid"]);
                if (!fshipperagentid.IsNullOrEmptyOrWhiteSpace()) agentIds.Add(fshipperagentid);

                var ftargetagentId = Convert.ToString(dataEntity["ftargetagentId"]);
                if (!ftargetagentId.IsNullOrEmptyOrWhiteSpace()) agentIds.Add(ftargetagentId);

                var freceiveragentId = Convert.ToString(dataEntity["freceiveragentid"]);
                if (!freceiveragentId.IsNullOrEmptyOrWhiteSpace()) agentIds.Add(freceiveragentId);
            }

            // 获取经销商->业务经销商映射
            var bizAgents = this.Container.GetService<IAgentService>().GetBizAgentIdByIds(this.Context, agentIds);
            return bizAgents;
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }

            // 从数据库捞旧数据
            var transferApplysFromDB =
                this.Context.LoadBizDataById(this.HtmlForm.Id, e.DataEntitys.Select(s => Convert.ToString(s["id"])));
            var lstValidObjs = new List<DynamicObject>();
            foreach (var dataEntity in e.DataEntitys)
            {
                var id = Convert.ToString(dataEntity["id"]);
                var fnumber = Convert.ToString(dataEntity["fbillno"]);
                var transferApplyFromDB = transferApplysFromDB.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(id));

                var ftransferstatus = Convert.ToString(transferApplyFromDB["ftransferstatus"]);

                if (ftransferstatus.EqualsIgnoreCase("2"))
                {
                    this.Result.ComplexMessage.WarningMessages.Add("单据编号为【{0}】的转单申请单，转单状态已审批通过，不允许重复审批通过！".Fmt(fnumber));
                    continue;
                }

                if (ftransferstatus.EqualsIgnoreCase("3"))
                {
                    this.Result.ComplexMessage.WarningMessages.Add("单据编号为【{0}】的转单申请单，转单状态已审批驳回，请重新提交转单申请！".Fmt(fnumber));
                    continue;
                }

                lstValidObjs.Add(dataEntity);
            }

            e.DataEntitys = lstValidObjs.ToArray();

            if (e.DataEntitys.Length == 0)
            {
                return;
            }

            this.OrderForm = this.MetaModelService.LoadFormModel(this.Context, OrderFormId);
            RewriteOrder(e.DataEntitys);
            AuditTransferOrderApply(e.DataEntitys);

            //bool isZZAgent = false;
            //foreach (var group in e.DataEntitys.GroupBy(s => Convert.ToString(s["fshipperagentid"])))
            //{
            //    foreach (var apply in group)
            //    {
            //        var freceiveragentid = Convert.ToString(apply["freceiveragentid"]);//接单方经销商
            //        var fshipperagentid = Convert.ToString(apply["fshipperagentid"]);//发货方经销商Id
            //        if (checkmaconfig(freceiveragentid, fshipperagentid))
            //        {
            //            isZZAgent = true;
            //            //当发货方=接单方，不重新生成销售合同
            //            continue;
            //        }
            //    }
            //}
            //if (isZZAgent)
            //{
            //    //发货方和接单方相同
            //    SaveTransferOrderApply(e.DataEntitys);
            //    // 返回操作成功的单据
            //    this.Result.IsSuccess = true;
            //    this.Result.SrvData = e.DataEntitys.Select(s => new InternalSuccessBillData(s)).ToList();
            //    return;
            //}

            var allShipperOrders = new List<DynamicObject>();
            //这里的e.DataEntitys是上游传送的数据包:转单申请单(批量)

            //加载引用数据
            var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, e.DataEntitys[0].DynamicObjectType, e.DataEntitys, false);

            foreach (var group in e.DataEntitys.GroupBy(s => Convert.ToString(s["fshipperagentid"])))
            {
                var shipperOrders = new List<DynamicObject>();

                // 业务经销商->转单集合
                var bizTargetAgentTransferOrderApplyMap = new Dictionary<UserContext, List<DynamicObject>>();

                foreach (var apply in group)
                {
                    var sourceOrderId = Convert.ToString(apply["fsourcenumber"]);//接单方合同ID
                    var freceiveragentid = Convert.ToString(apply["freceiveragentid"]);//接单方经销商
                    var fshipperagentid = Convert.ToString(apply["fshipperagentid"]);//发货方经销商Id
                    //if (checkmaconfig(freceiveragentid, fshipperagentid))
                    //{
                    //    //当发货方=接单方，不重新生成销售合同
                    //    continue;
                    //}

                    // 将发货方经销商创建为接单方经销商的供应商

                    // 接单方经销商上下文
                    var receiverAgentCtx = ReceiverAgentContexts[freceiveragentid];

                    var freceiveragentid_ref = apply["freceiveragentid_ref"] as DynamicObject;//接单方经销商
                    /*
                       1.接单经销商，发起转单，总台审批通过，发货方=接单方
                       2.目标经销商并未创建对应的《客户》信息（接单经销商）
                       3.接单经销商并未创建对应的《供应商》信息（目标经销商）
                        */
                    var targetAgent = new AgentBriefInfo()
                    {
                        AgentId = Convert.ToString(apply["ftargetagentId"]),
                        AgentNo = Convert.ToString(apply["ftargetagentno"]),
                        AgentName = Convert.ToString(apply["ftargetagentname"]),
                    };//目标方经销商
                    var receiverAgent = new AgentBriefInfo()
                    {
                        AgentId = Convert.ToString(freceiveragentid_ref["id"]),
                        AgentNo = Convert.ToString(freceiveragentid_ref["fnumber"]),
                        AgentName = Convert.ToString(freceiveragentid_ref["fname"]),
                    };//接单方经销商
                    var shipperAgent = new AgentBriefInfo()
                    {
                        AgentId = Convert.ToString(apply["fshipperagentid"]),
                        AgentNo = Convert.ToString(apply["fshipperagentno"]),
                        AgentName = Convert.ToString(apply["fshipperagentname"]),
                    };//发货方经销商     

                    if (!apply["fisoffline"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(apply["fisoffline"]))//线下转单不自动创建供应商信息和生成应付单
                    {
                        //创建接单方为目标经销商的供应商
                        CreateAgentSupplierOrCustomerBaseData(receiverAgentCtx, apply, targetAgent, receiverAgent, SupplierFormId);
                        continue;
                    }

                    // 发货经销商
                    var shipperAgentId = group.Key;
                    // 目标经销商
                    var targetAgentid = targetAgent.AgentId;

                    // 业务发货经销商的上下文
                    BizAgentContexts.TryGetValue(shipperAgentId, out var bizShipperAgentCtx);
                    // 业务目标经销商上下文
                    BizAgentContexts.TryGetValue(targetAgentid, out var bizTargetAgentCtx);

                    if (bizShipperAgentCtx == null || bizTargetAgentCtx == null)
                    {
                        throw new BusinessException("非线下转单的发货经销商和目标经销商不可为空或不存在！");
                    }

                    if (fshipperagentid == freceiveragentid && bizAgents[fshipperagentid] == bizAgents[freceiveragentid])
                    {
                        CreateAgentSupplierOrCustomerBaseData(receiverAgentCtx, apply, targetAgent, receiverAgent, SupplierFormId);
                        CreateAgentSupplierOrCustomerBaseData(bizTargetAgentCtx, apply, receiverAgent, targetAgent, CustomerFormId);
                    }
                    else
                    {
                        // 将接单方经销商创建为发货方经销商的客户
                        CreateAgentSupplierOrCustomerBaseData(bizShipperAgentCtx, apply, receiverAgent, shipperAgent, CustomerFormId);
                        CreateAgentSupplierOrCustomerBaseData(receiverAgentCtx, apply, shipperAgent, receiverAgent, SupplierFormId);
                    }

                    var entries = apply["fentry"] as DynamicObjectCollection;
                    var sourceEntryId = Convert.ToString(entries.FirstOrDefault()?["fsourcenentryid"]);//转单明细ID

                    // 检查这个发货方经销商的这张合同是否已经生成过,若已生成则合并到同一张《销售合同》 
                    var shipperOrder = bizShipperAgentCtx.LoadBizDataByFilter(OrderFormId, $" fissaletransferorder='1' and freceivercontractnumber='{sourceOrderId}' ",true).FirstOrDefault();
                    if (shipperOrder != null)
                    {
                        var orderEntries = shipperOrder["fentry"] as DynamicObjectCollection;
                        if (!orderEntries.IsNullOrEmpty())
                        {
                            var receiverOrderEntry = GetReceiverAgentOrderEntry(freceiveragentid, sourceOrderId, sourceEntryId);
                            if (receiverOrderEntry != null)
                            {
                                apply["fshippercontractnumber"] = shipperOrder["fbillno"];
                                //var newfentry = GenerateShipperAgentOrderEntry(bizShipperAgentCtx, orderEntries, receiverOrderEntry, shipperOrder);
                                //所对应的《转单申请单》财务信息的[目标经销商总额] /《销售合同》商品行的[销售数量]
                                var newfentry = GenerateShipperAgentOrderEntry(bizShipperAgentCtx, orderEntries, receiverOrderEntry, apply);
                                orderEntries.Add(newfentry);
                                //bizShipperAgentCtx.SaveBizData(OrderFormId, shipperOrder);
                            }
                        }
                        //查询是否有新渠道系列，有则需要把接单方的销售合同合作渠道同步给送货方
                        var allProductSeries= orderEntries.Select(p=>(p["fproductid_ref"] as DynamicObject)?["fseriesid"]).ToList();
                        if (newChannelSeries.Keys.Any(key => allProductSeries.Contains(key))) {
                            if (receiverAgentCtx == null)
                            {
                                throw new BusinessException("接单方方经销商上下文不存在");
                            }
                            DynamicObject order  = receiverAgentCtx.LoadBizDataByNo(OrderFormId, "fbillno", new List<string>() { sourceOrderId }, true)?.FirstOrDefault();
                            if (order != null) {
                                shipperOrder["fchannel"] = order["fchannel"];
                            }
                        }
                        this.MergeShipperOrderAmount(shipperOrder, apply);
                    }
                    else
                    {
                        /*
                     1.接单方与发货方不是同一人时:需要生成发货方的《销售合同》
                     2.接单方与发货方是同一人时:
                        a.接单方《销售合同》商品明细中【目标经销商】和【送货方经销商】均反写为：【接单方经销商】；
                        b.接单方的《转单申请单》上发货合同编号为【接单方合同编号】
                     3.自动创建【目标经销商】的转单申请单
                     */
                        if (fshipperagentid != freceiveragentid && bizAgents[fshipperagentid] != bizAgents[freceiveragentid])
                        {
                            shipperOrder = GenerateShipperAgentOrder(apply);
                            //bizShipperAgentCtx.SaveBizData(OrderFormId, shipperOrder);
                            shipperOrders.Add(shipperOrder);
                        }
                        //此处屏蔽，逻辑放下面了，避免不必要的保存
                        //else
                        //{
                        //    apply["fshippercontractnumber"] = apply["freceivercontractnumber"];
                        //    this.Context.SaveBizData(this.HtmlForm.Id, apply);

                        //    var orderNos = e.DataEntitys.Where(s => Convert.ToString(s["fsourcetype"]).EqualsIgnoreCase(OrderFormId)).Select(s => Convert.ToString(s["fsourcenumber"])).Distinct();
                        //    shipperOrder = this.Context.LoadBizDataByNo(this.OrderForm.Id, "fbillno", orderNos).FirstOrDefault();

                        //    // 发货方=接单方，使用接单方上下文（当前上下文）作为发货方上下文
                        //    bizShipperAgentCtx = this.Context;
                        //}
                    }

                    if (shipperOrder == null)
                    {
                        apply["fshippercontractnumber"] = apply["freceivercontractnumber"];
                        //this.Context.SaveBizData(this.HtmlForm.Id, apply);
                        this.Gateway.InvokeBillOperation(Context,
                        this.HtmlForm.Id,
                        new List<DynamicObject> { apply },
                        "draft",
                        new Dictionary<string, object>());

                        var orderNos = e.DataEntitys.Where(s => Convert.ToString(s["fsourcetype"]).EqualsIgnoreCase(OrderFormId)).Select(s => Convert.ToString(s["fsourcenumber"])).Distinct();
                        shipperOrder = this.Context.LoadBizDataByNo(this.OrderForm.Id, "fbillno", orderNos).FirstOrDefault();

                        // 发货方=接单方，使用接单方上下文（当前上下文）作为发货方上下文
                        bizShipperAgentCtx = this.Context;
                    }
                    else
                    {
                        // 自动计算送货方财务信息，注意用户上下文
                        AutoCalculateShipperFinancialInfo(bizShipperAgentCtx, shipperOrder);
                    }
                    //接单方合同 统一放后面处理
                    var receiverOrder = this.Context.LoadBizDataByNo(this.OrderForm.Id, "fbillno", new[] { Convert.ToString(apply["fsourcenumber"]) }).FirstOrDefault();
                    var receiverOrderEntrys = receiverOrder["fentry"] as DynamicObjectCollection;
                    string fsourcenentryid = Convert.ToString((apply["fentry"] as DynamicObjectCollection)?.FirstOrDefault()?["fsourcenentryid"]);
                    //处理未全部审批完的转单申请单、销售合同
                    AlterOrderAndApply(shipperOrder, receiverOrderEntrys, apply, fsourcenentryid);

                    // 生成目标方经销商经的转单申请单
                    var targetTransferOrderApply = GenerateTargetAgentTransferOrderApply(apply, shipperOrder);

                    // 反写【发货方合同编号】
                    targetTransferOrderApply["fshippercontractnumber"] = shipperOrder["fbillno"];
                    targetTransferOrderApply["fsourcenumber"] = shipperOrder["fbillno"];

                    bizTargetAgentTransferOrderApplyMap.TryGetValue(bizTargetAgentCtx, out var targetTransferOrderApplys);
                    if (targetTransferOrderApplys == null)
                    {
                        targetTransferOrderApplys = new List<DynamicObject>();
                        bizTargetAgentTransferOrderApplyMap[bizTargetAgentCtx] = targetTransferOrderApplys;
                    }

                    targetTransferOrderApplys.Add(targetTransferOrderApply);
                }

                // 按业务目标经销商保存转单
                foreach (var item in bizTargetAgentTransferOrderApplyMap)
                {
                    //item.Key.SaveBizData(this.HtmlForm.Id, item.Value);
                    this.Gateway.InvokeBillOperation(item.Key,
                        this.HtmlForm.Id,
                        item.Value,
                        "draft",
                        new Dictionary<string, object>());
                }

                // 用于反写
                allShipperOrders.AddRange(shipperOrders);
            }

            SaveTransferOrderApply(e.DataEntitys, allShipperOrders);

            //// 返回操作成功的单据
            //this.Result.IsSuccess = true;
            //this.Result.SrvData = e.DataEntitys.Select(s => new InternalSuccessBillData(s)).ToList();
        }

        /// <summary>
        /// 合并送货方销售合同的金额【发货结算金额】【成本价格】【其他费用】
        /// </summary>
        /// <param name="shipperOrder">送货方销售合同</param>
        /// <param name="apply">转单申请</param>
        private void MergeShipperOrderAmount(DynamicObject shipperOrder, DynamicObject apply)
        {
            shipperOrder["ftargetagentamount"] = Convert.ToDecimal(shipperOrder["ftargetagentamount"]) + Convert.ToDecimal(apply["ftargetagentamount"]);
            shipperOrder["ftransfercostprice"] = Convert.ToDecimal(shipperOrder["ftransfercostprice"]) + Convert.ToDecimal(apply["fcostprice"]);
            shipperOrder["fotherfee"] = Convert.ToDecimal(shipperOrder["fotherfee"]) + Convert.ToDecimal(apply["fotherfee"]);
        }

        /// <summary>
        /// 自动计算送货方财务信息
        /// </summary>
        /// <param name="shipperAgentCtx"></param>
        /// <param name="order"></param>
        private void AutoCalculateShipperFinancialInfo(UserContext shipperAgentCtx, DynamicObject order)
        {
            var entries = order["fentry"] as DynamicObjectCollection;
            var isSaleTransferOrder = Convert.ToString(order["fissaletransferorder"]);
            if (!isSaleTransferOrder.IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(isSaleTransferOrder) && entries.Any())
            {
                //计算合同结算信息
                var OrderService = this.Container.GetService<IOrderService>();
                var metaModelService = this.Container.GetService<IMetaModelService>();
                var orderHtmlForm = metaModelService.LoadFormModel(shipperAgentCtx, "ydj_order");
                OrderService.CalculateSettlement(shipperAgentCtx, order, orderHtmlForm);

                //shipperAgentCtx.SaveBizData(OrderFormId, order);
                var deptid = Convert.ToString(order["fdeptid"]);
                if (!deptid.IsNullOrEmptyOrWhiteSpace())
                {
                    var fstoreid = Convert.ToString(shipperAgentCtx.LoadBizBillHeadDataById("ydj_dept", deptid, "fstore")?["fstore"]);
                    if (!fstoreid.IsNullOrEmptyOrWhiteSpace())
                    {
                        order["fstore"] = fstoreid;
                    }
                }
                this.Gateway.InvokeBillOperation(shipperAgentCtx,
                OrderFormId,
                new List<DynamicObject> { order },
                "draft",
                new Dictionary<string, object>());
            }
        }

        /// <summary>
        /// 获取接单方经销商销售合同明细
        /// </summary>
        /// <param name="receiverAgentId"></param>
        /// <param name="orderNo"></param>
        /// <param name="entryId"></param>
        /// <returns></returns>
        private DynamicObject GetReceiverAgentOrderEntry(string receiverAgentId, string orderNo, string entryId)
        {
            var receiverAgentCtx = ReceiverAgentContexts[receiverAgentId];
            if (receiverAgentCtx == null)
            {
                throw new BusinessException("接单方方经销商上下文不存在");
            }
            List<DynamicObject> orders = orders = receiverAgentCtx.LoadBizDataByNo(OrderFormId, "fbillno", new List<string>() { orderNo }, true);
            if (orders == null || orders.Count == 0)
            {
                throw new WarnException("接单方销售合同不存在");
            }
            DynamicObject order = orders.FirstOrDefault();
            DynamicObject secondOrd = null;
            //是否是需转单的二级分销合同
            bool isNeedTransferResellOrd = Convert.ToBoolean(order["fneedtransferorder"]) && Convert.ToBoolean(order["fisresellorder"]);
            //如果是需转单二级分销合同那么价格相关需要取上级采购的上级合同的相关明细行数据
            DynamicObject purOrder = null;
            if (isNeedTransferResellOrd)
            {
                purOrder = receiverAgentCtx.LoadBizDataById("ydj_purchaseorder", Convert.ToString(order["fsourceid"]));
                if (purOrder == null)
                {
                    throw new WarnException("接单方销售合同对应的二级分销采购订单不存在");
                }
                //目前不支持多合同下二级采购后提交一级，这里拿明细行的源合同做处理
                var secondOrdId = (purOrder["fentity"] as DynamicObjectCollection).FirstOrDefault(x => !x["fsourceinterid"].IsNullOrEmptyOrWhiteSpace())?["fsourceinterid"];
                if (secondOrdId.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new WarnException("接单方销售合同对应的二级分销合同不存在");
                }
                secondOrd = receiverAgentCtx.LoadBizDataById("ydj_order", Convert.ToString(secondOrdId), true);
                if (secondOrd == null)
                {
                    throw new WarnException("接单方销售合同对应的二级分销合同单据不存在");
                }
            }
            //var orderHtmlForm = this.MetaModelService?.LoadFormModel(receiverAgentCtx, OrderFormId);
            //receiverAgentCtx.Container.GetService<LoadReferenceObjectManager>()?.Load(receiverAgentCtx, orderHtmlForm.GetDynamicObjectType(receiverAgentCtx), order, false); //加载引用数据
            var entries = order["fentry"] as DynamicObjectCollection;
            if (entries == null || entries.Count() == 0)
            {
                throw new WarnException($"接单方{(isNeedTransferResellOrd ? "分销" : string.Empty)}销售合同明细{entryId}不存在");
            }
            var result = entries.FirstOrDefault(x => Convert.ToString((object)x["id"]).EqualsIgnoreCase(entryId));
            if (isNeedTransferResellOrd)
            {
                //匹配二级分销合同明细行id
                var sourcePurEntryId = result["fsourceentryid_e"];
                var sourceSecondOrdEntryId = (purOrder["fentity"] as DynamicObjectCollection).FirstOrDefault(x => Convert.ToString(x["id"]) == Convert.ToString(sourcePurEntryId))["fsourceentryid_e"];
                var secentries = secondOrd["fentry"] as DynamicObjectCollection;
                var secresult = secentries.FirstOrDefault(x => Convert.ToString((object)x["id"]).EqualsIgnoreCase(Convert.ToString(sourceSecondOrdEntryId)));
                //找到二级合同明细，将价格信息给一级明细，使发货方销售合同所获取到的商品价格信息都是获取二级分销商《销售合同》的商品明细的价格
                result["fbizqty"] = secresult["fbizqty"];
                result["fsubqty"] = secresult["fsubqty"];
                result["fhqprice"] = secresult["fhqprice"];
                result["fterprice"] = secresult["fterprice"];
                result["fteramount"] = secresult["fteramount"];
                result["fprice"] = secresult["fprice"];
                result["fcost"] = secresult["fcost"];
                result["fcostprice"] = secresult["fcostprice"];
                result["fdistrateraw"] = secresult["fdistrateraw"];
                result["famount"] = secresult["famount"];
                result["fdistrate"] = secresult["fdistrate"];
                result["fdistamount"] = secresult["fdistamount"];
                result["fdealprice"] = secresult["fdealprice"];
                result["fdealamount"] = secresult["fdealamount"];
                result["fqty"] = secresult["fqty"];
            }
            return result;
        }

        /// <summary>
        /// 生成发货方经销商销售合同
        /// </summary>
        /// <param name="receiverTransferOrderApply">接单方转单申请单</param>
        private DynamicObject GenerateShipperAgentOrder(DynamicObject receiverTransferOrderApply)
        {
            var receiverSourcenumber = Convert.ToString(receiverTransferOrderApply["fsourcenumber"]);//接单方销售合同编号 
            var freceiveragentid = Convert.ToString(receiverTransferOrderApply["freceiveragentid"]);//接单方经销商  
            var fshipperagentid = Convert.ToString(receiverTransferOrderApply["fshipperagentid"]);//发货方经销商

            // 接单方合同
            var receiverOrder = this.Context.LoadBizDataByNo(this.OrderForm.Id, "fbillno", new[] { receiverSourcenumber }, true).FirstOrDefault();
            if (receiverOrder == null)
            {
                throw new WarnException($"销售合同【{receiverSourcenumber}】不存在！");
            }
            var receiverAgentCtx = ReceiverAgentContexts[freceiveragentid];
            if (receiverAgentCtx == null)
            {
                throw new BusinessException("接单方方经销商上下文不存在");
            }
            var bizShipperAgentCtx = BizAgentContexts[fshipperagentid];
            if (bizShipperAgentCtx == null)
            {
                throw new BusinessException("送货方经销商上下文不存在");
            }
            var billTypeObj = bizShipperAgentCtx.GetBillTypeByBizObject(OrderFormId, SaleTransferOrderBillType);
            if (billTypeObj == null)
            {
                throw new BusinessException($"当前发货方销售转单单据类型不存在！");
            }
            //终端客户
            var customer = CreateShipperAgentBaseData(receiverAgentCtx, bizShipperAgentCtx, CustomerFormId, Convert.ToString(receiverOrder["fcustomerid"]), receiverOrder, false);
            //获取客户(接单方经销商)
            var customer_ag = CreateShipperAgentBaseData(receiverAgentCtx, bizShipperAgentCtx, CustomerFormId, freceiveragentid, receiverOrder, true);
            var building = CreateShipperAgentBaseData(receiverAgentCtx, bizShipperAgentCtx, BuildingFormId, Convert.ToString(receiverOrder["fbuildingid"]), receiverOrder);

            ////将发货方经销商创建为接单方经销商的 供应商
            //CreateAgentSupplierOrCustomerBaseData(receiverAgentCtx, receiverTransferOrderApply, fshipperagentid, freceiveragentid, SupplierFormId);
            #region 需转单二级分销合同逻辑
            DynamicObject secondOrd = null;
            //是否是需转单的二级分销合同
            bool isNeedTransferResellOrd = Convert.ToBoolean(receiverOrder["fneedtransferorder"]) && Convert.ToBoolean(receiverOrder["fisresellorder"]);
            //如果是需转单二级分销合同那么价格相关需要取上级采购的上级合同的相关明细行数据
            DynamicObject purOrder = null;
            if (isNeedTransferResellOrd)
            {
                purOrder = receiverAgentCtx.LoadBizDataById("ydj_purchaseorder", Convert.ToString(receiverOrder["fsourceid"]));
                if (purOrder == null)
                {
                    throw new WarnException("接单方销售合同对应的二级分销采购订单不存在");
                }
                //目前不支持多合同下二级采购后提交一级，这里拿明细行的源合同做处理
                var secondOrdId = (purOrder["fentity"] as DynamicObjectCollection).FirstOrDefault(x => !x["fsourceinterid"].IsNullOrEmptyOrWhiteSpace())?["fsourceinterid"];
                if (secondOrdId.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new WarnException("接单方销售合同对应的二级分销合同不存在");
                }
                secondOrd = receiverAgentCtx.LoadBizDataById("ydj_order", Convert.ToString(secondOrdId), true);
                if (secondOrd == null)
                {
                    throw new WarnException("接单方销售合同对应的二级分销合同单据不存在");
                }
            }
            #endregion

            //创建发货合同
            var shipperOrder = (DynamicObject)receiverOrder.Clone();
            ResetOrderInfo(shipperOrder, billTypeObj);
            //客户取值来源：接单方经销商
            if (customer_ag != null)
            {
                shipperOrder["fcustomerid"] = customer_ag?["id"];
                shipperOrder["fcustomersource"] = customer_ag?["fsource"];
            }
            //终端客户取值来源：接单方客户
            if (customer != null)
            {
                shipperOrder["fterminalcustomer"] = customer["id"];
                shipperOrder["fcontacts_c"] = customer["fname"];

                shipperOrder["fcoophone"] = customer["fphone"];
                shipperOrder["fprovince_c"] = customer["fprovince"];
                shipperOrder["fcity_c"] = customer["fcity"];
                shipperOrder["fregion_c"] = customer["fregion"];
                shipperOrder["fcooaddress"] = customer["faddress"];
                //0712 调整：客户下的基本信息 也取关联客户的信息
                var sqlText = $@"
                select top 1 t1.fid,t2.fcuscontacttryid fcustomercontactid 
                from t_ydj_customer t1 with(nolock) 
                left join t_ydj_fcuscontacttry t2 with(nolock) on t1.fid=t2.fid 
                where t1.fid='{customer["id"]}' ";
                var customerObj = this.DBService.ExecuteDynamicObject(this.Context, sqlText).FirstOrDefault();
                shipperOrder["fcustomercontactid"] = customerObj?["fcustomercontactid"];
                shipperOrder["fphone"] = customer?["fphone"];
                shipperOrder["fprovince"] = customer?["fprovince"];
                shipperOrder["fcity"] = customer?["fcity"];
                shipperOrder["fregion"] = customer?["fregion"];
                shipperOrder["faddress"] = customer?["faddress"];

            }
            shipperOrder["fmemberdesc"] = receiverOrder["fmemberdesc"];

            //销售员明细 
            var fdutyentry = shipperOrder["fdutyentry"] as DynamicObjectCollection;
            fdutyentry.Clear();

            //商品信息
            var shipperOrderEntrys = shipperOrder["fentry"] as DynamicObjectCollection;
            var receiverOrderEntrys = receiverOrder["fentry"] as DynamicObjectCollection;
            // 通过【源单明细内码】查找商品明细行
            string fsourcenentryid = Convert.ToString((receiverTransferOrderApply["fentry"] as DynamicObjectCollection)?.FirstOrDefault()?["fsourcenentryid"]);
            DynamicObject receiverOrderEntry = receiverOrderEntrys?.FirstOrDefault(x => Convert.ToString(x["id"]) == fsourcenentryid);
            if (isNeedTransferResellOrd)
            {
                //匹配二级分销合同明细行id
                var sourcePurEntryId = receiverOrderEntry["fsourceentryid_e"];
                var sourceSecondOrdEntryId = (purOrder["fentity"] as DynamicObjectCollection).FirstOrDefault(x => Convert.ToString(x["id"]) == Convert.ToString(sourcePurEntryId))["fsourceentryid_e"];
                var secentries = secondOrd["fentry"] as DynamicObjectCollection;
                var secresult = secentries.FirstOrDefault(x => Convert.ToString((object)x["id"]).EqualsIgnoreCase(Convert.ToString(sourceSecondOrdEntryId)));
                //找到二级合同明细，将价格信息给一级明细，使发货方销售合同所获取到的商品价格信息都是获取二级分销商《销售合同》的商品明细的价格
                receiverOrderEntry["fbizqty"] = secresult["fbizqty"];
                receiverOrderEntry["fsubqty"] = secresult["fsubqty"];
                receiverOrderEntry["fhqprice"] = secresult["fhqprice"];
                receiverOrderEntry["fterprice"] = secresult["fterprice"];
                receiverOrderEntry["fteramount"] = secresult["fteramount"];
                receiverOrderEntry["fprice"] = secresult["fprice"];
                receiverOrderEntry["fcost"] = secresult["fcost"];
                receiverOrderEntry["fcostprice"] = secresult["fcostprice"];
                receiverOrderEntry["fdistrateraw"] = secresult["fdistrateraw"];
                receiverOrderEntry["famount"] = secresult["famount"];
                receiverOrderEntry["fdistrate"] = secresult["fdistrate"];
                receiverOrderEntry["fdistamount"] = secresult["fdistamount"];
                receiverOrderEntry["fdealprice"] = secresult["fdealprice"];
                receiverOrderEntry["fdealamount"] = secresult["fdealamount"];
                receiverOrderEntry["fqty"] = secresult["fqty"];
            }

            if (receiverOrderEntry == null)
            {
                throw new WarnException($"接单方销售合同【{receiverOrder["fbillno"]}】转单的商品明细行不存在！");
            }

            var basAgent = Context.LoadBizDataById("bas_agent", Convert.ToString(receiverTransferOrderApply["freceiveragentid"]));

            shipperOrderEntrys.Clear();
            //所对应的《转单申请单》财务信息的[目标经销商总额] /《销售合同》商品行的[销售数量]
            var newfentry = GenerateShipperAgentOrderEntry(bizShipperAgentCtx, shipperOrderEntrys, receiverOrderEntry, receiverTransferOrderApply);
            shipperOrderEntrys.Add(newfentry);
            var allProductSeries = shipperOrderEntrys.Select(p => (p["fproductid_ref"] as DynamicObject)?["fseriesid"]).ToList();
            if (newChannelSeries.Keys.Any(key => allProductSeries.Contains(key)))
            {
                shipperOrder["fchannel"] = receiverOrder["fchannel"];
            }
            //转单信息
            shipperOrder["freceivercontractnumber"] = receiverTransferOrderApply["freceivercontractnumber"];
            shipperOrder["freceivercontractid"] = receiverTransferOrderApply["freceivercontractid"];
            shipperOrder["freceiveragentid"] = receiverTransferOrderApply["freceiveragentid"];
            shipperOrder["freceiveragentno"] = basAgent?["fnumber"];
            shipperOrder["freceiveragent"] = basAgent?["fname"];
            shipperOrder["freceiverstore"] = receiverTransferOrderApply["freceiverstore"];
            shipperOrder["freceiversaler"] = receiverTransferOrderApply["freceiversaler"];//接单方销售员(联合开单)
            shipperOrder["freceiveragentcontactperson"] = receiverTransferOrderApply["freceiveragentcontactperson"];
            shipperOrder["freceivercontactpersonphone"] = receiverTransferOrderApply["freceivercontactpersonphone"];
            shipperOrder["ftransferdate"] = receiverTransferOrderApply["fpassdate"];
            shipperOrder["ftargetagentamount"] = receiverTransferOrderApply["ftargetagentamount"];
            shipperOrder["ftransfercostprice"] = receiverTransferOrderApply["fcostprice"];
            shipperOrder["fotherfee"] = receiverTransferOrderApply["fotherfee"];
            shipperOrder["ftransferremark"] = receiverTransferOrderApply["fremark"].IsNullOrEmptyOrWhiteSpace() ? string.Empty : receiverTransferOrderApply["fremark"];
            //处理未全部审批完的转单申请单、销售合同
            //AlterOrderAndApply(shipperOrder, receiverOrderEntrys, receiverTransferOrderApply, fsourcenentryid);

            this.PrepareSaveDataService.PrepareDataEntity(bizShipperAgentCtx, OrderForm, new[] { shipperOrder }, this.Option);

            return shipperOrder;
        }

        private void AlterOrderAndApply(DynamicObject shipperOrder, DynamicObjectCollection receiverOrderEntrys, DynamicObject receiverTransferOrderApply, string fsourcenentryid)
        {
            //to do 是否存在审批中的转单申请单？
            //1、如果存在：暂时作废当前生成的发货方合同、转单申请单。
            //2、如果不存在：所有接单方明细都已被处理（即：不存在审批中的转单申请）则所有关联发货方合同按最后一行状态做统一终审或者驳回。
            var IsExistUnFinished = receiverOrderEntrys.Any(o => Convert.ToString(o["ftransferorderstatus"]).EqualsIgnoreCase("1")
                                                            && !Convert.ToString(o["id"]).EqualsIgnoreCase(fsourcenentryid));
            if (IsExistUnFinished)
            {
                shipperOrder["fcancelstatus"] = "1";
                receiverTransferOrderApply["fcancelstatus"] = "1";
            }
            else
            {
                //如果不存在则说明是最后一个转单通过的申请明细，需要将前面作废的合同取消作废
                var receiverOrderEntrysID = receiverOrderEntrys.Select(o => Convert.ToString(o["id"])).ToList();
                UnCancelOrders(receiverOrderEntrysID, Convert.ToString(receiverTransferOrderApply["fbatchno"]));
                shipperOrder["fcancelstatus"] = "0";
                receiverTransferOrderApply["fcancelstatus"] = "0";
            }
        }

        /// <summary>
        /// 反作废发货方合同
        /// </summary>
        private void UnCancelOrders(List<string> receiverOrderEntrysID, string batchno)
        {
            var updateSqls = new List<string>();
            updateSqls.Add($@"/*dialect*/update od_ship SET od_ship.fcancelstatus ='0' from t_ydj_order as od_ship
                        inner join t_ydj_orderentry as od_shipmx on od_shipmx.fid = od_ship.fid
                        inner join t_ydj_orderentry as od_recmx on od_recmx.fentryid = od_shipmx.fsourceentryid_e
                        where od_recmx.fentryid in ({receiverOrderEntrysID.JoinEx(",", true)}) and od_ship.fcancelstatus = '1'; ");
            //发货方转单申请单
            updateSqls.Add($@"/*dialect*/update apply set apply.fcancelstatus ='0' from t_ydj_transferorderapply as apply  
                    inner join t_transferorderapply_pro as applymx on applymx.fid = apply.fid
                    inner join t_ydj_orderentry as od_shipmx on applymx.fsourcenentryid = od_shipmx.fentryid
                    inner join t_ydj_orderentry as od_recmx on od_recmx.fentryid = od_shipmx.fsourceentryid_e
                    where od_recmx.fentryid in ({receiverOrderEntrysID.JoinEx(",", true)}) and apply.fcancelstatus = '1'  and apply.fbatchno='{batchno}'; ");
            //接单方转单申请单
            updateSqls.Add($@"/*dialect*/update apply set apply.fcancelstatus ='0' FROM t_ydj_transferorderapply as apply  
                                inner join t_transferorderapply_pro as applymx on applymx.fid = apply.fid
                                inner join t_ydj_orderentry as od_recmx on od_recmx.fentryid = applymx.fsourcenentryid 
                                where od_recmx.fentryid in ({receiverOrderEntrysID.JoinEx(",", true)}) and apply.fcancelstatus = '1'  and apply.fbatchno='{batchno}' ");

            var dbServiceEx = this.Context.Container.GetService<IDBServiceEx>();
            dbServiceEx.ExecuteBatch(this.Context, updateSqls);
        }

        /// <summary>
        /// 重置销售合同信息
        /// </summary>
        /// <param name="shipperOrder"></param>
        private static void ResetOrderInfo(DynamicObject shipperOrder, BillTypeInfo billTypeObj)
        {
            shipperOrder["fisautogenerate"] = "1";//标记为：接口自动生成
            shipperOrder["fissaletransferorder"] = "1"; //标记为：销售转单 
            shipperOrder["fisresellorder"] = false; //二级分销合同标识不携带送货方合同上
            shipperOrder["fbillno"] = string.Empty;
            shipperOrder["fcustomercontactid"] = string.Empty;
            shipperOrder["fphone"] = string.Empty;
            shipperOrder["fprovince"] = string.Empty;
            shipperOrder["fcity"] = string.Empty;
            shipperOrder["fregion"] = string.Empty;
            shipperOrder["fflowinstanceid"] = string.Empty;
            shipperOrder["fmainorgid"] = string.Empty;
            shipperOrder["fbilltype"] = billTypeObj.fid; //销售转单 
            shipperOrder["fneedtransferorder"] = string.Empty;
            shipperOrder["fstaffid"] = string.Empty;
            shipperOrder["fdeptid"] = string.Empty;
            shipperOrder["fstore"] = string.Empty;
            shipperOrder["fdesignscheme"] = string.Empty;
            shipperOrder["fstylistid"] = string.Empty;
            shipperOrder["fscalerecord"] = string.Empty;
            shipperOrder["finnercustomerid"] = string.Empty;
            shipperOrder["fchannel"] = string.Empty;
            shipperOrder["finvoiceamount"] = 0;
            shipperOrder["fsourcetype"] = OrderFormId;
            shipperOrder["fsourcenumber"] = string.Empty;
            shipperOrder["flockstate"] = string.Empty;
            shipperOrder["flockdate"] = null;
            shipperOrder["fcustomerid"] = string.Empty;
            shipperOrder["ftranid"] = string.Empty;

            shipperOrder["fstatus"] = BillStatus.B.ToString(); //默认为创建状态
            shipperOrder["fchangestatus"] = string.Empty;
            shipperOrder["fcreatorid"] = string.Empty;
            shipperOrder["fcreatedate"] = null;
            shipperOrder["fmodifierid"] = string.Empty;
            shipperOrder["fmodifydate"] = null;
            shipperOrder["fclosestate"] = null;
            shipperOrder["fcloseid"] = string.Empty;
            shipperOrder["fclosedate"] = null;
            shipperOrder["fapproveid"] = string.Empty;
            shipperOrder["fapprovedate"] = null;
            shipperOrder["fcancelstatus"] = string.Empty;
            shipperOrder["fcancelid"] = string.Empty;
            shipperOrder["fcanceldate"] = null;
            shipperOrder["fnextprocnode"] = string.Empty;
            shipperOrder["fflowinstanceid"] = string.Empty;
            shipperOrder["ffromtranid"] = string.Empty;
            shipperOrder["froottranid"] = string.Empty;
            shipperOrder["fsourcetype"] = string.Empty;
            shipperOrder["fsourcenumber"] = string.Empty;
            shipperOrder["fpublishcid"] = string.Empty;
            shipperOrder["fdataorigin"] = string.Empty;
            shipperOrder["fprintcount"] = 0;
            shipperOrder["fprintid"] = string.Empty;
            shipperOrder["fprintdate"] = null;
            shipperOrder["fdatasourceterminal"] = string.Empty;


            //财务信息:先清零，后面会通过AutoCalculateShipperFinancialInfo计算明细得到对应的值
            shipperOrder["freceiptstatus"] = "receiptstatus_type_01"; //需求：状态一律默认为：全款未收
            shipperOrder["ffaceamount"] = 0;//货品原值
            shipperOrder["fsumamount"] = 0; //订单总额
            shipperOrder["fdealamount"] = 0; //成交金额
            shipperOrder["fdistsumrate"] = 0;//折扣率
            shipperOrder["fdistamount"] = 0;//折扣额
            shipperOrder["freceivabletobeconfirmed"] = 0;//收款待确认
            shipperOrder["freceivable"] = 0;//确认收款
            shipperOrder["funreceived"] = 0;//未收款
            shipperOrder["factrefundamount"] = 0;//实退金额
            shipperOrder["ffirstamount"] = 0;//首款额

            //协同信息(这里每个字段都判断一下存不存在的原因：wwc删除MDL字段却没有同时处理，这里为了兼容线上数据判断是否包含)
            string[] flds = new[] { "dept", "fstaff", "fstylist", "fterminalcustomer", "fcoophone", "fprovincecityregion", "fcooaddress" };
            foreach (var fld in flds)
            {
                if (shipperOrder.DynamicObjectType.Properties.ContainsKey(fld))
                {
                    shipperOrder[fld] = string.Empty;
                }
            }
        }

        /// <summary>
        /// 生成发货方经销商销售合同明细
        /// </summary>
        /// <param name="shipperOrderEntrys">发货方销售合同明细</param>
        /// <param name="receiverOrderEntry">接单方销售合同明细</param>
        /// <returns></returns>
        private DynamicObject GenerateShipperAgentOrderEntry(UserContext shipperAgentCtx, DynamicObjectCollection shipperOrderEntrys, DynamicObject receiverOrderEntry, DynamicObject shipperOrder)
        {
            //加载引用数据
            var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, receiverOrderEntry.DynamicObjectType, receiverOrderEntry, true);
            //目标经销商总额
            var ftargetagentamount = Convert.ToDecimal(shipperOrder["ftargetagentamount"]);

            var newfentry = (DynamicObject)shipperOrderEntrys.DynamicCollectionItemPropertyType.CreateInstance();
            newfentry["fproductid"] = receiverOrderEntry["fproductid"];
            newfentry["fproductid_ref"] = receiverOrderEntry["fproductid_ref"];
            newfentry["fattrinfo"] = receiverOrderEntry["fattrinfo"];
            newfentry["fattrinfo_first"] = (receiverOrderEntry["fattrinfo_ref"] as DynamicObject)?["fname"].ToString();
            newfentry["fattrinfo_e"] = receiverOrderEntry["fattrinfo_e"];
            newfentry["fcustomdes_e"] = receiverOrderEntry["fcustomdes_e"];
            newfentry["fmtono"] = receiverOrderEntry["fmtono"];
            newfentry["fbizunitid"] = receiverOrderEntry["fbizunitid"];
            newfentry["fbizqty"] = receiverOrderEntry["fbizqty"];
            newfentry["fstockstatus"] = receiverOrderEntry["fstockstatus"];
            newfentry["fexdeliverydate"] = receiverOrderEntry["fexdeliverydate"];
            newfentry["fsupplierid"] = receiverOrderEntry["fsupplierid"];
            newfentry["fsuitdescription"] = receiverOrderEntry["fsuitdescription"];
            newfentry["fsubqty"] = receiverOrderEntry["fsubqty"];
            newfentry["fsuitproductid"] = receiverOrderEntry["fsuitproductid"];
            newfentry["fsuitcombnumber"] = receiverOrderEntry["fsuitcombnumber"];
            newfentry["fpartscombnumber"] = receiverOrderEntry["fpartscombnumber"];
            newfentry["fsofacombnumber"] = receiverOrderEntry["fsofacombnumber"];
            //配件相关字段
            newfentry["fiscombmain"] = receiverOrderEntry["fiscombmain"];
            newfentry["fisautopartflag"] = receiverOrderEntry["fisautopartflag"];

            newfentry["funstdtype"] = receiverOrderEntry["funstdtype"];
            newfentry["fisgiveaway"] = receiverOrderEntry["fisgiveaway"];
            newfentry["fisoutspot"] = receiverOrderEntry["fisoutspot"];
            newfentry["funstdtypestatus"] = receiverOrderEntry["funstdtypestatus"];
            newfentry["fdistrateraw"] = receiverOrderEntry["fdistrateraw"];

            var brandId = receiverOrderEntry["fresultbrandid"];
            if (!receiverOrderEntry["fresultbrandid"].IsNullOrEmptyOrWhiteSpace())//业绩品牌
            {
                var product = receiverOrderEntry["fproductid_ref"] as DynamicObject;
                if (product != null)
                {
                    refMgr.Load(this.Context, product.DynamicObjectType, product, false);
                    var series = product["fseriesid_ref"] as DynamicObject;
                    if (series != null)
                    {
                        var seriesId = Convert.ToString(series["id"]);
                        var seriesCode = Convert.ToString(series["fnumber"]);
                        if (seriesCode == "Z1")//【系列】为”通配品牌” (编码”Z1”)
                        {
                            var AgentInfos = new ProductDataIsolateHelper().GetCurrentUserAgentInfos(shipperAgentCtx);
                            var Agents = AgentInfos.Select(o => o.OrgId).ToList(); //获取 经销商所有的【业绩品牌】
                            var seriesAuthSql = $@"SELECT b.* FROM t_ydj_productauth a with(nolock)
                                        INNER JOIN t_ydj_productauthbs b with(nolock) ON b.fid = a.fid 
                                        WHERE a.forgid in ('{string.Join("','", Agents)}') and fserieid='{receiverOrderEntry["fresultbrandid"].ToString()}' ";
                            var seriesAuthList = this.DBService.ExecuteDynamicObject(this.Context, seriesAuthSql).ToList();
                            if (!seriesAuthList.Any())
                            {
                                brandId = string.Empty; //商品 本身为"通配品牌" (Z1) 且 对应接单方经销商的销售合同商品 的 "业绩品牌" 当前发货经销商 没有授权时, 要清空该 发货经销商的销售合同 的  "业绩品牌"  并允许编辑
                            }
                        }
                    }
                }
            }
            newfentry["fresultbrandid"] = brandId;
            var qty = Convert.ToDecimal(newfentry["fbizqty"]);
            //是否赠品
            var fisgiveaway = Convert.ToBoolean(newfentry["fisgiveaway"]);

            newfentry["fprice"] = receiverOrderEntry["fprice"];
            newfentry["famount"] = receiverOrderEntry["famount"];
            newfentry["fdistrate"] = receiverOrderEntry["fdistrate"];
            newfentry["fdistamount"] = receiverOrderEntry["fdistamount"];
            newfentry["fdealprice"] = receiverOrderEntry["fdealprice"];
            newfentry["fdealamount"] = receiverOrderEntry["fdealamount"];
            //一切计算的前提是非赠品，如果是赠品就取的接单方合同的数据（接单方合同如果是赠品相应计算是对应的）
            if (ftargetagentamount > 0 && qty > 0 && !fisgiveaway)
            {
                //送货方合同成交单价：目标经销商总额/数量。应该是要改成交单价而不是单价。
                newfentry["fdealprice"] = ftargetagentamount / qty;
                newfentry["fdealamount"] = ftargetagentamount;
            }
            if (Convert.ToDecimal(newfentry["famount"]) > 0 && !fisgiveaway)
            {
                newfentry["fdistrate"] = Convert.ToDecimal(newfentry["fdealprice"]) * 10 / Convert.ToDecimal(newfentry["fprice"]);
                newfentry["fdistrateraw"] = Convert.ToDecimal(newfentry["fdealprice"]) * 10 / Convert.ToDecimal(newfentry["fprice"]);
                newfentry["fdistamount"] = Convert.ToDecimal(newfentry["famount"]) - Convert.ToDecimal(newfentry["fdealamount"]);
            }
            newfentry["flogisticscompanyid"] = receiverOrderEntry["flogisticscompanyid"];
            newfentry["funitid"] = receiverOrderEntry["funitid"];
            newfentry["fqty"] = receiverOrderEntry["fqty"];
            newfentry["fshipperagentid"] = receiverOrderEntry["fshipperagentid"];
            newfentry["ftargetagentid"] = receiverOrderEntry["ftargetagentid"];
            newfentry["fshipperdeliver"] = receiverOrderEntry["fshipperdeliver"]; //送达方
            newfentry["fshipperagentcontactperson"] = receiverOrderEntry["fshipperagentcontactperson"];
            newfentry["fshippercontactpersonphone"] = receiverOrderEntry["fshippercontactpersonphone"];
            newfentry["fclosestatus_e"] = receiverOrderEntry["fclosestatus_e"];
            newfentry["fsourceentryid_e"] = receiverOrderEntry["id"];

            newfentry["fhqprice"] = receiverOrderEntry["fhqprice"];
            newfentry["fterprice"] = receiverOrderEntry["fterprice"];
            newfentry["fteramount"] = receiverOrderEntry["fteramount"];
            newfentry["fcost"] = receiverOrderEntry["fcost"];
            newfentry["fcostprice"] = receiverOrderEntry["fcostprice"];
            return newfentry;
        }

        /// <summary>
        /// 根据接单方手机号+来源门店 加载或者创建客户，避免生成同手机号、同来源门店的客户
        /// </summary>
        /// <typeparam name="DynamicObject"></typeparam>
        /// <param name="receiverAgentCtx"></param>
        /// <param name="bizShipperAgentCtx"></param>
        /// <param name="formId"></param>
        private List<DynamicObject> GetCustomer(UserContext receiverAgentCtx, UserContext bizShipperAgentCtx, List<DynamicObject> shipperData, string formId, DynamicObject receiverOrder, bool IsCus = false)
        {
            if (formId != CustomerFormId || IsCus) return shipperData;
            //转单生成终端客户。在查询客户的关联手机号和最后创建客户的手机号应该是同一个字段：【收货人手机号】
            var fphone = Convert.ToString(receiverOrder["fphone"]);
            var where = $" fphone ='{fphone}'  ";
            //接单方来源门店
            //var fsrcstoreid = Convert.ToString(receiverData["fsrcstoreid"]);
            //var paramSer = this.Container.GetService<ISystemProfile>();
            //var fcustomerunique = paramSer.GetSystemParameter<string>(bizShipperAgentCtx, "bas_storesysparam", "fcustomerunique");//获取客户报备唯一性控制规则
            //var uniqueFlag = fcustomerunique != null && fcustomerunique.IndexOf("store") > 0 && !fsrcstoreid.IsNullOrEmptyOrWhiteSpace();

            //先不考虑 来源门店维度，因为发货方经销商没有接单方的来源门店，这个维度没有意义。
            //if (uniqueFlag && !fsrcstoreid.IsNullOrEmptyOrWhiteSpace())
            //{
            //    where += $" and fsrcstoreid='{fsrcstoreid}' ";
            //}
            return bizShipperAgentCtx.LoadBizDataByFilter(formId, where); //查看送货方客户是否存在

        }

        /// <summary>
        /// 创建送货方经销商基础数据(检查该发货经销商下是否有接单方的【客户】和【楼盘】，若无即创建数据)
        /// </summary>
        /// <param name="receiverAgentCtx"></param>
        /// <param name="bizShipperAgentCtx">业务发货经销商</param>
        /// <param name="formId"></param>
        /// <param name="fid"></param>
        /// <param name="IsCus">是否获取接单方经销商</param>
        private DynamicObject CreateShipperAgentBaseData(UserContext receiverAgentCtx, UserContext bizShipperAgentCtx, string formId, string fid, DynamicObject receiverOrder, bool IsCus = false)
        {
            if (!string.IsNullOrWhiteSpace(fid))
            {
                var receiverData = receiverAgentCtx.LoadBizDataById(formId, fid); //查看接单方经销商基础数据
                if (formId == CustomerFormId && IsCus)
                {
                    //客户是需要查接单方经销商的 而不是客户。
                    receiverData = receiverAgentCtx.LoadBizDataById("bas_agent", fid);
                }
                if (receiverData == null)
                {
                    return null;
                }
                var name = Convert.ToString(receiverData["fname"]);
                var shipperData = bizShipperAgentCtx.LoadBizDataByFilter(formId, $" fname ='{name}' "); //查看送货方经销商基础数据是否存在
                if (formId == CustomerFormId && IsCus)
                {
                    var fnumber = Convert.ToString(receiverData["fnumber"]);
                    shipperData = bizShipperAgentCtx.LoadBizDataByFilter(formId, $" fnumber ='{fnumber}' "); //查看送货方经销商基础数据是否存在
                }

                //to do 根据手机号+来源门店获取客户，如果没有才创建。
                shipperData = GetCustomer(receiverAgentCtx, bizShipperAgentCtx, shipperData, formId, receiverOrder, IsCus);

                if (shipperData == null || shipperData.Count == 0)
                {
                    var obj = receiverAgentCtx.LoadBizDataById(formId, fid);//无组织隔离
                    if (obj != null)
                    {
                        var shipperObj = (DynamicObject)obj.Clone();
                        shipperObj["id"] = string.Empty;
                        shipperObj["fnumber"] = string.Empty;
                        shipperObj["fmainorgid"] = string.Empty;
                        shipperObj["ftranid"] = string.Empty;

                        //客户字段特殊处理
                        if (formId == CustomerFormId)
                        {
                            shipperObj["faddress"] = receiverOrder["faddress"];
                            shipperObj["fprovince"] = receiverOrder["fprovince"];
                            shipperObj["fcity"] = receiverOrder["fcity"];
                            shipperObj["fregion"] = receiverOrder["fregion"];
                            //收货人、手机号
                            shipperObj["fphone"] = receiverOrder["fphone"];
                            shipperObj["fname"] = (receiverOrder["fcustomercontactid_ref"] as DynamicObject)?["fcontacter"];
                            shipperObj["fgeneratesource"] = "转单申请单审批通过：创建送货方经销商基础数据";

                            var dutyentry = shipperObj["fdutyentry"] as DynamicObjectCollection;
                            dutyentry.Clear();

                            shipperObj["fsrcstoreid"] = string.Empty;
                            shipperObj["fchannelid"] = string.Empty;
                            shipperObj["fdealamount"] = string.Empty;
                            shipperObj["fcustomerlevel"] = string.Empty;
                            shipperObj["favailableintegral"] = 0;
                            shipperObj["fsumamount"] = 0;
                            //来源门店也带上
                            //if (receiverData["fsrcstoreid"] != null) {
                            //    shipperObj["fsrcstoreid"] = receiverData["fsrcstoreid"];
                            //    //shipperObj["fdescription"] = "来源门店为接单方来源门店";
                            //}

                            shipperObj["fbizorgid"] = bizShipperAgentCtx.BizOrgId;
                            shipperObj["fmemberno"] = receiverData["fmemberno"];
                            var entries = shipperObj["fentry"] as DynamicObjectCollection;
                            if (entries != null && entries.Count > 0)
                            {
                                var goodsPayment = entries.Where(x => x["fpurpose"].ToString() == "settleaccount_type_01").FirstOrDefault();//货款
                                if (goodsPayment != null)
                                {
                                    if (goodsPayment.DynamicObjectType.Properties.ContainsKey("fbalance_e"))
                                    {
                                        goodsPayment["fbalance_e"] = 0;
                                    }
                                }
                            }
                        }
                        //bizShipperAgentCtx.SaveBizData(formId, shipperObj);
                        this.Gateway.InvokeBillOperation(bizShipperAgentCtx,
                        formId,
                        new List<DynamicObject> { shipperObj },
                        "draft",
                        new Dictionary<string, object>());
                        return shipperObj;
                    }
                }
                else
                {
                    var ship = shipperData.FirstOrDefault();
                    if (formId == CustomerFormId && !IsCus)
                    {
                        //如果匹配到送达方客户，那也以接档方客户传递过来。(客户信息应该取接单方合同客户)
                        ship["fname"] = (receiverOrder["fcustomercontactid_ref"] as DynamicObject)?["fcontacter"];
                        //客户信息应该取接单方合同客户信息而非接单方客户档案，因为合同带出客户后可能在合同上更新地址。
                        //收货人、手机号
                        ship["fphone"] = receiverOrder["fphone"];

                        ship["fprovince"] = receiverOrder["fprovince"];
                        ship["fcity"] = receiverOrder["fcity"];
                        ship["fregion"] = receiverOrder["fregion"];
                        ship["faddress"] = receiverOrder["faddress"];
                    }
                    return ship;
                }
            }
            return null;
        }

        /// <summary>
        /// 为来源经销商创建目标经销商对应的供应商、客户基础数据
        /// 作者：zpf
        /// 日期：2022-03-05
        /// </summary>
        /// <param name="targetAgentCtx">目标经销商上下文</param>
        /// <param name="receiverTransferOrderApply">转单申请单信息</param>
        /// <param name="fromAgent">来源经销商</param>
        /// <param name="targetAgent">目标经销商</param>
        /// <param name="formId">需要创建的目标数据表单Id</param>
        private void CreateAgentSupplierOrCustomerBaseData(UserContext targetAgentCtx, DynamicObject receiverTransferOrderApply, AgentBriefInfo fromAgent, AgentBriefInfo targetAgent, string formId)
        {
            if (!string.IsNullOrWhiteSpace(fromAgent?.AgentNo) && !string.IsNullOrWhiteSpace(targetAgent?.AgentNo))
            {
                var fromAgentNumber = Convert.ToString(fromAgent.AgentNo);
                var fromAgentName = Convert.ToString(fromAgent.AgentName);
                //查询来源经销商是否已经在目标经销商下创建了客户、供应商数据信息
                var targetObjData = targetAgentCtx.LoadBizDataByFilter(formId, $" fnumber='{fromAgentNumber}' "); //查看经销商/客户基础数据是否存在

                if (targetObjData == null || targetObjData.Count == 0)
                {
                    //获取一条已审核的数据进行修改最为保险
                    var customerOrSupplierForm = this.MetaModelService.LoadFormModel(targetAgentCtx, formId);
                    var customerOrSupplierObj = customerOrSupplierForm.GetDynamicObjectType(targetAgentCtx).CreateInstance() as DynamicObject;

                    customerOrSupplierObj["id"] = string.Empty;
                    customerOrSupplierObj["fnumber"] = fromAgentNumber;
                    customerOrSupplierObj["fname"] = fromAgent.AgentName;
                    customerOrSupplierObj["fmainorgid"] = string.Empty;
                    customerOrSupplierObj["fcreatorid"] = this.Context.UserId;
                    customerOrSupplierObj["fcreatedate"] = DateTime.Now;
                    customerOrSupplierObj["ftranid"] = string.Empty;        // 清空

                    //客户字段特殊处理
                    if (formId == CustomerFormId)
                    {
                        customerOrSupplierObj["fgeneratesource"] = "转单申请单审批通过：创建目标经销商对应的客户基础数据";
                        var dutyentry = customerOrSupplierObj["fdutyentry"] as DynamicObjectCollection;
                        dutyentry.Clear();

                        customerOrSupplierObj["fsrcstoreid"] = string.Empty;
                        customerOrSupplierObj["fchannelid"] = string.Empty;
                        customerOrSupplierObj["fdealamount"] = string.Empty;
                        customerOrSupplierObj["fcustomerlevel"] = string.Empty;
                        customerOrSupplierObj["favailableintegral"] = 0;
                        customerOrSupplierObj["fsumamount"] = 0;
                        customerOrSupplierObj["fbizorgid"] = targetAgentCtx.BizOrgId;

                        customerOrSupplierObj["ftype"] = "customertype_01";//类型【公司】
                        customerOrSupplierObj["fcustype"] = "customercate_02";//客户类型【供应商】
                        customerOrSupplierObj["foperationmode"] = "2";//运营模式

                        customerOrSupplierObj["fcontacts"] = Convert.ToString(receiverTransferOrderApply["freceiveragentcontactperson"]);//接单方经销商联系人
                        customerOrSupplierObj["fphone"] = Convert.ToString(receiverTransferOrderApply["freceivercontactpersonphone"]);//接单方经销商联系电话

                        var cuscontacttrys = customerOrSupplierObj["fcuscontacttry"] as DynamicObjectCollection;
                        var cuscontacttry = cuscontacttrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                        cuscontacttry["fcontacter"] = Convert.ToString(receiverTransferOrderApply["freceiveragentcontactperson"]);//接单方经销商联系人
                        cuscontacttry["fphone"] = Convert.ToString(receiverTransferOrderApply["freceivercontactpersonphone"]);//接单方经销商联系电话
                        cuscontacttrys.Add(cuscontacttry);

                        //客户货款账号
                        var cusentrys = customerOrSupplierObj["fentry"] as DynamicObjectCollection;
                        var cusentry = cusentrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                        cusentry["fpurpose"] = "settleaccount_type_01";
                        cusentry["fispayment"] = true;
                        cusentry["fiscash"] = true;
                        cusentry["fseq"] = "1";
                        cusentrys.Add(cusentry);
                    }
                    else
                    {
                        customerOrSupplierObj["ftype"] = "suppliertype_01";//类型

                        //【目标经销商】=【发货经销商】
                        if (Convert.ToString(receiverTransferOrderApply["ftargetagentid"]) == Convert.ToString(receiverTransferOrderApply["fshipperagentid"]))
                        {
                            customerOrSupplierObj["fcontacts"] = Convert.ToString(receiverTransferOrderApply["fshipperagentcontactperson"]);
                            customerOrSupplierObj["fphone"] = Convert.ToString(receiverTransferOrderApply["fshippercontactpersonphone"]);
                            var supcontacttrys = customerOrSupplierObj["fsupcontacttry"] as DynamicObjectCollection;
                            var supcontacttry = supcontacttrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                            supcontacttry["fcontacter"] = Convert.ToString(receiverTransferOrderApply["fshipperagentcontactperson"]);
                            supcontacttry["fcontacttype"] = Convert.ToString(receiverTransferOrderApply["fshippercontactpersonphone"]);
                            supcontacttrys.Add(supcontacttry);
                        }
                    }
                    //targetAgentCtx.SaveBizData(formId, customerOrSupplierObj);
                    this.Gateway.InvokeBillOperation(targetAgentCtx,
                        formId,
                        new List<DynamicObject> { customerOrSupplierObj },
                        "draft",
                        new Dictionary<string, object>());
                }
            }
        }

        /// <summary>
        /// 生成目标经销商的转单申请单
        /// </summary>
        /// <param name="receiverOrderApply">接单方转单申请单</param>
        /// <param name="shipperOrder">新生成的发货方销售合同</param>
        /// <returns></returns>
        private DynamicObject GenerateTargetAgentTransferOrderApply(DynamicObject receiverOrderApply, DynamicObject shipperOrder)
        {
            var shipperApply = (DynamicObject)receiverOrderApply.Clone();
            shipperApply["fbillno"] = null;
            shipperApply["fmainorgid"] = string.Empty;
            shipperApply["fstatus"] = BillStatus.E.ToString(); // 手动改为【已审核】
            shipperApply["ftranid"] = string.Empty;
            shipperApply["freceivertransferid"] = receiverOrderApply["id"]; // 保存接单方转单id
            shipperApply["fisreselltransfer"] = receiverOrderApply["fisreselltransfer"];
            shipperApply["ftransferstatus"] = "2";
            var shipperApplyEntries = shipperApply["fentry"] as DynamicObjectCollection;
            var shipperOrderEntries = shipperOrder["fentry"] as DynamicObjectCollection;
            if (!shipperApplyEntries.IsNullOrEmpty() && !shipperOrderEntries.IsNullOrEmpty())
            {
                var applyEntry = shipperApplyEntries.First();//转单明细必然是只有一条的
                foreach (var entry in shipperOrderEntries)
                {
                    var receiverOrderApplyFentry = (receiverOrderApply["fentry"] as DynamicObjectCollection)?.FirstOrDefault();//通过接单方转单申请单明细中源单明细Id找到新生成的销售合同上明细行中源单明细Id对应的商品行，来确定新生成的发货方转单申请单明细的源单明细Id

                    if (entry.DynamicObjectType.Properties.ContainsKey("fsourceentryid_e") && Convert.ToString(entry["fsourceentryid_e"]) == Convert.ToString(receiverOrderApplyFentry?["fsourcenentryid"]))
                    {
                        applyEntry["fsourcenentryid"] = entry["id"]; //保持下推源单据明细ID一致(联查和反写需要用到)
                    }
                }
            }
            return shipperApply;
        }

        /// <summary>
        /// 反写转单申请单
        /// </summary>
        /// <param name="transferOrdersApplys">接单方转单申请单</param>
        /// <param name="shipperOrders">发货经销商销售合同</param>
        private void SaveTransferOrderApply(IEnumerable<DynamicObject> transferOrdersApplys, IEnumerable<DynamicObject> shipperOrders)
        {
            if (transferOrdersApplys == null || transferOrdersApplys.Count() == 0) return;

            // 反写接单方转单申请单【发货方合同编号】=发货经销商销售合同【单据编码】
            foreach (var transferOrdersApply in transferOrdersApplys)
            {
                string receiverOrderNo = Convert.ToString(transferOrdersApply["freceivercontractnumber"]);
                // 依据：发货销售合同的【接单方合同编号】=接单方转单申请单【接单方合同编号】
                var shipperOrder = shipperOrders.FirstOrDefault(s => Convert.ToString(s["freceivercontractnumber"]).EqualsIgnoreCase(receiverOrderNo));
                if (shipperOrder != null)
                {
                    //把生成的发货方经销商信息反写到接单方经销商转单申请单中
                    transferOrdersApply["fshippercontractnumber"] = shipperOrder["fbillno"];
                }
            }
            //this.Context.SaveBizData(this.HtmlForm.Id, transferOrdersApplys);
            this.Gateway.InvokeBillOperation(Context,
                        this.HtmlForm.Id,
                        transferOrdersApplys,
                        "draft",
                        new Dictionary<string, object>());
        }

        /// <summary>
        /// 反写转单申请单
        /// </summary>
        /// <param name="transferOrdersApplys">接单方转单申请单</param>
        /// <param name="shipperOrders">发货经销商销售合同</param>
        private void SaveTransferOrderApply(IEnumerable<DynamicObject> transferOrdersApplys)
        {
            if (transferOrdersApplys == null || transferOrdersApplys.Count() == 0) return;
            // 反写接单方转单申请单【发货方合同编号】=发货经销商销售合同【单据编码】
            foreach (var transferOrdersApply in transferOrdersApplys)
            {
                //把生成的发货方经销商信息反写到接单方经销商转单申请单中
                transferOrdersApply["fshippercontractnumber"] = transferOrdersApply["fsourcenumber"];
            }
            //this.Context.SaveBizData(this.HtmlForm.Id, transferOrdersApplys);
            this.Gateway.InvokeBillOperation(Context,
                        this.HtmlForm.Id,
                        transferOrdersApplys,
                        "draft",
                        new Dictionary<string, object>());
        }

        /// <summary>
        /// 更新上游的《销售合同》字段如下 (通过《转单申请单》基本信息-表头【接单方合同编号】查找)
        /// </summary>
        /// <param name="transferOrdersApplys">接单方转单申请单</param>
        private void RewriteOrder(IEnumerable<DynamicObject> transferOrdersApplys)
        {
            // 1)更新上游的《销售合同》字段如下 (通过《转单申请单》基本信息-表头【接单方合同编号】查找)
            if (transferOrdersApplys.IsNullOrEmpty()) return;

            var orderNos = transferOrdersApplys.Where(s => Convert.ToString(s["fsourcetype"]).EqualsIgnoreCase(OrderFormId))
                .Select(s => Convert.ToString(s["fsourcenumber"])).Distinct();
            if (orderNos.Count() == 0) return;

            var orders = this.Context.LoadBizDataByNo(this.OrderForm.Id, "fbillno", orderNos);
            if (orders == null || orders.Count == 0) return;

            Dictionary<string, DynamicObject> savedOrders = new Dictionary<string, DynamicObject>();
            foreach (var transferOrderApply in transferOrdersApplys.Where(s => Convert.ToString(s["fsourcetype"]).EqualsIgnoreCase(OrderFormId)))
            {
                string orderNo = Convert.ToString(transferOrderApply["fsourcenumber"]);
                var order = orders.FirstOrDefault(s => Convert.ToString(s["fbillno"]).EqualsIgnoreCase(orderNo));
                if (order == null) continue;

                //保存时根据客户自动带出客户类型
                if (!order["fcustomerid"].IsNullOrEmptyOrWhiteSpace())
                {
                    var customerid = Convert.ToString(order["fcustomerid"]).Trim();
                    var customerObj = this.Context.LoadBizDataById("ydj_customer", customerid);
                    order["fcustomertype"] = customerObj["ftype"];
                }

                var tranferOrderApplyEntrys = transferOrderApply["fentry"] as DynamicObjectCollection;
                var orderEntrys = order["fentry"] as DynamicObjectCollection;
                foreach (var entry in tranferOrderApplyEntrys)
                {
                    string sourcenEntryId = Convert.ToString(entry["fsourcenentryid"]);
                    var orderEntry = orderEntrys.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(sourcenEntryId));
                    if (orderEntry == null) continue;

                    RewriteOrderEntry(orderEntry, transferOrderApply);
                    // 只需放一次
                    if (!savedOrders.ContainsKey(orderNo))
                    {
                        savedOrders[orderNo] = order;
                    }
                }
            }

            if (savedOrders.Any())
            {
                this.Gateway.InvokeBillOperation(Context,
                        this.OrderForm.Id,
                        savedOrders.Select(s => s.Value),
                        "draft",
                        new Dictionary<string, object>());
                //this.Context.SaveBizData(this.OrderForm.Id, savedOrders.Select(s => s.Value));
            }
        }

        /// <summary>
        /// 更新接单方销售合同明细
        /// </summary>
        /// <param name="orderEntry"></param>
        /// <param name="transferOrderApply"></param>
        private void RewriteOrderEntry(DynamicObject orderEntry, DynamicObject transferOrderApply)
        {
            /*
                     * 《销售合同》商品明细-【转单状态】 = 《转单申请单》基本信息-表头【转单状态】
                     * 《销售合同》商品明细-【转单通过日期】 = 《转单申请单》基本信息-表头【转单通过日期】
                     * 《销售合同》商品明细-【目标经销商】 = 《转单申请单》基本信息-表头【目标经销商】
                     * 《销售合同》商品明细-【发货经销商】 = 《转单申请单》基本信息-表头【发货经销商】
                     * 《销售合同》商品明细-【发货送发方】 = 《转单申请单》基本信息-表头【发货送发方】
                     * 《销售合同》商品明细-【发货经销商联系人】 = 《转单申请单》基本信息-表头【发货经销商联系人】
                     * 《销售合同》商品明细-【发货联系人手机号】 = 《转单申请单》基本信息-表头【发货联系人手机号】
                     */
            orderEntry["ftransferorderstatus"] = transferOrderApply["ftransferstatus"];
            orderEntry["ftransferorderagreetime"] = transferOrderApply["fpassdate"];
            orderEntry["fshipperagentid"] = transferOrderApply["fshipperagentid"];
            orderEntry["fshipperagentno"] = transferOrderApply["fshipperagentno"];
            orderEntry["fshipperagentname"] = transferOrderApply["fshipperagentname"];
            orderEntry["ftargetagentid"] = transferOrderApply["ftargetagentid"];
            orderEntry["ftargetagentno"] = transferOrderApply["ftargetagentno"];
            orderEntry["ftargetagentname"] = transferOrderApply["ftargetagentname"];
            orderEntry["fshipperdeliver"] = transferOrderApply["fshipperdeliverid"]; //送达方
            orderEntry["fshipperagentcontactperson"] = transferOrderApply["fshipperagentcontactperson"];
            orderEntry["fshippercontactpersonphone"] = transferOrderApply["fshippercontactpersonphone"];

        }

        /// <summary>
        /// 将接单方经销商的《转单申请单》由提交状态自动【审核】
        /// </summary>
        private void AuditTransferOrderApply(IEnumerable<DynamicObject> transferOrdersApplys)
        {
            var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, transferOrdersApplys, "audit",
                new Dictionary<string, object>());
            result.ThrowIfHasError(true, $"接单方{this.HtmlForm.Caption}审核失败！");
        }

        /// <summary>
        /// 根据id返回fname 或者其他字段
        /// </summary>
        /// <param name="context"></param>
        /// <param name="formId"></param>
        /// <param name="id"></param>
        /// <param name="fieldName"></param>
        /// <returns></returns>
        private string GetBaseDataNameById(UserContext context, string formId, string id, string fieldName = "fname")
        {
            if (id.IsNullOrEmptyOrWhiteSpace()) return "";

            var htmlForm = context.Container.GetService<IMetaModelService>()?.LoadFormModel(context, formId);
            var dm = context.Container.GetService<IDataManager>();
            dm.InitDbContext(context, htmlForm.GetDynamicObjectType(context));
            var dynObj = dm.Select(id) as DynamicObject;
            if (dynObj != null)
            {
                return Convert.ToString(dynObj[fieldName]);
            }
            return "";
        }
    }

    /// <summary>
    /// 经销商简要信息
    /// 作者：zpf
    /// 日期：2022-03-31
    /// </summary>
    public class AgentBriefInfo
    {
        /// <summary>
        /// 经销商Id
        /// </summary>
        public string AgentId { get; set; }

        /// <summary>
        /// 经销商编号
        /// </summary>
        public string AgentNo { get; set; }

        /// <summary>
        /// 经销商名称
        /// </summary>
        public string AgentName { get; set; }
    }
}
