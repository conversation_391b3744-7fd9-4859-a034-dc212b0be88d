using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.DTO.PurchaseOrder;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using static JieNor.AMS.YDJ.Core.Helpers.ProductDelistingHelper;

namespace JieNor.AMS.YDJ.MS.API.Plugin.PurchaseOrder
{
    /// <summary>
    /// 采购订单：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("MSSave")]
    public class MSSave : AbstractOperationServicePlugIn
    {
        private DynamicObject purOrder { get; set; }
        private List<DynamicObject> auditBeforeOrder { get; set; }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var dataStr = this.GetQueryOrSimpleParam("data", "");

            JObject purchaseOrderObj;
            try
            {
                purchaseOrderObj = JObject.Parse(dataStr);
            }
            catch (Exception ex)
            {
                throw new BusinessException("参数格式不正确！");
            }

            string billNo = purchaseOrderObj.GetJsonValue("fbillno", "");
            if (billNo.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("采购订单编号不能为空！");
            }

            var purchaseOrder = this.Context.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", new string[] { billNo }, true)
                ?.FirstOrDefault();
            if (purchaseOrder == null)
            {
                throw new WarnException($"采购订单{billNo}不存在，请检查！");
            }
            this.purOrder = purchaseOrder;
            string fhqderstatus = purchaseOrderObj.GetJsonValue("fhqderstatus", "");
            if (fhqderstatus.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"采购订单{billNo}字段【总部合同状态】不能为空！");
            }

            // 如果当前采购订单的总部合同状态已经是"驳回" 或 "终审"后，接口不接收 "提交至总部" 这个状态
            if (fhqderstatus.EqualsIgnoreCase("02"))
            {
                string currStatus = Convert.ToString(purchaseOrder["fhqderstatus"]);
                if (currStatus.EqualsIgnoreCase("03") || currStatus.EqualsIgnoreCase("05"))
                {
                    throw new WarnException($"采购订单{billNo}字段【总部合同状态】={this.HtmlForm.GetSimpleSelectItemText("fhqderstatus", currStatus)}，不允许更新【总部合同状态】={this.HtmlForm.GetSimpleSelectItemText("fhqderstatus", "02")}！");
                }
            }
            // 如果当前采购订单的总部合同状态已经是"终审"，接口不接收 "驳回" 这个状态
            if (fhqderstatus.EqualsIgnoreCase("05"))
            {
                string currStatus = Convert.ToString(purchaseOrder["fhqderstatus"]);
                if (currStatus.EqualsIgnoreCase("03"))
                {
                    throw new WarnException($"采购订单{billNo}字段【总部合同状态】={this.HtmlForm.GetSimpleSelectItemText("fhqderstatus", currStatus)}，不允许更新【总部合同状态】={this.HtmlForm.GetSimpleSelectItemText("fhqderstatus", "05")}！");
                }
            }

            string fhqderauditdate = purchaseOrderObj.GetJsonValue("fhqderauditdate", "");
            // 如果当前采购订单的总部合同状态是已终审，更新已终审时间
            if (fhqderstatus.EqualsIgnoreCase("03") && !string.IsNullOrWhiteSpace(fhqderauditdate))
            {
                purchaseOrder["fhqderauditdate"] = Convert.ToDateTime(fhqderauditdate);
                //purchaseOrder["fhqderauditdate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
            //if (fhqderstatus.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("采购订单【总部合同状态】不能为空！");

#warning 临时屏蔽：不判断状态
            //if (purchaseOrder["fhqderstatus"].IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"采购订单【{billNo}】未提交至总部，不允许操作！");

            // 【总部合同状态】=【驳回】，不允许调整财务字段，并且不需要重新计算
            var ignoreFinField = fhqderstatus.EqualsIgnoreCase("05");

            MapField(purchaseOrderObj, purchaseOrder, ignoreFinField);

            if (!ignoreFinField)
            {
                Calculate(purchaseOrderObj, purchaseOrder);
                Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatus(purchaseOrder);
                Core.Helpers.DocumentStatusHelper.DoCloseOrUnClose(purchaseOrder, this.Context);
            }
            else
            {
                //驳回，清空【总部合同号】
                purchaseOrder["fhqderno"] = "";
            }

            // 直接保存数据库
            //var dm = this.GetDataManager();
            //dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            //dm.Save(purchaseOrder);

            //改成调用draft
            var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new List<DynamicObject> { purchaseOrder }, "draft", new Dictionary<string, object>() { { "IgnoreCheckPermssion", true } });
            result.ThrowIfHasError();

            //当存在总部通过接口：”/msapi/purchaseorder/save?format=json”中的
            //【fprice】（采购单价）或【fdistrate】（折扣率）或【fdealprice】（成交单价）或【fdealamount_e】（成交金额）或【fdistamount_e】（折扣额）字段的
            //下发导致金蝶系统更新了《采购订单》的【采购单价】、【成交单价】、【成交金额】、【金额】、【折扣额】、【折扣率】时；
            //在原功能逻辑不变下增加逻辑：通过《采购订单》的【单据编号】+【商品编码】+【辅助属性】+【定制说明】+【行号】去匹配关联的下游《采购入库单》，
            //并更新《采购入库单》的【成交单价】、【采购单价】、【成交金额】、【金额】、【修改日期】、【修改人】，
            //并且操作记录要记录此次修改的记录，操作人为系统管理员。补充说明：《采购入库单》【金额】=【采购单价】*【实收数量】；【成交金额】=【成交单价】*【实收数量】；
            var purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();
            purchaseOrderService.UpdatePostockin(this.Context, purchaseOrder);
            //UpdatePostockin(purchaseOrder);

            // 反写采购订单的上游销售合同的【总部预计交货日期】
            this.BackWriteOrderHqDeliveryDate(purchaseOrder);

            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, new DynamicObject[] { purchaseOrder });

            // 如果是终审且有价格，就发给AI云
            this.Container.GetService<IMuSiAIService>().SyncToAI(this.Context, this.HtmlForm, new DynamicObject[] { purchaseOrder }, this.Option);

            if (ignoreFinField)
            {
                this.auditBeforeOrder = ProductDelistingHelper.GetOrderData(this.Context, new DynamicObject[] { purchaseOrder }.ToList(), this.HtmlForm);
            }

            this.WriteBackDirectOrderSubmitHqInfo(this.Context,purchaseOrder);
            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "操作成功！";
        }

        /// <summary>
        /// 反写采购订单的上游销售合同的【总部预计交货日期】
        /// </summary>
        private void BackWriteOrderHqDeliveryDate(DynamicObject purchaseOrder)
        {
            var deliveryPlanDate = purchaseOrder["fdeliveryplandate"];
            var hqDeliveryDate = deliveryPlanDate.IsNullOrEmptyOrWhiteSpace() ? "null" : $"'{Convert.ToString(deliveryPlanDate)}'";
            var entrys = purchaseOrder["fentity"] as DynamicObjectCollection;

            // 上游销售合同明细行ID
            var orderEntryIds = entrys
                ?.Where(o => Convert.ToString(o["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                    && !o["fsoorderentryid"].IsNullOrEmptyOrWhiteSpace())
                ?.Select(o => Convert.ToString(o["fsoorderentryid"]))
                ?.Distinct()
                ?.ToList();

            if (orderEntryIds == null || !orderEntryIds.Any()) return;

            var sqlList = new List<string>();
            foreach (var orderEntryId in orderEntryIds)
            {
                sqlList.Add($"update t_ydj_orderentry set fhqdeliverydate={hqDeliveryDate} where fentryid='{orderEntryId}'");
            }

            var dbServiceEx = this.Container.GetService<IDBServiceEx>();
            dbServiceEx.ExecuteBatch(this.Context, sqlList);
        }

        /// <summary>
        /// 允许映射字段
        /// </summary>
        static HashSet<string> allowMapFieldIds = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            // 单据头
            "fhqderstatus", "fhqderno", "fdescription", "fhqsyncdesc", "fdeliveryplandate", "fhqdertype",
            // 单据体：fentity
            "fprice", "fdistrate", "fdistamount_e", "fdemanddate","fplandate","fbizqty"//, "ftaxprice", "fstardiscount", "fdepthdiscount", "fnewdiscount", "fexpenserebate", "fotherdiscount", "fsapdiscount"
        };

        /// <summary>
        /// 财务字段
        /// </summary>
        static HashSet<string> finFieldIds = new HashSet<string>(StringComparer.OrdinalIgnoreCase) { "fprice", "fdistrate", "fdistamount_e" };

        /// <summary>
        /// 映射字段
        /// </summary>
        /// <param name="jObject"></param>
        /// <param name="dataEntity"></param>
        /// <param name="ignoreFinField">排除</param>
        private void MapField(JObject jObject, DynamicObject dataEntity, bool ignoreFinField = false)
        {
            // 字段按需填写
            foreach (JProperty item in jObject.Properties())
            {
                var e = new BeforeMapFieldEventArgs
                {
                    Property = item,
                    Data = jObject,
                    DataEntity = dataEntity
                };

                //如果是驳回 只映射总部合同状态字段，其它字段不更新，避免驳回时传了单价，但是金额没计算导致金额对不上
                if (ignoreFinField)
                {
                    if (!e.Property.Name.ToLower().EqualsIgnoreCase("fhqderstatus") &&
                        !e.Property.Name.ToLower().EqualsIgnoreCase("fhqderdate") &&
                        !e.Property.Name.ToLower().EqualsIgnoreCase("fhqderno") &&
                        !e.Property.Name.ToLower().EqualsIgnoreCase("fhqdertype")
                        ) continue;
                }

                BeforeMapField(e);

                if (e.Cancel) continue;

                string fieldId = item.Name;

                var htmlField = this.HtmlForm.GetField(fieldId);
                if (htmlField != null)
                {
                    // 不包括允许映射字段列表里
                    if (!allowMapFieldIds.Contains(fieldId)) continue;

                    // 忽略财务字段
                    if (ignoreFinField && finFieldIds.Contains(htmlField.Id)) continue;

                    // 有值才赋值
                    if (item.Value.Type != JTokenType.Null)
                    {
                        dataEntity[htmlField.PropertyName] = item.Value;
                    }
                    continue;
                }

                // 实体键
                string entityKey = item.Name;
                var htmlEntity = this.HtmlForm.GetEntity(entityKey);
                if (htmlEntity != null)
                {
                    JArray array = item.Value as JArray;
                    if (array != null)
                    {
                        var entities = dataEntity[entityKey] as DynamicObjectCollection;
                        foreach (var row in array)
                        {
                            var id = row.GetJsonValue("id", "");
                            var entity = entities.FirstOrDefault(s => s["ftranid"].ToString().EqualsIgnoreCase(id));
                            if (entity != null && row.Type == JTokenType.Object)
                            {
                                MapField(JObject.FromObject(row), entity);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 计算
        /// </summary>
        /// <param name="purchaseOrder"></param>
        private void Calculate(JObject jObject, DynamicObject purchaseOrder)
        {
            var entry = jObject?["fentity"];
            var entities = purchaseOrder["fentity"] as DynamicObjectCollection;
            foreach (var entity in entities)
            {
                var data = entry?.Where(o => Convert.ToString(o?["id"]).EqualsIgnoreCase(entity["ftranid"].ToString())).FirstOrDefault();
                if (!data.IsNullOrEmpty())
                {
                    var cancelreason = Convert.ToString(data?["cancelreason"]);
                    var fbizqty = Convert.ToDecimal(data?["fbizqty"]);
                    //如果总部返回拒绝原因下发的手工单对应明细应该 数量为0 且手动关闭
                    if (cancelreason.EqualsIgnoreCase("02"))
                    {
                        fbizqty = 0;
                        entity["fqty"] = fbizqty;
                        entity["fbizqty"] = fbizqty;
                        //走关闭服务计算关闭状态
                        //entity["fclosestatus_e"] = CloseStatusConst.Manual;
                    }
                    else
                    {
                        //传了数量才去做更新数量，因为采购订单下发变更和总部返回终审意见是同一个接口
                        if (fbizqty != 0)
                        {
                            entity["fqty"] = fbizqty;
                            entity["fbizqty"] = fbizqty;
                            //走关闭服务计算关闭状态
                            //entity["fclosestatus_e"] = CloseStatusConst.Default;
                        }
                    }
                }
            }

            PurchaseOrderUtil.Calcuate(this.Context, purchaseOrder);
        }

        private void BeforeMapField(BeforeMapFieldEventArgs e)
        {
            var property = e.Property;
            var dataEntity = e.DataEntity;
            var data = e.Data;

            switch (property.Name.ToLower())
            {
                case "fdiscounts":

                    e.Cancel = true;

                    // 新增总部折扣字段
                    decimal ftaxprice = property.Value.FirstOrDefault(s => s.GetJsonValue<string>("ftype").EqualsIgnoreCase(Enu_DiscountType.ZP01.ToString()))?.GetJsonValue<decimal>("fvalue") ?? 0;
                    decimal fstardiscount = property.Value.FirstOrDefault(s => s.GetJsonValue<string>("ftype").EqualsIgnoreCase(Enu_DiscountType.ZK03.ToString()))?.GetJsonValue<decimal>("fvalue") ?? 0;
                    decimal fdepthdiscount = property.Value.FirstOrDefault(s => s.GetJsonValue<string>("ftype").EqualsIgnoreCase(Enu_DiscountType.ZK05.ToString()))?.GetJsonValue<decimal>("fvalue") ?? 0;
                    decimal fnewdiscount = property.Value.FirstOrDefault(s => s.GetJsonValue<string>("ftype").EqualsIgnoreCase(Enu_DiscountType.ZK06.ToString()))?.GetJsonValue<decimal>("fvalue") ?? 0;
                    decimal fexpenserebate = property.Value.FirstOrDefault(s => s.GetJsonValue<string>("ftype").EqualsIgnoreCase(Enu_DiscountType.ZK99.ToString()))?.GetJsonValue<decimal>("fvalue") ?? 0;
                    decimal fsapdiscount = data.GetJsonValue<decimal>("fdistamount_e");
                    // 其他折扣=SAP折扣总额-ZK03 -ZK05-ZK06-ZK99
                    decimal fotherdiscount = fsapdiscount - fstardiscount - fdepthdiscount - fnewdiscount -
                                             fexpenserebate;

                    dataEntity["ftaxprice"] = ftaxprice;
                    dataEntity["fstardiscount"] = fstardiscount;
                    dataEntity["fdepthdiscount"] = fdepthdiscount;
                    dataEntity["fnewdiscount"] = fnewdiscount;
                    dataEntity["fexpenserebate"] = fexpenserebate;
                    dataEntity["fotherdiscount"] = fotherdiscount;
                    dataEntity["fsapdiscount"] = fsapdiscount;

                    break;
            }
        }


        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            var dataStr = this.GetQueryOrSimpleParam("data", "");

            JObject purchaseOrderObj;
            try
            {
                purchaseOrderObj = JObject.Parse(dataStr);
            }
            catch (Exception ex)
            {
                throw new BusinessException("参数格式不正确！");
            }
            string fhqderstatus = purchaseOrderObj.GetJsonValue("fhqderstatus", "");
            var ignoreFinField = fhqderstatus.EqualsIgnoreCase("05");
            if (ignoreFinField)
            {
                //反写销售合同【已提交总部或终审采购数量】
                OrderQtyWriteBackHelper.WriteBackHQPurQty(this.Context, this.HtmlForm, new List<DynamicObject>() { this.purOrder });

                ProductDelistingHelper.DealDelistingDataByPurOrder(this.Context, this.HtmlForm, this.auditBeforeOrder, new List<DynamicObject>() { this.purOrder }, this.OperationNo);
            }
        }

        /// <summary>
        /// 当采购订单是【摆场订单】且是直营的情况下，需要把中台返回的总部信息同步到直营的单据类型为【门店上样】销售合同上
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="purchaseOrder"></param>

        public void WriteBackDirectOrderSubmitHqInfo(UserContext userCtx,DynamicObject purchaseOrder)
        {
            if (this.Context.IsDirectSale)
            {
                var purchaseOrderBillTypeId = Convert.ToString(purchaseOrder["fbilltypeid"]);
                if(purchaseOrderBillTypeId.IsNullOrEmptyOrWhiteSpace()) return;

                var billTypeDy = purchaseOrder["fbilltypeid_ref"] as DynamicObject;

                if (billTypeDy == null)
                {
                    billTypeDy = this.Context.LoadBizBillHeadDataById("bd_billtype", purchaseOrderBillTypeId, "fname");
                }

                var purchaseOrderBillTypeName = Convert.ToString(billTypeDy["fname"]);

                if (purchaseOrderBillTypeName.IsNullOrEmptyOrWhiteSpace() ||
                    !purchaseOrderBillTypeName.EqualsIgnoreCase("摆场订单")) return;

                var purChaseOrderEntrys = purchaseOrder["fentity"] as DynamicObjectCollection;
                if(purChaseOrderEntrys == null || !purChaseOrderEntrys.Any()) return;
                var orderIdList = purChaseOrderEntrys.Where(x=>Convert.ToString(x["fsourceformid"]).EqualsIgnoreCase("ydj_order"))
                                                    .Select(y=>Convert.ToString(y["fsourceinterid"]))
                                                    .ToList();
                if(orderIdList == null || !orderIdList.Any()) return;
                var orgId = Convert.ToString(purchaseOrder["fmainorgid"]);
                var agentDy = userCtx.LoadBizBillHeadDataById("bas_agent",orgId,"ftoppiecesendtag,fdirectsalesgiveawaynotzero,fmanagemodel");
                if (agentDy != null)
                {
                    // 当【创建该单据的经销商.经营类型=直营】，并且【经销商档案.一件代发标记≠勾选】，并且【销售合同.单据类型=门店上样】，手动点击<采购>下推生成创建态采购订单（无需反写SAP状态）
                    var isPieceSendTag = Convert.ToBoolean(Convert.ToInt32(agentDy["ftoppiecesendtag"]));
                    if (!isPieceSendTag)
                    {
                        return;
                    }
                }
                var orderDys = this.Context.LoadBizDataById("ydj_order",orderIdList);

                //总部终审时间
                var submitHqTime = Convert.ToDateTime(purchaseOrder["fhqderauditdate"]);

                //总部合同类型
                var hqderType = Convert.ToString(purchaseOrder["fhqdertype"]);

                //总部合同号
                var hqderNo = Convert.ToString(purchaseOrder["fhqderno"]);

                //总部同步信息
                var hqsyncdesc = Convert.ToString(purchaseOrder["fhqsyncdesc"]);

                //总部合同状态 '01':'新建','02':'提交至总部','03':'已终审','04':'排产中','05':'驳回'
                //这里还有几个状态，是没有，需要跟需求确定下
                var hqderStatus = Convert.ToString(purchaseOrder["fhqderstatus"]);

                foreach (var orderDy in orderDys)
                {
                    switch (hqderStatus.ToLower())
                    {
                        case "03":
                            //采购订单总部合同类型 --> 销售合同sap合同类型
                            orderDy["fheadcontracttype"] = hqderType;
                            //采购订单总部合同状态 --> 销售合同sap合同状态
                            //'1':'已提交总部','2':'已驳回','3':'已终审'
                            orderDy["fchstatus"] = "3";
                            orderDy["fheadquartno"] = hqderNo;
                            if(submitHqTime == null || submitHqTime == default(DateTime))
                            {
                                submitHqTime = DateTime.Now;
                            }
                            else
                            {
                                orderDy["fheadquartfrtime"] = submitHqTime;
                            }
                            orderDy["fheadquartsyncmessage"] = hqsyncdesc;
                            break;
                        //驳回
                        case "05":
                            //采购订单总部合同类型 --> 销售合同sap合同类型
                            orderDy["fheadcontracttype"] = hqderType;
                            //采购订单总部合同状态 --> 销售合同sap合同状态
                            //'1':'已提交总部','2':'已驳回','3':'已终审'
                            orderDy["fchstatus"] = "2";
                            orderDy["fheadquartsyncmessage"] = hqsyncdesc;
                            break;
                            
                    }
                }

                var option = new Dictionary<string, object>();
                option.Add("__fromdirectpurchaseorder__","__fromdirectpurchaseorder__");
                Gateway.InvokeBillOperation(userCtx,"ydj_order",orderDys,"draft",option);
            }
        }

    }
}
