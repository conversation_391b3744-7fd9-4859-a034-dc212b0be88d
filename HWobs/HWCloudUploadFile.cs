using ICSharpCode.SharpZipLib.Zip;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OBS;
using OBS.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;

namespace JieNor.Framework.FileWebAPI.Helpers
{
    /// <summary>
    /// 华为云文件上传
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("hwuploadfile")]
    public class HWCloudUploadFile : AbstractOperationServicePlugIn, IHWCloudFileService
    {
        protected string Ak = "";
        protected string Sk = "";
        protected string Endpoint = "";
        protected string BucketName = "";
        public HWCloudUploadFile()
        {
            //Access Key Id
            Ak = this.GetAppConfig<string>("fw.huaweioss.ak");
            //Secret Access Key
            Sk = this.GetAppConfig<string>("fw.huaweioss.sk");
            //Endpoint 
            Endpoint = this.GetAppConfig<string>("fw.huaweioss.endpoint");
            //桶
            BucketName = this.GetAppConfig<string>("fw.huaweioss.bucketname");
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            ObsConfig config = new ObsConfig();
            config.Endpoint = Endpoint;
            ObsClient client = new ObsClient(Ak, Sk, config);
            var siData = this.GetQueryOrSimpleParam<string>("objs");
            var objAry = this.GetQueryOrSimpleParam("objs", "");
            var objs = JArray.Parse(objAry);
            try
            {
                foreach (var obj in objs)
                {
                    var fileName = obj["fileName"]?.ToString();
                    var fileUrl = System.AppDomain.CurrentDomain.BaseDirectory + obj["fileUrl"]?.ToString() + "\\" + fileName;
                    var fileId = obj["fileId"]?.ToString();
                    if (fileName.IsNullOrEmptyOrWhiteSpace() || fileUrl.IsNullOrEmptyOrWhiteSpace())
                    {
                        continue;
                    }
                    Stream stream = new MemoryStream(System.Text.Encoding.Default.GetBytes(fileId));
                    PutObjectRequest request = new PutObjectRequest()
                    {
                        BucketName = BucketName,  //待传入目标桶名
                        ObjectKey = fileId,   //待传入对象名(不是本地文件名，是文件上传到桶里后展现的对象名)
                        InputStream = stream,//  1. 文件流方式 
                        // FilePath = fileUrl,//待上传的本地文件路径，需要指定到具体的文件名 2.文件绝对路径方式
                    };
                    PutObjectResponse response = client.PutObject(request);
                }
                this.Result.IsSuccess = true;
            }
            catch (Exception ex)
            {
                this.Result.ComplexMessage.ErrorMessages.Add("文件下载失败：" + ex.ToString());
                this.Result.IsSuccess = false;
            }
        }

        public void UploadFile(string fileId, string fileName, string fileUrl)
        {
            ObsConfig config = new ObsConfig();
            config.Endpoint = Endpoint;
            ObsClient client = new ObsClient(Ak, Sk, config);
            try
            {
                Stream stream = new FileStream(fileUrl,FileMode.Open);
                PutObjectRequest request = new PutObjectRequest()
                {
                    BucketName = BucketName,  //待传入目标桶名
                    ObjectKey = fileId,   //待传入对象名(不是本地文件名，是文件上传到桶里后展现的对象名)
                    InputStream = stream,//  1. 文件流方式 
                                         // FilePath = fileUrl,//待上传的本地文件路径，需要指定到具体的文件名 2.文件绝对路径方式
                };
                PutObjectResponse response = client.PutObject(request);

                this.Result.IsSuccess = true;
            }
            catch (Exception ex)
            {
                this.Result.ComplexMessage.ErrorMessages.Add("文件下载失败：" + ex.ToString());
                this.Result.IsSuccess = false;
            }
        }

        public string UploadFile(string fileId, Stream stream)
        {
            Dictionary<string, string> result = new Dictionary<string, string>();
            ObsConfig config = new ObsConfig();
            config.Endpoint = Endpoint;
            ObsClient client = new ObsClient(Ak, Sk, config);
            try
            {
                PutObjectRequest request = new PutObjectRequest()
                {
                    BucketName = BucketName,  //待传入目标桶名
                    ObjectKey = fileId,   //待传入对象名(不是本地文件名，是文件上传到桶里后展现的对象名)
                    InputStream = stream,//  1. 文件流方式 
                                         // FilePath = fileUrl,//待上传的本地文件路径，需要指定到具体的文件名 2.文件绝对路径方式
                };
                PutObjectResponse response = client.PutObject(request);
                result.Add("IsSuccess", "true");
            }
            catch (Exception ex)
            {
                result.Add("IsSuccess", "false");
                result.Add("文件下载失败", "文件下载失败：" + ex.ToString());
            }
            return JsonConvert.SerializeObject(result);
        }
        
    }
}