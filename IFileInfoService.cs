using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    public interface IFileInfoService
    {
        Dictionary<string, string> PostFile(string filePath, string fileName = null);

        List<Dictionary<string, string>> PostFile(List<string> filePaths);

        string SendHttpRequestPost(string url, Dictionary<string, string> keyValues, Dictionary<string, string> fileList, Encoding encoding, Dictionary<string, string> headers);

    }
}
