using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface.DBSchema;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.Interface.Session;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.ActionParam;
using JieNor.Framework.MetaCore.PermData;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Drivers;
using Newtonsoft.Json.Linq;
using ServiceStack;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 扩展方法
    /// </summary>
    public static class OperationContextExtension
    {
        /// <summary>
        /// 显示指定表单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="activeForm"></param>
        /// <param name="pkId"></param>
        /// <param name="parentPageId"></param>
        /// <param name="domainType"></param>
        /// <param name="openStyle"></param>
        /// <param name="pageGlobalPara"></param>
        /// <param name="formCallback"></param>
        /// <param name="containerId"></param>
        /// <param name="formLayoutId">前端的显示视图标识</param>
        public static HtmlViewAction ShowForm(this UserContext userCtx,
                                            HtmlForm activeForm,
                                            string pkId,
                                            string parentPageId,
                                            Enu_DomainType domainType,
                                            Enu_OpenStyle openStyle = Enu_OpenStyle.Default,
                                            Dictionary<string, object> pageGlobalPara = null,
                                            Action<FormShowParameter> formCallback = null,
                                            string containerId = "",
                                            string formLayoutId = "")
        {
            //if (formId.IsNullOrEmptyOrWhiteSpace()) formId = operCtx.HtmlForm.Id;

            var pageInfras = userCtx.Container.GetService<IStaticResProvider>()?.GetPageInfrastructure(userCtx, activeForm.Id, formLayoutId, domainType);

            if (pageInfras?.PageViewFile.IsNullOrEmptyOrWhiteSpace() == true)
            {
                throw HttpError.NotFound("页面视图文件【{0}.html】不存在！".Fmt(pageInfras?.PageViewFile ?? activeForm.Id));
            }

            var uiMetaService = userCtx.Container.GetService<IMetaModelService>();
            var uiDataService = userCtx.Container.GetService<IUiDataConverter>();

            FormShowParameter formShowPara = null;

            var status = Enu_BillStatus.New;
            if (pkId.IsEmptyPrimaryKey() == false)
            {
                status = Enu_BillStatus.View;
                var permSvc = userCtx.Container.GetService<IPermissionService>();
                if (permSvc?.HasPermission(userCtx, new PermAuth(userCtx)
                {
                    FormId = activeForm.Id,
                    OperationName = "修改",
                    PermId = PermConst.PermssionItem_Modify
                }) == true)
                {
                    status = Enu_BillStatus.Modify;
                }
            }

            string postFix = status == Enu_BillStatus.New ? "新增" : status == Enu_BillStatus.Modify ? "修改" : "查看";

            if (domainType == Enu_DomainType.List
                || domainType == Enu_DomainType.ListTree)
            {
                status = Enu_BillStatus.Query;
                postFix = "列表";
            }
            else if (domainType == Enu_DomainType.ListReport)
            {
                status = Enu_BillStatus.Query;
                postFix = "报表";
            }
            else if (activeForm.ElementType == JieNor.Framework.DataTransferObject.HtmlElementType.HtmlForm_DynamicForm
                || domainType == Enu_DomainType.Report)
            {
                postFix = "";
            }
            else if (activeForm.ElementType == JieNor.Framework.DataTransferObject.HtmlElementType.HtmlForm_ParameterForm
                || domainType == Enu_DomainType.Parameter)
            {
                postFix = "";
            }

            var userProfileService = userCtx.Container.GetService<IUserProfile>();
            var allowQkFilter = false;
            switch (domainType)
            {
                case Enu_DomainType.List:
                case Enu_DomainType.ListTree:
                case Enu_DomainType.ListReport:
                    formShowPara = new ListShowParameter();
                    //列表快捷过滤启用场景
                    switch ((formShowPara as ListShowParameter).ListMode)
                    {
                        case Enu_ListMode.Lookup:
                            allowQkFilter = activeForm.IsAllowQkFilter(ListQuickSearchScene.List_Lookup);
                            break;
                        case Enu_ListMode.Trace:
                            allowQkFilter = activeForm.IsAllowQkFilter(ListQuickSearchScene.List_Trace);
                            break;
                        case Enu_ListMode.Pull:
                            allowQkFilter = activeForm.IsAllowQkFilter(ListQuickSearchScene.List_Pull);
                            break;
                        default:
                            allowQkFilter = activeForm.IsAllowQkFilter(ListQuickSearchScene.List);
                            break;
                    }
                    break;
                case Enu_DomainType.Bill:
                    formShowPara = new BillShowParameter();
                    (formShowPara as BillShowParameter).PkId = pkId;
                    break;
                case Enu_DomainType.Dynamic:
                case Enu_DomainType.Parameter:
                    formShowPara = new FormShowParameter();
                    break;
                case Enu_DomainType.Report:
                    formShowPara = new ReportShowParameter();
                    status = Enu_BillStatus.View;
                    break;
                default:
                    throw new NotSupportedException("不支持的领域模型：" + domainType);
            }

            var domainTypeName = domainType.ToString().ToLower();
            formShowPara.FormUserProfile = userProfileService.LoadUserProfile(userCtx, activeForm.Id, domainTypeName);
            formShowPara.DomainType = domainType;
            formShowPara.FormId = activeForm.Id;
            formShowPara.ContainerId = containerId;

            formShowPara.Status = status;
            formShowPara.FormCaption = postFix.IsNullOrEmptyOrWhiteSpace()
                ? activeForm.Caption : "{0}-{1}".Fmt(activeForm.Caption, postFix);
            formShowPara.OpenStyle = openStyle;

            formShowPara.FormLayoutId = formLayoutId;

            //创建服务端页面实例
            string currPageId = CreateServerPageInstance(userCtx, activeForm, parentPageId, domainType, openStyle, pageGlobalPara, status, pkId);

            try
            {
                formShowPara.PageId = currPageId;

                //委托出去由调用者提供自定义参数信息
                formCallback?.Invoke(formShowPara);

                if (domainType == Enu_DomainType.Bill
                    || domainType == Enu_DomainType.Dynamic
                    || domainType == Enu_DomainType.Parameter)
                {
                    formShowPara.FormId = activeForm.Id;
                    formShowPara.UiRule = uiMetaService.LoadFormBusinessRule(userCtx, formShowPara);
                }

                if (activeForm.ElementType == HtmlElementType.HtmlForm_DynamicForm
                    && domainType == Enu_DomainType.Dynamic
                    && !formShowPara.CustomParameter.ContainsKey("SendBackModelData"))
                {
                    formShowPara.CustomParameter.Add("SendBackModelData", "true");
                }

                //获取菜单模型数据
                if (formShowPara is ListShowParameter)
                {
                    if (formShowPara.FormTopMenu == null
                        || formShowPara.FormTopMenu.Any() == false)
                        formShowPara.FormTopMenu = uiMetaService.GetListMenu(userCtx, activeForm.Id, formLayoutId, (formShowPara as ListShowParameter).ListMode);

                    formShowPara.UiMeta = uiDataService.CreateUIMetaObject(userCtx, activeForm, domainType, formShowPara.Status == Enu_BillStatus.New);
                    formShowPara.UiMeta["allowQkFilter"] = allowQkFilter;
                }
                else if (formShowPara is ReportShowParameter)
                {
                    formShowPara.FormTopMenu = uiMetaService.GetListMenu(userCtx, activeForm.Id, formLayoutId, Enu_ListMode.Default);
                    formShowPara.UiMeta = uiDataService.CreateUIMetaObject(userCtx, activeForm, domainType, formShowPara.Status == Enu_BillStatus.New);

                    //加载下拉框数据源
                    LoadComboDatas(userCtx, activeForm, formShowPara);
                }
                else if (formShowPara is BillShowParameter
                    || formShowPara is FormShowParameter)
                {
                    if (formShowPara.FormTopMenu == null
                        || formShowPara.FormTopMenu.Any() == false)
                        formShowPara.FormTopMenu = uiMetaService.GetBillMenuByGroup(userCtx, activeForm.Id, "standard", formLayoutId, status, formShowPara.CustomParameter);
                    if (formShowPara.FormBottomMenu == null
                        || formShowPara.FormBottomMenu.Any() == false)
                        formShowPara.FormBottomMenu = uiMetaService.GetBillMenuByGroup(userCtx, activeForm.Id, "business", formLayoutId, status);

                    var permService = userCtx.Container.GetService<IPermissionService>();
                    var lstInvisibleFields = permService.GetInvisibleFieldByFormId(userCtx, activeForm.Id) ?? new List<string>();

                    //加载下拉框数据源
                    LoadComboDatas(userCtx, activeForm, formShowPara);

                    var profileService = userCtx.Container.GetService<IListQuryProfile>();

                    var uiMetaAction = new Action<HtmlElement, JObject>((el, uiMeta) =>
                    {
                        if (el is HtmlField)
                        {
                            if (lstInvisibleFields.Contains(el.Id, StringComparer.OrdinalIgnoreCase))
                            {
                                uiMeta["visible"] = false;
                                uiMeta["useMask"] = true;
                            }
                        }
                        else if (el is HtmlEntryEntity)
                        {
                            var entity = el as HtmlEntryEntity;
                            if (entity.Personalized)
                            {
                                //加载单元格颜色设置
                                var styleSet = profileService.GetListCellColor(userCtx, activeForm.Id, $"{entity.Id}color");
                                if (styleSet != null)
                                {
                                    styleSet = styleSet.OrderBy(f => f.Priority).ToList();
                                    uiMeta["styleSet"] = JArray.Parse(styleSet.ToJson());
                                }
                            }
                        }
                    });

                    formShowPara.UiMeta = uiDataService.CreateUIMetaObject(userCtx, activeForm, domainType, formShowPara.Status == Enu_BillStatus.New, uiMetaAction);
                }

                //加载外部应用表单容器插件js路径
                if (pageGlobalPara != null && pageGlobalPara.ContainsKey("extApp"))
                {
                    string extAppJson = Convert.ToString(pageGlobalPara["extApp"]);
                    if (extAppJson.IsNullOrEmptyOrWhiteSpace()) extAppJson = "{}";
                    Dictionary<string, object> extAppDic = extAppJson.FromJson<Dictionary<string, object>>();
                    if (extAppDic != null && extAppDic.ContainsKey("formId"))
                    {
                        string extAppFormId = Convert.ToString(extAppDic["formId"]);
                        if (!extAppFormId.IsNullOrEmptyOrWhiteSpace())
                        {
                            var staticResProvider = userCtx.Container.GetService<IStaticResProvider>();
                            var extAppFormJsFilePath = staticResProvider?.GetJsFileRelativePath(userCtx, extAppFormId);
                            extAppDic.Add("extAppFormJsPath", extAppFormJsFilePath ?? "");

                        }
                        //将 extApp 参数原路返回给前端
                        formShowPara?.CustomParameter?.Add("extApp", extAppDic);
                    }
                }
            }
            catch
            {
                //创建页面时出错，必须回滚页面实例，不然，补偿机制执行不成功
                var pageMgr = userCtx.Container.GetService<IPageManager>();
                pageMgr?.Dispose(userCtx, currPageId);
                throw;
            }

            return new HtmlViewAction()
            {
                ActionId = "showForm",
                ActionParams = new Dictionary<string, object>
                {
                    { "fsp", formShowPara },
                    //{ "url", pageInfras?.PageViewFile??"" },
                    //{ "formCaption", strFormCaption },
                    //{ "status", status },
                    //{ "openStyle", openStyle },
                    //{ "containerid", containerid },
                    //{ "pkId", pkId },
                    //{ "formid", activeForm.Id },
                    //{ "domaintype", domainType },
                    {"pi",pageInfras },
                    //{ "opData", opData }
                }
            };
        }

        /// <summary>
        /// 加载下拉框数据源
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="activeForm"></param>
        /// <param name="formShowPara"></param>
        private static void LoadComboDatas(UserContext userCtx, HtmlForm activeForm, FormShowParameter formShowPara)
        {
            //加载下拉框数据源
            var comboDataService = userCtx.Container.GetService<IComboDataService>();
            var comboDatas = comboDataService.GetFormComboDatas(userCtx, activeForm.Id, true);
            if (comboDatas != null)
            {
                //此处转成动态对象，避免序列化多余的类型字符串到前端
                Dictionary<string, object> uiComboData = new Dictionary<string, object>();
                foreach (var key in comboDatas.Keys)
                {
                    uiComboData[key] = comboDatas[key].Select(o => new
                    {
                        Id = o.Id,
                        Number = o.Number,
                        Name = o.Name,
                        Disable = o.Disable,
                        IsPrepare = o.IsPreset,
                    }).ToList();
                }
                formShowPara.UiComboData = uiComboData;
            }
        }

        private static string CreateServerPageInstance(UserContext userCtx,
            HtmlForm activeForm,
            string parentPageId,
            Enu_DomainType domainType,
            Enu_OpenStyle openStyle,
            Dictionary<string, object> pageGlobalPara,
            Enu_BillStatus status,
            string pkId)
        {
            //这个是发布菜单入口Id
            var funcEntryId = pageGlobalPara?.GetString("__entry");
            //if (funcEntryId.IsNullOrEmptyOrWhiteSpace())
            //    funcEntryId = userCtx.TranId;

            //if (domainType == Enu_DomainType.Bill
            //    || domainType == Enu_DomainType.Dynamic
            //    || domainType == Enu_DomainType.Parameter)
            //{
            //    funcEntryId = Guid.NewGuid().ToString();
            //}

            HtmlServerPage page = HtmlServerPage.CreatePageInstance(userCtx, activeForm, domainType, openStyle, status, parentPageId, pkId);
            if (!funcEntryId.IsNullOrEmptyOrWhiteSpace())
            {
                page.Id = funcEntryId;
            }

            //服务端建立父子页模型
            var pageMgr = userCtx.Container.GetService<IPageManager>();
            pageMgr.AddOrUpdatePage(userCtx, page);

            //将页面全局参数放入session共享区
            dynamic pageSession = page.GetPageSession();
            FormParameter formPara = new FormParameter()
            {
                FormId = activeForm.Id,
                PageId = page.PageId,
                ParentPageId = page.ParentPageId,
                DomainType = domainType,
                OpenStyle = openStyle,
                Status = status,
                CustomParameter = pageGlobalPara ?? new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase),
            };

            pageSession.FormParameter = formPara;

            dynamic parentPageSession = page.GetParentPageSession();
            if (parentPageSession != null
                && parentPageSession.FormParameter != null
                && parentPageSession.FormParameter.CustomParameter is Dictionary<string, object>)
            {
                formPara.CustomParameter.Merge(parentPageSession.FormParameter.CustomParameter as Dictionary<string, object>, (key) =>
                {
                    //如果父子表单属于同一个formid，只全部继承会话区参数
                    if (formPara.FormId.EqualsIgnoreCase(parentPageSession.FormParameter.FormId as string))
                    {
                        return false;
                    }
                    //否则，目前只合并一个叫openStyle的参数
                    switch (key.ToLower())
                    {
                        case "openstyle":
                            return false;
                    }
                    return true;
                });
            }

            return page.PageId;
        }

        /// <summary>
        /// 使用指定数据包显示指定表单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="activeForm"></param>
        /// <param name="dataEntity"></param>
        /// <param name="isApplyDefValue">是否设置默认值，需要注意：一般新增时需要设置，查看修改等不需要设置默认值（否则会被默认值覆盖，导致数据错误）</param>
        /// <param name="parentPageId"></param>
        /// <param name="openStyle"></param>
        /// <param name="domainType"></param>
        /// <param name="pageGlobalPara"></param>
        /// <param name="formCallback"></param>
        /// <param name="containerId"></param>
        /// <param name="formLayoutId"></param>
        /// <returns></returns>
        //[PerfMonitor]
        public static object ShowSpecialForm(
            this UserContext userCtx,
            HtmlForm activeForm,
            DynamicObject dataEntity = null,
            bool isApplyDefValue = true,
            string parentPageId = null,
            Enu_OpenStyle openStyle = Enu_OpenStyle.Default,
            Enu_DomainType domainType = Enu_DomainType.None,
            Dictionary<string, object> pageGlobalPara = null,
            Action<FormShowParameter> formCallback = null,
            string containerId = "",
            string formLayoutId = "")
        {
            if (dataEntity == null)
            {
                dataEntity = activeForm.GetDynamicObjectType(userCtx).CreateInstance() as DynamicObject;
            }

            //表单模型字段默认值计算器
            if (isApplyDefValue)
            {
                var defCalulator = userCtx.Container.GetService<IDefaultValueCalculator>();
                defCalulator.Execute(userCtx, activeForm, new DynamicObject[] { dataEntity });

                //再根据默认单据类型应用自动记忆的处理逻辑
                defCalulator.SetFieldMemoryValueByBillType(userCtx, activeForm, new DynamicObject[] { dataEntity });
            }

            //var perfId = PerfmonUtil.StartMonitor();
            userCtx.Container.GetService<LoadReferenceObjectManager>()?.Load(userCtx, activeForm.GetDynamicObjectType(userCtx), new DynamicObject[] { dataEntity }, false);
            //Debug.Print(PerfmonUtil.EndMonitor(perfId));


            var uiConverter = userCtx.Container.GetService<IUiDataConverter>();
            var billJsonData = uiConverter.CreateUIDataObject(userCtx, activeForm, dataEntity);

            Enu_BillStatus status = Enu_BillStatus.New;
            if (dataEntity.DataEntityState.FromDatabase) status = Enu_BillStatus.View;

            return userCtx.ShowForm(activeForm,
                dataEntity["id"] as string,
                parentPageId,
                domainType,
                openStyle,
                pageGlobalPara,
                (formPara) =>
                {
                    formPara.UiData = billJsonData.GetJsonValue<JObject>("uiData", new JObject());
                    formPara.Status = status;
                    formCallback?.Invoke(formPara);

                    //将单据类型的控制参数放入自定义参数包
                    var billTypeField = activeForm.GetFieldList().FirstOrDefault(f => f is HtmlBillTypeField && f.Entity is HtmlHeadEntity);
                    if (billTypeField != null)
                    {
                        var billTypeId = billTypeField.DynamicProperty.GetValue<string>(dataEntity);
                        if (!billTypeId.IsEmptyPrimaryKey())
                        {
                            var billTypeService = userCtx.Container.GetService<IBillTypeService>();
                            var billTypeObj = billTypeService.GetBillTypeById(userCtx, billTypeId);
                            formPara.uiBillTypeParam = billTypeService.CreateBillTypeUIDataObject(userCtx, billTypeObj); 
                        }
                    }
                },
                containerId, formLayoutId);
        }

        /// <summary>
        /// 显示进度表单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="taskId"></param>
        /// <param name="dctOption"></param>
        /// <returns></returns>
        public static HtmlViewAction ShowProgressForm(this UserContext userCtx, string taskId, Dictionary<string, string> dctOption = null)
        {
            return new HtmlViewAction()
            {
                ActionId = "showprogressform",
                ActionParams = new Dictionary<string, object> {
                    { "actiondata", new ShowProgressFormAction()
                            {
                                TaskId = taskId,
                                Total = 100,
                                Option = dctOption ?? new Dictionary<string, string>(),
                            }
                    },
                }
            };
        }

        /// <summary>
        /// 向前端返回设置字段值的指令
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="id">字段标识</param>
        /// <param name="value">字段值</param>
        /// <param name="row">行号</param>
        /// <param name="pageId">页面标识（不传为当前页面）</param>
        public static void SetValue(this OperationContext operCtx, string id, object value, string row = null, string pageId = null)
        {
            if (operCtx == null) return;

            var htmlForm = operCtx.HtmlForm;
            if (!pageId.IsNullOrEmptyOrWhiteSpace() && !operCtx.PageId.EqualsIgnoreCase(pageId))
            {
                var pageMgr = operCtx.Container?.GetService<IPageManager>();
                var pageSession = pageMgr?.GetPage(operCtx.UserContext, pageId);
                if (pageSession != null)
                {
                    var metaModelService = operCtx.Container.GetService<IMetaModelService>();
                    htmlForm = metaModelService.LoadFormModel(operCtx.UserContext, pageSession.FormId);
                }
            }

            if (pageId.IsNullOrEmptyOrWhiteSpace()) pageId = operCtx.PageId;

            SetValue(operCtx.UserContext, operCtx.Result, htmlForm, id, value, row, pageId);
        }

        /// <summary>
        /// 自由添加指定表单的赋值动作
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="result"></param>
        /// <param name="htmlForm"></param>
        /// <param name="id"></param>
        /// <param name="value"></param>
        /// <param name="row"></param>
        /// <param name="pageId"></param>
        public static void SetValue(this UserContext userCtx, IOperationResult result, HtmlForm htmlForm, string id, object value, string row = null, string pageId = null)
        {
            if (userCtx == null) return;

            if (htmlForm == null) return;
            var fld = htmlForm.GetField(id);
            if (fld == null) return;

            if (pageId.IsNullOrEmptyOrWhiteSpace()) return;

            var updateValueAction = result.HtmlActions
                .FirstOrDefault(o => (o as HtmlViewAction)?.ActionId.EqualsIgnoreCase("updatevalue") == true) as HtmlViewAction;
            if (updateValueAction == null)
            {
                updateValueAction = new HtmlViewAction();
                updateValueAction.ActionId = "updatevalue";
                result.HtmlActions.Add(updateValueAction);
            }
            if (updateValueAction.ActionParams == null) updateValueAction.ActionParams = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);

            var fldValueListByPage = (Dictionary<string, SetValueAction>)updateValueAction.ActionParams.GetOrAdd(pageId, (key) =>
            {
                return new Dictionary<string, SetValueAction>(StringComparer.OrdinalIgnoreCase);
            });
            SetValueAction fldValue = null;
            if (!fldValueListByPage.TryGetValue($"{fld.Id}_{row}", out fldValue))
            {
                fldValue = new SetValueAction();
                fldValueListByPage[$"{fld.Id}_{row}"] = fldValue;
            }
            fldValue.Id = id;
            fldValue.GridId = fld.IsBillHeadField ? "" : fld.EntityKey;
            fldValue.Row = row;
            fldValue.Value = value;
        }

        /// <summary>
        /// 向前端返回设置明细数据源的指令
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="id">明细标识</param>
        /// <param name="value">明细表格数据</param>
        /// <param name="pageId">页面标识（不传为当前页面）</param>
        public static void SetGridDataAction(this OperationContext operCtx, string id, object value, string pageId = null)
        {
            if (operCtx == null) return;
            if (operCtx.HtmlForm == null) return;
            var entry = operCtx.HtmlForm.GetEntryEntity(id);
            if (entry == null) return;

            if (pageId.IsNullOrEmptyOrWhiteSpace()) pageId = operCtx.PageId;
            if (pageId.IsNullOrEmptyOrWhiteSpace()) return;

            var htmlAction = operCtx.Result.HtmlActions
                .FirstOrDefault(o => (o as HtmlViewAction)?.ActionId.EqualsIgnoreCase("setgriddataaction") == true) as HtmlViewAction;
            if (htmlAction == null)
            {
                htmlAction = new HtmlViewAction();
                htmlAction.ActionId = "setgriddataaction";
                operCtx.Result.HtmlActions.Add(htmlAction);
            }

            if (htmlAction.ActionParams == null) htmlAction.ActionParams = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);

            var actionParams = (Dictionary<string, object>)htmlAction.ActionParams.GetOrAdd(pageId, (key) =>
            {
                return new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
            });

            actionParams[$"{entry.Id}"] = value;
        }

        /// <summary>
        /// 生成刷新前端页面的动作指令
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="pageId"></param>
        public static void AddRefreshPageAction(this OperationContext operCtx, string pageId = null)
        {
            if (operCtx == null) return;
            if (operCtx.HtmlForm == null) return;

            if (pageId.IsNullOrEmptyOrWhiteSpace()) pageId = operCtx.PageId;
            if (pageId.IsNullOrEmptyOrWhiteSpace()) return;

            var htmlAction = operCtx.Result.HtmlActions
                .FirstOrDefault(o => (o as HtmlViewAction)?.ActionId.EqualsIgnoreCase("refreshaction") == true) as HtmlViewAction;
            if (htmlAction == null)
            {
                htmlAction = new HtmlViewAction();
                htmlAction.ActionId = "refreshaction";
                operCtx.Result.HtmlActions.Add(htmlAction);
            }

            if (htmlAction.ActionParams == null) htmlAction.ActionParams = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);

            var actionParams = (Dictionary<string, object>)htmlAction.ActionParams.GetOrAdd(pageId, (key) =>
            {
                return new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
            });
        }

        /// <summary>
        /// 向前端返回插入明细行的指令
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="id">明细标识</param>
        /// <param name="rowValue">插入新行所对应的值</param>
        /// <param name="row">明细行号（表示在哪一行前面插入新行，如果不填，则默认插入到表格最后面）</param>
        /// <param name="pageId">页面标识（不传为当前页面）</param>
        public static void InsertRowAction(this OperationContext operCtx, string id, object rowValue, int row = -1, string pageId = null)
        {
            if (operCtx == null) return;
            if (operCtx.HtmlForm == null) return;
            var entry = operCtx.HtmlForm.GetEntryEntity(id);
            if (entry == null) return;

            if (pageId.IsNullOrEmptyOrWhiteSpace()) pageId = operCtx.PageId;
            if (pageId.IsNullOrEmptyOrWhiteSpace()) return;

            var htmlAction = operCtx.Result.HtmlActions
                .FirstOrDefault(o => (o as HtmlViewAction)?.ActionId.EqualsIgnoreCase("insertrowaction") == true) as HtmlViewAction;
            if (htmlAction == null)
            {
                htmlAction = new HtmlViewAction();
                htmlAction.ActionId = "insertrowaction";
                operCtx.Result.HtmlActions.Add(htmlAction);
            }

            if (htmlAction.ActionParams == null) htmlAction.ActionParams = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);

            var actionParams = (Dictionary<string, InsertRowAction>)htmlAction.ActionParams.GetOrAdd(pageId, (key) =>
            {
                return new Dictionary<string, InsertRowAction>(StringComparer.OrdinalIgnoreCase);
            });

            InsertRowAction actionParam = null;
            if (!actionParams.TryGetValue($"{entry.Id}_{row}", out actionParam))
            {
                actionParam = new InsertRowAction();
                actionParams[$"{entry.Id}_{row}"] = actionParam;
            }
            actionParam.GridId = id;
            actionParam.Row = row;
            actionParam.Value = rowValue;
        }

        /// <summary>
        /// 向前端返回删除明细行的指令
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="id">明细标识</param>
        /// <param name="row">明细行号</param>
        /// <param name="pageId">页面标识（不传为当前页面）</param>
        public static void DeleteRowAction(this OperationContext operCtx, string id, int row, string pageId = null)
        {
            if (operCtx == null) return;
            if (operCtx.HtmlForm == null) return;
            var entry = operCtx.HtmlForm.GetEntryEntity(id);
            if (entry == null) return;

            if (pageId.IsNullOrEmptyOrWhiteSpace()) pageId = operCtx.PageId;
            if (pageId.IsNullOrEmptyOrWhiteSpace()) return;

            var htmlAction = operCtx.Result.HtmlActions
                .FirstOrDefault(o => (o as HtmlViewAction)?.ActionId.EqualsIgnoreCase("deleterowaction") == true) as HtmlViewAction;
            if (htmlAction == null)
            {
                htmlAction = new HtmlViewAction();
                htmlAction.ActionId = "deleterowaction";
                operCtx.Result.HtmlActions.Add(htmlAction);
            }

            if (htmlAction.ActionParams == null) htmlAction.ActionParams = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);

            var actionParams = (Dictionary<string, DeleteRowAction>)htmlAction.ActionParams.GetOrAdd(pageId, (key) =>
            {
                return new Dictionary<string, DeleteRowAction>(StringComparer.OrdinalIgnoreCase);
            });

            DeleteRowAction actionParam = null;
            if (!actionParams.TryGetValue($"{entry.Id}_{row}", out actionParam))
            {
                actionParam = new DeleteRowAction();
                actionParams[$"{entry.Id}_{row}"] = actionParam;
            }
            actionParam.GridId = id;
            actionParam.Row = row;
        }

        /// <summary>
        /// 初始化各操作相关的元素状态信息
        /// </summary>
        /// <param name="operCtx"></param>
        public static void InitOperationUIItemStates(this OperationContext operCtx)
        {
            //if (operCtx == null) return;
            //if (operCtx.HtmlForm == null) return;

            //if (operCtx is BillOperationContext)
            //{
            //    var modelSrv = operCtx.Container.GetService<IMetaModelService>();
            //    var billMenuItems = modelSrv.GetBillMenu(operCtx.UserContext, operCtx.HtmlForm.Id);
            //    if (billMenuItems == null) return;

            //    foreach (JObject menuObj in billMenuItems)
            //    {
            //        JToken menuId;
            //        if (menuObj.TryGetValue("id", StringComparison.OrdinalIgnoreCase, out menuId)
            //            && menuId.Value<string>().EqualsIgnoreCase(operCtx.OperationNo))
            //        {
            //            JToken uiStates;
            //            if (menuObj.TryGetValue("uistates", StringComparison.OrdinalIgnoreCase, out uiStates))
            //            {
            //                if (uiStates is JArray)
            //                {
            //                    foreach (JObject uiState in uiStates)
            //                    {
            //                        JToken id, enabled, visible;
            //                        uiState.TryGetValue("id", StringComparison.OrdinalIgnoreCase, out id);
            //                        if (uiState.TryGetValue("enable", StringComparison.OrdinalIgnoreCase, out enabled))
            //                        {
            //                            operCtx.SetEnabled(id.Value<string>(), enabled.Value<bool>());
            //                        }
            //                        if (uiState.TryGetValue("visible", StringComparison.OrdinalIgnoreCase, out visible))
            //                        {
            //                            operCtx.SetVisible(id.Value<string>(), enabled.Value<bool>());
            //                        }
            //                    }
            //                }
            //            }
            //        }
            //    }
            //}
        }

        /// <summary>
        /// 返回当前页面或者指定页面的父页面Id 
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="pageId"></param>
        /// <returns></returns>
        public static string GetParentPageId(this OperationContext operCtx, string pageId = null)
        {
            if (pageId.IsNullOrEmptyOrWhiteSpace())
            {
                pageId = operCtx?.PageId;
            }
            var page = operCtx?.GetPage(pageId);
            return page?.ParentPageId ?? "";
        }

        /// <summary>
        /// 返回当前页面或者指定页面的父页面表单标识 
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="pageId"></param>
        /// <returns></returns>
        public static string GetParentPageFormId(this OperationContext operCtx, string pageId = null)
        {
            if (pageId.IsNullOrEmptyOrWhiteSpace())
            {
                pageId = operCtx?.PageId;
            }
            var page = operCtx?.GetPage(pageId);

            var parentPageId = page?.ParentPageId ?? "";
            var parentPage = operCtx?.GetPage(parentPageId);

            return parentPage?.FormId ?? "";
        }

        /// <summary>
        /// 设置页面缓存值
        /// </summary>
        /// <param name="sessionKey">缓存键</param>
        /// <param name="sessionValue">缓存值</param>
        /// <param name="targetFormId">目标表单Id，不传则默认为当前表单</param>
        /// <param name="pageId">页面Id，不传则默认为当前页面</param>
        public static void SetPageSessionValue(this OperationContext operCtx, string sessionKey, object sessionValue, string targetFormId = null, string pageId = null)
        {
            if (sessionKey.IsNullOrEmptyOrWhiteSpace()) return;

            var session = operCtx?.GetPageSession(pageId);
            if (session != null)
            {
                if (!targetFormId.IsNullOrEmptyOrWhiteSpace())
                {
                    sessionKey = $"{targetFormId}_{sessionKey}";
                }
                session[sessionKey] = sessionValue;
            }
        }

        /// <summary>
        /// 获取页面缓存值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="sessionKey">缓存键</param>
        /// <param name="defValue">未取到缓存值时的默认值</param>
        /// <param name="targetFormId">目标表单Id，不传则默认为当前表单</param>
        /// <param name="pageId">页面Id，不传则默认为当前页面</param>
        /// <returns>缓存值</returns>
        public static T GetPageSessionValue<T>(this OperationContext operCtx, string sessionKey, T defValue = default(T), string targetFormId = null, string pageId = null)
        {
            if (sessionKey.IsNullOrEmptyOrWhiteSpace()) return defValue;

            var session = operCtx?.GetPageSession(pageId);
            if (session == null) return defValue;

            if (!targetFormId.IsNullOrEmptyOrWhiteSpace())
            {
                sessionKey = $"{targetFormId}_{sessionKey}";
            }
            var objValue = session[sessionKey];
            if (objValue == null) return defValue;

            return (T)Convert.ChangeType(objValue, typeof(T));
        }

        /// <summary>
        /// 返回当前页面指定的页面对象实例
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="pageId"></param>
        /// <returns></returns>
        public static HtmlServerPage GetPage(this OperationContext operCtx, string pageId = null)
        {
            if ((operCtx.PageId.IsNullOrEmptyOrWhiteSpace() || operCtx.PageId.EqualsIgnoreCase(HtmlServerPage.IndexPageId))
                && (pageId.IsNullOrEmptyOrWhiteSpace() || pageId.EqualsIgnoreCase(HtmlServerPage.IndexPageId))) return null;

            if (pageId.IsNullOrEmptyOrWhiteSpace())
            {
                pageId = operCtx.PageId;
            }
            var pageMgr = operCtx.Container?.GetService<IPageManager>();
            var page = pageMgr?.GetPage(operCtx.UserContext, pageId);
            if (page == null)
            {
                //重置页面实例缓存区
                page = operCtx.RebuildPageInstance();
            }
            page.LastActiveTime = DateTime.Now;
            return page;
        }

        /// <summary>
        /// 获取页面数据共享区
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="pageId"></param>
        /// <returns></returns>
        public static PageSharedStorage GetPageSession(this OperationContext operCtx, string pageId = null)
        {
            if (pageId.IsNullOrEmptyOrWhiteSpace())
            {
                pageId = operCtx.PageId;
            }
            if (pageId.EqualsIgnoreCase(HtmlServerPage.IndexPageId)) return null;

            var page = operCtx?.GetPage(pageId);
            if (page == null) return null;

            var session = page.GetPageSession();
            return session;
        }

        /// <summary>
        /// 获取父页面数据共享区
        /// </summary>
        /// <param name="operCtx"></param>
        /// <returns></returns>
        public static PageSharedStorage GetParentPageSession(this OperationContext operCtx)
        {
            var page = operCtx?.GetPage();
            if (page == null) return null;
            var session = page.GetParentPageSession();
            return session;
        }

        /// <summary>
        /// 尽可能的重建页面实例
        /// </summary>
        /// <param name="operCtx"></param>
        /// <returns></returns>
        private static HtmlServerPage RebuildPageInstance(this OperationContext operCtx)
        {
            Enu_DomainType domainType = Enu_DomainType.Dynamic;
            Enu_BillStatus status = Enu_BillStatus.New;
            var pkId = "";
            if (operCtx is ListTreeOperationContext)
            {
                domainType = Enu_DomainType.ListTree;
                status = Enu_BillStatus.Query;
            }
            else if (operCtx is ListReportOperationContext)
            {
                domainType = Enu_DomainType.ListReport;
                status = Enu_BillStatus.Query;
            }
            else if (operCtx is ListOperationContext)
            {
                domainType = Enu_DomainType.List;
                status = Enu_BillStatus.Query;
            }
            else if (operCtx is BillOperationContext)
            {
                domainType = Enu_DomainType.Bill;
                if (operCtx.SelectedRows?.Any() == true)
                {
                    pkId = operCtx.SelectedRows.First().PkValue;
                }
                if (operCtx.DataEntities?.Any() == true)
                {
                    pkId = operCtx.DataEntities.First().GetPrimaryKeyValue<string>(false);
                }
                status = pkId.IsEmptyPrimaryKey() ? Enu_BillStatus.New : Enu_BillStatus.View;
            }
            else if (operCtx is ParameterOperationContext)
            {
                domainType = Enu_DomainType.Parameter;
                status = Enu_BillStatus.New;
            }
            else if (operCtx is ReportOperationContext)
            {
                domainType = Enu_DomainType.Report;
                status = Enu_BillStatus.New;
            }

            var page = HtmlServerPage.CreatePageInstance(operCtx.UserContext, operCtx.HtmlForm, domainType, Enu_OpenStyle.Default, status, HtmlServerPage.IndexPageId, pkId);
            page.PageId = operCtx.PageId;
            var pageMgr = operCtx.Container.GetService<IPageManager>();
            pageMgr.AddOrUpdatePage(operCtx.UserContext, page);
            return page;
        }

        /// <summary>
        /// 获取合法文件地址
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="fileId"></param>
        /// <param name="fileName"></param>
        /// <param name="fileDesc"></param>
        /// <returns></returns>
        public static JObject GetJsonImageUrl(this UserContext userCtx, string fileId, string fileName = "", string fileDesc = "")
        {
            var fileServer = userCtx.GetFileServerUrl();
            if (fileServer.IsNullOrEmptyOrWhiteSpace()) return null;
            if (fileId.IsNullOrEmptyOrWhiteSpace()) return null;

            var imageUrl = fileId.GetSignedFileUrl(false);
            JObject urlObj = new JObject();
            urlObj.Add("url", imageUrl);
            urlObj.Add("title", fileName);
            urlObj.Add("desc", fileDesc);
            return urlObj;
        }

        /// <summary>
        /// 获取签名的文件地址
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="thumbNail"></param>
        /// <returns></returns>
        public static string GetSignedFileUrl(this string fileId, bool thumbNail = false)
        {
            var fileServerUrl = fileId.GetFileServerUrl();

            return GetSignedFileUrl(fileServerUrl, fileId, thumbNail);
        }


        /// <summary>
        /// 获取签名的文件地址（本地文件服务器签名地址）
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="thumbNail"></param>
        /// <returns></returns>
        public static string GetSignedFileLocalUrl(this string fileId, bool thumbNail = false)
        {
            var fileServerUrl = fileId.GetLocalFileServerUrl();

            return GetSignedFileUrl(fileServerUrl, fileId, thumbNail);
        }



        /// <summary>
        /// 获取签名的文件地址
        /// </summary>
        /// <param name="fileServerUrl"></param>
        /// <param name="fileId"></param>
        /// <param name="thumbNail"></param>
        /// <returns></returns>
        private static string GetSignedFileUrl(string fileServerUrl, string fileId, bool thumbNail)
        {
            var timeStamp = DateTime.Now.AddMonths(1).Ticks;

            if (fileServerUrl.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("业务应用未配置文件服务器");
            }
            //文件id为空直接返回空地址，属正常请求
            if (fileId.IsNullOrEmptyOrWhiteSpace())
            {
                return "";
            }

            var urlSign = $"{fileId}#".SignData();
            var relativeUrl = $"fileinfo/file/{fileId}?ts=&type={(thumbNail ? 1 : 0)}&sign={urlSign}";

            return Path.Combine(fileServerUrl, relativeUrl);
        }





        /// <summary>
        /// 菜单按钮id与图片名称的映射缓存
        /// </summary>
        private static Dictionary<string, string> menuBtnIdAndNameMaps = null;
        /// <summary>
        /// 获取菜单图片地址
        /// </summary>
        /// <param name="menuBtnId"></param>
        /// <param name="userContext"></param>
        /// <returns></returns>
        public static string GetMenuIconUrl(this string menuBtnId, UserContext userContext)
        {
            const string menuBasePath = "fw/js/ydj/images/menu/";
            if (menuBtnIdAndNameMaps == null)
            {
                lock (menuBasePath)
                {
                    if (menuBtnIdAndNameMaps == null)
                    {
                        menuBtnIdAndNameMaps = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
                        string menuBaseLocalPath = Path.Combine(PathUtils.GetStartupPath(), menuBasePath.Replace('/', '\\'));
                        var files = Directory.GetFiles(menuBaseLocalPath);
                        if (files != null && files.Length > 0)
                        {
                            foreach (var file in files)
                            {
                                menuBtnIdAndNameMaps[Path.GetFileNameWithoutExtension(file)] = Path.GetFileName(file);
                            }
                        }
                    }
                }
            }

            if (string.IsNullOrWhiteSpace(menuBtnId) || menuBtnIdAndNameMaps.Keys.Contains(menuBtnId) == false)
            {
                //throw new BusinessException($"不存在id为{menuBtnId}的按钮菜单图片!");
                return string.Empty;
            }

            return string.Format(@"{0}{1}{2}", userContext.AppServer.ToString(), menuBasePath, menuBtnIdAndNameMaps[menuBtnId]);
        }

        /// <summary>
        /// 获取操作关联的服务
        /// </summary>
        /// <param name="container"></param>
        /// <param name="serviceId"></param>
        /// <returns></returns>
        public static IBaseService GetOperationService(this IServiceContainer container, string serviceId)
        {
            string strLifetimeScopeId = Guid.NewGuid().ToString();
            return container.BeginLifetimeScope(strLifetimeScopeId).GetServiceByMeta<IBaseService>(p =>
            {
                object sid = null;
                p.TryGetValue("serviceid", out sid);
                return serviceId?.EqualsIgnoreCase(sid as string) == true;
            });
        }



        /// <summary>
        /// 更新模型对应表结构
        /// </summary>
        /// <param name="ctx"></param>
        public static void UpdateMdlSchema(this OperationContext ctx, string formId, bool rebuild = false)
        {
            //if (ctx != null)
            //{
            //    UpdateMdlSchema(ctx.UserContext, formId);
            //}
        }

        /// <summary>
        /// 更新模型对应表结构
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="rebuild"></param>
        public static void UpdateMdlSchema(this UserContext ctx, string formId, bool rebuild = false)
        {
            //if (ctx != null && ctx.Container != null && !ctx.Company.IsNullOrEmptyOrWhiteSpace())
            //{
            //    Task task = new Task(() =>
            //    {
            //        var svc = ctx.Container.GetService<IDBSchemaService>();
            //        //svc.UpdateMdlMetaData(ctx, formId, rebuild);
            //    });

            //    ThreadWorker.QuequeTask(task, result =>
            //    {
            //        if (result?.Exception != null)
            //        {
            //            WinEventUtil.WriteError("线程执行错误：", result.Exception, 2018);
            //        }
            //    });
            //}
        }




        /// <summary>
        /// 按条件获取业务对象数据包（注意，是整个业务对象数据包）
        /// </summary>
        /// <param name="ctx"></param>
        public static List<DynamicObject> LoadBizDataByFilter(this UserContext ctx, string formId, string filter,
                                                                bool loadRef = false, IEnumerable<SqlParam> paramList = null)
        {
            if (filter.IsNullOrEmptyOrWhiteSpace())
            {
                return null;
            }
            ctx.UpdateMdlSchema(formId);
            var meta = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            var orgFld = meta.GetField("fmainorgid");
            if (meta.Isolate == "1" && orgFld != null)//按组织隔离
            {
                filter = "(fmainorgid='' or fmainorgid='0' or fmainorgid='{1}') and ({0})".Fmt(filter, ctx.Company);
            }
            else if (orgFld != null)//不按组织隔离，则取总部数据及自己组织的数据
            {
                // 父级企业过滤条件
                var parentCompanyIdFilter = "";
                if (!ctx.ParentCompanyId.IsNullOrEmptyOrWhiteSpace())
                {
                    parentCompanyIdFilter = $" or fmainorgid='{ctx.ParentCompanyId}'";
                }
                filter = "(fmainorgid='' or fmainorgid='0' or fmainorgid='{1}' or fmainorgid='{2}' {3}) and ({0})".Fmt(filter, ctx.Company, ctx.TopCompanyId, parentCompanyIdFilter);
            }


            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));
            var sql = @"select {0} from {1} with(nolock) where {2} ".Fmt(meta.HeadEntity.PkFieldName, meta.HeadEntity.TableName, filter);
            var reader = ctx.ExecuteReader(sql, paramList);
            var billData = dm.SelectBy(reader, OperateOption.Create(), false).OfType<DynamicObject>().ToList();

            if (loadRef)
            {
                ctx.Container.GetService<LoadReferenceObjectManager>()?.Load(ctx, meta.GetDynamicObjectType(ctx), billData, false);
            }

            return billData;
        }

        /// <summary>
        /// 按（条件+数据隔离规则）获取业务对象数据包（注意，是整个业务对象数据包）
        /// </summary>
        /// <param name="ctx"></param>
        public static List<DynamicObject> LoadBizDataByACLFilter(this UserContext ctx, string formId, string filter,
                                        bool loadRef = false, IEnumerable<SqlParam> paramList = null)
        {
            if (filter.IsNullOrEmptyOrWhiteSpace())
            {
                return null;
            }
            ctx.UpdateMdlSchema(formId);
            var meta = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(ctx, "", "");
            var orgFld = meta.GetField("fmainorgid");
            if (meta.Isolate == "1" && orgFld != null)//按组织隔离
            {
                if (aclFilter.IsNullOrEmptyOrWhiteSpace())
                {
                    filter = "(fmainorgid='' or fmainorgid='0' or or fmainorgid='{1}' ) and ({0})".Fmt(filter, ctx.Company);
                }
                else
                {
                    filter = "(fmainorgid='' or fmainorgid='0' or ({1})) and ({0})".Fmt(filter, aclFilter);
                }
            }
            else if (orgFld != null)//不按组织隔离，则取总部数据及自己组织的数据
            {
                if (aclFilter.IsNullOrEmptyOrWhiteSpace())
                {
                    filter = "(fmainorgid='' or fmainorgid='0' or fmainorgid='{1}' or fmainorgid='{2}') and ({0})".Fmt(filter, ctx.Company, ctx.TopCompanyId);
                }
                else
                {
                    filter = "(fmainorgid='' or fmainorgid='0' or ({1}) ) and ({0})".Fmt(filter, aclFilter);
                }
            }


            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));
            var sql = @"select {0} from {1} with(nolock) where {2} ".Fmt(meta.HeadEntity.PkFieldName, meta.HeadEntity.TableName, filter);
            var reader = ctx.ExecuteReader(sql, paramList);
            var billData = dm.SelectBy(reader, OperateOption.Create(), false).OfType<DynamicObject>().ToList();

            if (loadRef)
            {
                ctx.Container.GetService<LoadReferenceObjectManager>()?.Load(ctx, meta.GetDynamicObjectType(ctx), billData, false);

            }

            return billData;
        }


        /// <summary>
        /// 按条件获取业务对象的某些指定字段的数据
        /// 注意：字段不能跨多个平行表体
        /// </summary>
        /// <param name="ctx">上下文信息</param>
        /// <param name="formId">对应业务对象标识</param>
        /// <param name="filter">取数条件</param>
        /// <param name="fields">要获取的字段标识，多个用逗号或分号隔开</param>
        /// <returns></returns>
        public static List<DynamicObject> LoadBizDataByFilter(this UserContext ctx, string formId, string filter,
                                                            string fields, IEnumerable<SqlParam> paramList = null)
        {
            //禁止无条件范围数据，防止业务上偷懒直接查整表数据，如果业务上确实有表数据少要返回所有数据的，
            //可以传1=1的条件
            if (filter.IsNullOrEmptyOrWhiteSpace())
            {
                return null;
            }
            if (fields.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数错误，请传递要查询的数据字段");
            }

            var flds = fields.SplitKey();
            var sqlPara = new SqlBuilderParameter(ctx, formId);
            sqlPara.PageCount = -1;
            sqlPara.SelectedFieldKeys.AddRange(flds);
            sqlPara.QueryUserFieldOnly = true;
            sqlPara.FilterString = " ( {0} ) ".Fmt(filter);
            sqlPara.ReadDirty = true;
             
            var queryObject = QueryService.BuilQueryObject(sqlPara);

            var paramX = paramList?.ToList();
            if (paramX == null)
            {
                paramX = new List<SqlParam>();
            }
            foreach (var item in sqlPara.DynamicParams)
            {
                if(paramX.Any (f=>f.Name.EqualsIgnoreCase (item.Name)))
                {
                    continue;
                }

                paramX.Add(item);
            }

            var dbSvc = ctx.Container.GetService<IDBService>();
            var datas = dbSvc.ExecuteDynamicObject(ctx, queryObject.Sql, paramX);

            return datas.ToList();
        }



        /// <summary>
        /// 按主键获取业务对象数据包
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="pkId"></param>
        /// <param name="loadRef">是否加载引用属性</param>
        /// <returns></returns>
        public static DynamicObject LoadBizDataById(this UserContext ctx, string formId, string pkId, bool loadRef = false)
        {
            if (pkId.IsNullOrEmptyOrWhiteSpace())
            {
                return null;
            }
            ctx.UpdateMdlSchema(formId);
            var meta = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));
            var billData = dm.Select(pkId) as DynamicObject;

            if (loadRef)
            {
                ctx.Container.GetService<LoadReferenceObjectManager>()?.Load(ctx, meta.GetDynamicObjectType(ctx), billData, false);
            }

            return billData;
        }

        /// <summary>
        /// 按主键获取业务对象数据包
        /// </summary>
        /// <param name="ctx"></param>
        public static List<DynamicObject> LoadBizDataById(this UserContext ctx, string formId, IEnumerable<string> pkIds, bool loadRef = false)
        {
            if (pkIds.IsNullOrEmptyOrWhiteSpace())
            {
                return new List<DynamicObject>();
            }
            ctx.UpdateMdlSchema(formId);
            var meta = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));
            OperateOption op = OperateOption.InstanceBulkCopyAndNoCache;

            var ret = new List<DynamicObject>();
            var grpIds = pkIds.Distinct().GetListGroup(100);
            foreach (var item in grpIds)
            {
                var billData = dm.Select(item, op, false).OfType<DynamicObject>().ToList();
                ret.AddRange(billData);
            }

            if (loadRef)
            {
                ctx.Container.GetService<LoadReferenceObjectManager>()?.Load(ctx, meta.GetDynamicObjectType(ctx), ret, false);
            }

            return ret;
        }


        /// <summary>
        /// 按某个字段（如单据编码、基础资料编号）获取业务对象数据包
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="fldKey"></param>
        /// <param name="strNos">值列表，不要加引号</param>
        /// <returns></returns>
        public static List<DynamicObject> LoadBizDataByNo(this UserContext ctx, string formId, string fldKey, IEnumerable<string> strNos, bool loadRef = false)
        {
            if (strNos.IsNullOrEmptyOrWhiteSpace())
            {
                return new List<DynamicObject>();
            }
            ctx.UpdateMdlSchema(formId);
            var meta = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            var fld = meta.GetField(fldKey);
            if (fld == null)
            {
                return new List<DynamicObject>();
            }

            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));
            OperateOption op = OperateOption.InstanceBulkCopyAndNoCache;

            var ret = new List<DynamicObject>();
            var grpIds = strNos.Distinct().GetListGroup(100);
            foreach (var item in grpIds)
            {
                string filter = " {0} in ('{1}') ".Fmt(fldKey, string.Join("','", item));
                if (fld.ElementType.IsIntegerFld() || fld.ElementType.IsNumericFld())
                {
                    filter = " {0} in ({1}) ".Fmt(fldKey, string.Join(",", item));
                }
                var billData = LoadBizDataByFilter(ctx, formId, filter);
                ret.AddRange(billData);
            }

            if (loadRef)
            {
                ctx.Container.GetService<LoadReferenceObjectManager>()?.Load(ctx, meta.GetDynamicObjectType(ctx), ret, false);
            }

            return ret;
        }

        /// <summary>
        /// 根据主键ID加载业务单据头的指定字段数据
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formId">业务表单标识</param>
        /// <param name="pkId">主键ID</param>
        /// <param name="selectFieldName">指定要查询的单据头字段名称，多个字段用逗号分隔，比如：fname,fnumber</param>
        /// <returns>返回查询到的动态数据包，数据包中只包含主键ID和指定要查询的字段数据</returns>
        public static DynamicObject LoadBizBillHeadDataById(this UserContext userCtx, string formId, string pkId, string selectFieldName = "fname")
        {
            return LoadBizBillHeadDataById(userCtx, formId, new List<string> { pkId }, selectFieldName)?.FirstOrDefault();
        }

        /// <summary>
        /// 根据主键ID批量加载业务单据头的指定字段数据
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formId">业务表单标识</param>
        /// <param name="pkIds">主键ID集合</param>
        /// <param name="selectFieldName">指定要查询的单据头字段名称，多个字段用逗号分隔，比如：fname,fnumber</param>
        /// <returns>返回查询到的动态数据包，数据包中只包含主键ID和指定要查询的字段数据</returns>
        public static DynamicObjectCollection LoadBizBillHeadDataById(this UserContext userCtx, string formId, List<string> pkIds, string selectFieldName = "fname")
        {
            if (pkIds == null || !pkIds.Any()) return null;

            if (selectFieldName.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentNullException("参数 selectFieldName 不能为空！");
            }

            var metaService = userCtx.Container.GetService<IMetaModelService>();
            var formMeta = metaService.LoadFormModel(userCtx, formId);
            var pkFieldName = formMeta.BillPKFldName.ToLower();

            var sqlText = $@"select {pkFieldName} id,{selectFieldName} from {formMeta.BillHeadTableName} with(nolock)";
            var sqlParam = new List<SqlParam>();
            var dbService = userCtx.Container.GetService<IDBService>();

            var tempTable = "";

            DynamicObjectCollection dynObjs = null;
            using (var tran = userCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                if (pkIds.Count > 50)
                {
                    //用临时表关联查询
                    tempTable = dbService.CreateTempTableWithDataList(userCtx, pkIds,false);
                    sqlText = $@"
                select t.{pkFieldName} id,{selectFieldName} from {formMeta.BillHeadTableName} t with(nolock) 
                inner join {tempTable} tt with(nolock) on tt.fid=t.{pkFieldName}";
                }
                else if (pkIds.Count == 1)
                {
                    sqlParam.Add(new SqlParam("@fid", System.Data.DbType.String, pkIds[0]));
                    sqlText += $" where {pkFieldName}=@fid";
                }
                else
                {
                    var paramNames = new List<string>();
                    for (int i = 0; i < pkIds.Count; i++)
                    {
                        sqlParam.Add(new SqlParam($"@fid{i}", System.Data.DbType.String, pkIds[i]));
                        paramNames.Add($"@fid{i}");
                    }
                    sqlText += $" where {pkFieldName} in({string.Join(",", paramNames)})";
                }

                dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);

                tran.Complete();
            }
              
            if (!tempTable.IsNullOrEmptyOrWhiteSpace())
            {
                dbService.DeleteTempTableByName(userCtx, tempTable, true);
            }

            return dynObjs;
        }

        /// <summary>
        /// 保存业务单据数据(注意这个不走后端的插件业务逻辑，单纯只是保存数据到数据库，适用于类似更新某个字段的操作)
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="billData"></param>
        /// <returns></returns>
        public static void SaveBizData(this UserContext ctx, string formId, DynamicObject billData)
        {
            if (formId.IsNullOrEmptyOrWhiteSpace() || billData == null)
            {
                throw new Exception("参数错误");
            }


            ctx.UpdateMdlSchema(formId);
            var meta = HtmlParser.LoadFormMetaFromCache(formId, ctx);

            var prepareService = ctx.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(ctx, meta, new DynamicObject[] { billData }, null);

            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));
            dm.Save(billData);
        }

        /// <summary>
        /// 保存业务单据数据(注意这个不走后端的插件业务逻辑，单纯只是保存数据到数据库，适用于类似更新某个字段的操作)
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="billDatas"></param>
        /// <returns></returns>
        public static void SaveBizData(this UserContext ctx, string formId, IEnumerable<DynamicObject> billDatas)
        {
            if (formId.IsNullOrEmptyOrWhiteSpace() || billDatas == null)
            {
                throw new Exception("参数错误");
            }


            ctx.UpdateMdlSchema(formId);
            var meta = HtmlParser.LoadFormMetaFromCache(formId, ctx);

            var prepareService = ctx.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(ctx, meta, billDatas.ToArray(), null);

            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));
            OperateOption op = OperateOption.InstanceBulkCopyAndNoCache;
            dm.Save(billDatas, null, op);
        }

        /// <summary>
        /// 显示列表页面
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId">业务模型id</param>
        /// <param name="filterString">过滤条件</param>
        /// <param name="parentPageId">父页面Id</param>
        /// <returns></returns>
        public static object ShowListForm(this UserContext ctx, string formId, string filterString,
            string parentPageId, Enu_OpenStyle openStyle = Enu_OpenStyle.Default)
        {
            var metaSrv = ctx.Container?.TryGetService<IMetaModelService>();
            var formMeta = metaSrv.LoadFormModel(ctx, formId);
            List<FilterSchemeObject> filterSchemes = new List<FilterSchemeObject>();
            var schemeService = ctx.Container.GetService<IFilterSchemeService>();
            filterSchemes = schemeService.LoadFilterScheme(ctx, formMeta?.Id);
            List<ColumnObject> lstColumns = new List<ColumnObject>();
            foreach (var field in formMeta.GetFieldList())
            {
                lstColumns.AddRange(field.ToListColumn(ctx));
            }

            var action = ctx.ShowForm(formMeta,
                null,
                parentPageId,
                Enu_DomainType.List,
                openStyle,
                null,
                (formPara) =>
                {
                    var listFormPara = formPara as ListShowParameter;
                    if (listFormPara == null) return;
                    listFormPara.ListColumns = lstColumns.OrderBy(k => k.ListTabIndex).ToList();
                    listFormPara.FilterSchemes = filterSchemes;
                    listFormPara.FilterViewStyle = "Default";
                    listFormPara.FormCaption = formMeta.Caption;
                    if (!filterString.IsNullOrEmptyOrWhiteSpace())
                    {
                        listFormPara.FilterString = " ( {0} ) ".Fmt(filterString);
                    }
                    listFormPara.DynamicParam = "";
                    listFormPara.ListMode = Enu_ListMode.Default;
                });

            return action;
        }

        /// <summary>
        /// 获取指定的二级分销商的父级企业ID（也就是一级经销商企业ID）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="agentId">经销商ID</param>
        /// <returns>二级分销商的父级企业ID</returns>
        public static string GetAgentParentCompanyId(this UserContext userCtx, string agentId)
        {
            if (agentId.IsNullOrEmptyOrWhiteSpace()) return "";

            var sqlText = $"select forgid from t_bas_agent with(nolock) where fid=@agentId and fisreseller='1'";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@agentId", System.Data.DbType.String, agentId)
            };
            var dbService = userCtx.Container.GetService<IDBService>();
            DynamicObject dynObj = null;
            try
            {
                // ac站点、云链站点 没有经销商表 t_bas_agent 此处忽略掉该异常
                dynObj = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam)?.FirstOrDefault();
            }
            catch { }

            var parentCompanyId = Convert.ToString(dynObj?["forgid"]);

            return parentCompanyId;
        }




        #region DataManger

        static ConcurrentDictionary<string, IDataManager> _dataMangerDic = new ConcurrentDictionary<string, IDataManager>();

        /// <summary>
        /// 获取默认DataManager
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <returns></returns>
        public static IDataManager GetDefaultDataManager(this UserContext userCtx, HtmlForm htmlForm)
        {
            var companyDcInfo = userCtx.Companys.First(s => s.CompanyId.EqualsIgnoreCase(userCtx.Company));
            var server = DataCenterExtentions.GetDBServerInfor(companyDcInfo.DbServer);

            string key = $"{server.ServerIP}_{companyDcInfo.DBName}_{htmlForm.Id}";

            if (!_dataMangerDic.TryGetValue(key, out var dataManager))
            {
                dataManager = userCtx.Container.GetService<IDataManager>();
                dataManager.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));

                _dataMangerDic.TryAdd(key, dataManager);
            }

            return dataManager;
        }

        #endregion
    }
}
