using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.MetaCore.FormMeta;
using DlrDynaimcObject = System.Dynamic.DynamicObject;

namespace JieNor.Framework.AppService.MuSiExpression
{

    internal class BizDynamicDataRowList : DlrDynaimcObject, IEnumerable<object>
    {
        public HtmlForm HtmlForm { get; set; }

        public IEnumerable<object> ActiveDataObject { get; set; }

        public BizDynamicDataRowList(HtmlForm htmlForm)
        {
            this.HtmlForm = htmlForm;
        }

        public static implicit operator Array(BizDynamicDataRowList obj)
        {
            var objs = new List<object>();

            if (obj != null && obj.HtmlForm != null)
            {
                var fname = obj.HtmlForm.GetNameField()?.PropertyName;
                if (!fname.IsNullOrEmptyOrWhiteSpace())
                {
                    foreach (var item in obj.ActiveDataObject)
                    {
                        var objVal = item;
                        if (item is BizDynamicDataRow)
                        {
                            (item as BizDynamicDataRow).TryGetMember(fname, out objVal);
                        }
                        if (objVal.IsEmptyPrimaryKey()) continue;
                        objs.Add(objVal);
                    }
                }
            }

            return objs.ToArray();
        }


        /// <summary>
        /// 返回字段值
        /// </summary>
        /// <param name="binder"></param>
        /// <param name="result"></param>
        /// <returns></returns>
        public override bool TryGetMember(System.Dynamic.GetMemberBinder binder, out object result)
        {
            // TODO 获取业务对象上对应字段的值

            return this.TryGetMember(binder.Name, out result);
        }

        public virtual bool TryGetMember(string key, out object result)
        {
            result = null;

            if (this.ActiveDataObject == null) return false;

            List<object> lstObjs = new List<object>();

            foreach (var obj in this.ActiveDataObject)
            {
                object val = obj;
                if (obj is BizDynamicDataRow)
                {
                    (obj as BizDynamicDataRow).TryGetMember(key, out val);
                }
                if (val.IsEmptyPrimaryKey()) continue;
                lstObjs.Add(val);
            }
            if (this.ActiveDataObject.Any(f => f is BizDynamicDataRow && (f as BizDynamicDataRow).IsMergeToHead))
            {
                result = lstObjs.Distinct().ToList();
            }
            else
            {
                result = lstObjs;
            }

            return true;
        }

        public override bool TrySetMember(System.Dynamic.SetMemberBinder binder, object value)
        {
            // TODO 设置业务对象的值
            return this.TrySetMember(binder.Name, value);
        }


        public virtual bool TrySetMember(string key, object value)
        {
            throw new NotSupportedException("不支持赋值行为！");
        }

        public IEnumerator<object> GetEnumerator()
        {
            return this.ActiveDataObject as IEnumerator<object>;
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return this.ActiveDataObject.GetEnumerator();
        }
    }
}
