using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;



namespace JieNor.Framework.AppService.IMWorker.IMGroupService
{

    /// <summary>
    /// 解散聊天群
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMGroup)]
    [OperationNo("DissolveIMGroup")]
    public class DissolveIMGroup : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {

            var groupKey = this.GetQueryOrSimpleParam<string>("groupKey");
            string reason = this.GetQueryOrSimpleParam<string>("reason");
            var msgPiple = this.Container.GetService<IChatServiceProxy>();
            msgPiple.DissolveIMGroup(this.Context, groupKey, reason);
            this.Result.IsSuccess = true;

        }
    }



}
