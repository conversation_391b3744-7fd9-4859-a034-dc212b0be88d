using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;



namespace JieNor.Framework.AppService.IMWorker.IMGroupService
{

    /// <summary>
    /// 获取所有聊天群组信息
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMGroup)]
    [OperationNo("GetAllIMGroupList")]
    public class GetAllIMGroupList : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var msgPiple = this.Container.GetService<IChatServiceProxy>();
            this.Result.SrvData = msgPiple.GetAllIMGroupList(this.Context);

            this.Result.IsSuccess = true;
        }
    }



}
