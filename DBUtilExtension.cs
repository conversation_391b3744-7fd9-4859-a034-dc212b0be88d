using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// orm读取数据包的一些pkid读取扩展方法
    /// </summary>
    public static class DBUtilExtension
    {
        /// <summary>
        /// 启用db事务执行一段逻辑
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="action"></param>
        /// <param name="tranType"></param>
        public static void DoActionWithDbTransaction(this UserContext userCtx, Action action, TransactionScopeOption tranType = TransactionScopeOption.Required)
        {
            using (var tran = userCtx.CreateTransaction((int)tranType))
            {
                try
                {
                    action?.Invoke();
                    tran.Complete();
                }
                catch
                {
                    throw;
                }
            }
        }

        /// <summary>
        /// 根据表单模型及编码信息返回datareader
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="numbers"></param>
        /// <returns></returns>
        public static IDataReader GetPkIdDataReaderWithNumber(this UserContext userCtx, HtmlForm htmlForm, IEnumerable<string> numbers)
        {
            var numberField = htmlForm.GetNumberField();
            numberField.ThrowIfNull("编码字段不存在！");
            if (numbers == null || numbers.Any() == false) return null;

            return userCtx.GetPkIdDataReaderWithHeadField(htmlForm, numberField.Id, numbers);
        }

        /// <summary>
        /// 根据表单模型及名称信息返回datareader
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="names"></param>
        /// <returns></returns>
        public static IDataReader GetPkIdDataReaderWithName(this UserContext userCtx, HtmlForm htmlForm, IEnumerable<string> names)
        {
            var nameField = htmlForm.GetNameField();
            nameField.ThrowIfNull("编码字段不存在！");

            return userCtx.GetPkIdDataReaderWithHeadField(htmlForm, nameField.Id, names);
        }

        /// <summary>
        /// 根据指定表头字段获得过滤主键的读取器对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="fieldKey"></param>
        /// <param name="fieldValues"></param>
        /// <param name="extendWhereString"></param>
        /// <returns></returns>
        public static IDataReader GetPkIdDataReaderWithHeadField(this UserContext userCtx, HtmlForm htmlForm, string fieldKey, IEnumerable<object> fieldValues, string extendWhereString = "")
        {
            var field = htmlForm.GetField(fieldKey);
            field.ThrowIfNull($"字段不存在:{fieldKey}！");
            if (fieldValues.IsNullOrEmpty()) return null;

            var fieldName = field.FieldName;
            List<SqlParam> lstParam = new List<SqlParam>();
            lstParam.Add(new SqlParam(fieldName, DbType.String, fieldValues.FirstOrDefault()));
            string whereString = $"{fieldName} = @{fieldName}";

            var tempTable = "";
            var dbService = userCtx.Container.GetService<IDBService>();

            if (fieldValues.IsGreaterThan(1))
            {
                lstParam.Clear();
                if (fieldValues.IsGreaterThan(50))
                {

                    tempTable = dbService.CreateTempTableWithDataList(userCtx, fieldValues);
                    whereString = $" exists (select 1 from {tempTable} where {tempTable}.fid={field.FieldName}) ";
                }
                else
                {
                    whereString = $"{field.FieldName} in ({{0}})";
                    var paramString = "";
                    int i = 0;
                    foreach (var name in fieldValues)
                    {
                        paramString += $"@{fieldName}{i},";
                        lstParam.Add(new SqlParam($"{fieldName}{i}", DbType.String, name));
                        i++;
                    }
                    whereString = whereString.Fmt(paramString.Trim().TrimEnd(','));
                }
            }

            var result = userCtx.GetPkIdDataReader(htmlForm, whereString.JoinFilterString(extendWhereString), lstParam);

            if (!tempTable.IsNullOrEmptyOrWhiteSpace())
            {
                dbService.DeleteTempTableByName(userCtx, tempTable, true);
            }

            return result;
        }


        static Regex _regexMainorgid = new Regex(@"\bfmainorgid\b");

        /// <summary>
        /// 根据表单模型及表头过滤条件返回datareader
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="whereString"></param>
        /// <param name="paraList"></param>
        /// <param name="withNoLock"></param>
        /// <returns></returns>
        public static IDataReader GetPkIdDataReader(this UserContext userCtx, HtmlForm htmlForm, string whereString, IEnumerable<SqlParam> paraList,bool withNoLock=true)
        {
            var sqlParaList = new List<SqlParam>(paraList);
            //如果调用方传了fmainorgid，则这里不做处理
            if (htmlForm.Isolate.EqualsIgnoreCase("1")
                && !_regexMainorgid.IsMatch(whereString))
            {
                var mainOrgIdField = htmlForm.GetField("fmainorgid");
                if (mainOrgIdField != null)
                {
                    var existParam = paraList.FirstOrDefault(o => o.Name.EqualsIgnoreCase("currentCompanyId")
                          || o.Name.EqualsIgnoreCase("@currentCompanyId"));
                    if (existParam == null)
                        sqlParaList.Add(new SqlParam("currentCompanyId", DbType.String, userCtx.Company));
                    whereString = whereString.JoinFilterString($"({mainOrgIdField.FieldName}=@currentCompanyId or {mainOrgIdField.FieldName}='0')");
                }
            }

            string strPkIdSql = $"select {htmlForm.BillPKFldName} from {htmlForm.BillHeadTableName}{(withNoLock? " with(nolock) ":string.Empty)} where 1=1 {(whereString.IsNullOrEmptyOrWhiteSpace() ? "" : " and " + whereString)}";
            var dbService = userCtx.Container.GetService<IDBService>();
            return dbService.ExecuteReader(userCtx, strPkIdSql, sqlParaList, CommandType.Text);
        }


        /// <summary>
        /// 执行sql，返回datareader
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="sql"></param> 
        /// <param name="paraList"></param>
        /// <returns></returns>
        public static IDataReader ExecuteReader(this UserContext userCtx, string sql, IEnumerable<SqlParam> paraList)
        {
            var sqlParaList = new List<SqlParam>();
            if (paraList != null)
            {
                sqlParaList.AddRange(paraList);
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            return dbService.ExecuteReader(userCtx, sql, sqlParaList, CommandType.Text);
        }

        /// <summary>
        /// 执行sql，返回datareader
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="sql"></param> 
        /// <param name="paraList"></param>
        /// <returns></returns>
        public static DynamicObjectCollection ExecuteDynamicObject(this UserContext userCtx, string sql, IEnumerable<SqlParam> paraList)
        {
            var sqlParaList = new List<SqlParam>();
            if (paraList != null)
            {
                sqlParaList.AddRange(paraList);
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            return dbService.ExecuteDynamicObject(userCtx,sql, sqlParaList );
        }

    }
}
