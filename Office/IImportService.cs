using JieNor.Framework.DataTransferObject.Report;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{

    /// <summary>
    /// Excel数据导入接口
    /// </summary>
    public interface IImportService
    {


        /// <summary>
        /// 生成导入的Excel模板
        /// </summary>
        /// <param name="formId">业务对象标识</param>
        /// <returns>模板文件名（全路径）</returns>
        string BuildTemplate(UserContext ctx, string formId);




        /// <summary>
        /// Excel数据导入
        /// </summary>
        /// <param name="excelFile">文件名（全路径）</param>
        /// <returns>导入的详细情况信息文件名（全路径）</returns>
        string ImportData(UserContext ctx, string  excelFile);

        /// <summary>
        /// 导入文件流
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="excelFileStream">文件流</param>
        /// <returns></returns>
        string ImportData(UserContext ctx, Stream excelFileStream, string fileName);


        /// <summary>
        /// 下载基础资料数据到Excel
        /// </summary>
        /// <param name="ctx">上下文信息</param>
        /// <param name="formId">业务对象标识</param>
        /// <returns>模板文件名（全路径）</returns>
        void  DownloadBaseData(UserContext ctx, string formId);





    }


}
