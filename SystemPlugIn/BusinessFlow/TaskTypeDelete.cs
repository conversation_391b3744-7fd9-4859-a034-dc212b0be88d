using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.BusinessFlow
{
    /// <summary>
    /// 任务类型--删除
    /// </summary>
    [InjectService]
    [FormId("bf_tasktype")]
    [OperationNo("Delete")]
    public class TaskTypeDelete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return TypeDelete(newData); 
            }).WithMessage("任务类型**正被任务引用，不允许删除！\n"));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    return TypeForbid(newData);
            //}).WithMessage("如果当前任务类型已禁用，不允许删除！"));
        }
               
        /// <summary>
        /// 任务类型**正被任务引用，不允许删除！
        /// </summary>
        /// <param name="newdata"></param>
        /// <returns></returns>
        private bool TypeDelete(DynamicObject newdata)
        {
            bool result = true;

            string id = newdata["Id"].ToString();
            string sql = @"select fid from t_bf_task
                            where ftasktype=@id";
            SqlParam headParam = new SqlParam("@id", System.Data.DbType.String, id);
            using (var reader = this.DBService.ExecuteReader(this.Context, sql, headParam))
            {
                while (reader.Read())
                {
                    result = false;
                    break;
                }                
            }
            return result;
        }
    }
}
