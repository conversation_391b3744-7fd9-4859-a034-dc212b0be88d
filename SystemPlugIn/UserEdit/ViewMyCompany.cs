using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.UserEdit
{
    /// <summary>
    /// 查看我的企业接口插件
    /// </summary>
    [InjectService]
    [FormId("sys_company")]
    [OperationNo("view")]
    public class ViewMyCompany : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 操作前，提供查看企业的主键
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var isGetMyCompany = this.GetQueryOrSimpleParam<bool>("getMyCompany", false);
            if (isGetMyCompany)
            {
                //添加查看表单的主键信息
                this.SimpleData["id"] = this.Context.Company;

                var currCompany = this.Context.Companys.FirstOrDefault(f => f.CompanyId.EqualsIgnoreCase(this.Context.Company));
                if (currCompany == null)
                {
                    throw new BusinessException("当前登录会话异常，请重新登录系统！");
                }
                //检查一下，当前企业是否存在，不存在，先插一条记录
                var dm = this.GetDataManager();
                dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
                var companyInfo = dm.Select(this.Context.Company) as DynamicObject;
                if (companyInfo == null)
                {
                    companyInfo = this.HtmlForm.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
                    companyInfo["id"] = currCompany.CompanyId;
                    companyInfo["fnumber"] = currCompany.CompanyNumber;
                    companyInfo["fname"] = currCompany.CompanyName;

                    var preSaveService = this.Container.GetService<IPrepareSaveDataService>();
                    preSaveService.PrepareDataEntity(this.Context, this.HtmlForm, new DynamicObject[] { companyInfo }, OperateOption.Create());
                }

                //根据当前企业ID从EIS站点中获取企业其他信息
                var eisResponse = this.Gateway.Invoke<DynamicDTOWrapper>(this.Context,
                    TargetSEP.EisService,
                    new CommonBillDTO()
                    {
                        FormId = "eis_company",
                        OperationNo = "getcompanydetail",
                        SimpleData = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase) { { "id", currCompany.CompanyId } }
                    });
                if (eisResponse is DynamicDTOResponse)
                {
                    var srvData = (eisResponse as DynamicDTOResponse).OperationResult.SrvData as string;
                    List<Dictionary<string, string>> dicSrvData = srvData?.FromJson<List<Dictionary<string, string>>>() ?? new List<Dictionary<string, string>>();
                    if (dicSrvData != null && dicSrvData.Count > 0 && dicSrvData[0] != null)
                    {
                        if (dicSrvData[0].ContainsKey("fcontactid")) companyInfo["fcontactid"] = dicSrvData[0]["fcontactid"];
                        if (dicSrvData[0].ContainsKey("fcontactphone")) companyInfo["fcontactphone"] = dicSrvData[0]["fcontactphone"];
                        if (dicSrvData[0].ContainsKey("fenterprisescale")) companyInfo["fenterprisescale"] = dicSrvData[0]["fenterprisescale"];
                        if (dicSrvData[0].ContainsKey("fprovince")) companyInfo["fprovince"] = dicSrvData[0]["fprovince"];
                        if (dicSrvData[0].ContainsKey("fcity")) companyInfo["fcity"] = dicSrvData[0]["fcity"];
                        if (dicSrvData[0].ContainsKey("fregion")) companyInfo["fregion"] = dicSrvData[0]["fregion"];
                        if (dicSrvData[0].ContainsKey("faddress")) companyInfo["faddress"] = dicSrvData[0]["faddress"];
                        //if (dicSrvData[0].ContainsKey("fdescription")) companyInfo["fdescription"] = dicSrvData[0]["fdescription"];
                        if (dicSrvData[0].ContainsKey("fcontactposition")) companyInfo["fcontactposition"] = dicSrvData[0]["fcontactposition"];
                        if (dicSrvData[0].ContainsKey("fcontactmail")) companyInfo["fcontactmail"] = dicSrvData[0]["fcontactmail"];
                        if (dicSrvData[0].ContainsKey("fcompanyurl")) companyInfo["fcompanyurl"] = dicSrvData[0]["fcompanyurl"];
                    }
                }

                dm.Save(companyInfo);
            }
        }
    }
}
