using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.UsrMgr;

namespace JieNor.Framework.AppService.SystemPlugIn.Sec.AssignRightAllot
{
    /// <summary>
    /// 角色分配：加载用户列表
    /// </summary>
    [InjectService]
    [FormId("sec_assignrightallot")]
    [OperationNo("loaduser")]
    public class LoadUser : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var roleId = this.GetQueryOrSimpleParam<string>("roleId", "");
            if (roleId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数 roleId 为空，请检查！");
            }

            List<string> companyIdList = null;
            var companyIds = this.GetQueryOrSimpleParam<string>("companyIds", "");
            if (!companyIds.IsNullOrEmptyOrWhiteSpace())
            {
                companyIdList = companyIds.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
            }
            if (companyIdList == null || companyIdList.Count <= 0)
            {
                throw new BusinessException("企业Id参数 companyIds 为空 ，请检查！");
            }

            var paramList = new List<SqlParam>
            {
                new SqlParam("@froleid", System.Data.DbType.String, roleId),
                new SqlParam("@currentCompanyId", System.Data.DbType.String, this.Context.Company),
            };
            var mainOrgWhere = "";
            if (companyIdList.Count == 1)
            {
                mainOrgWhere = " and u.fmainorgid=@fmainorgid";
                paramList.Add(new SqlParam("@fmainorgid", System.Data.DbType.String, companyIdList[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < companyIdList.Count; i++)
                {
                    paramNames.Add($"@cid{i}");
                    paramList.Add(new SqlParam($"@cid{i}", System.Data.DbType.String, companyIdList[i]));
                }
                mainOrgWhere = $" and u.fmainorgid in({string.Join(",", paramNames)})";
            }

            //用户列表查询过滤条件，非管理员账号时，查询时将超级管理员与云链账号隐藏
            var filterString = "";
            var permService = this.Container.GetService<IPermissionService>();
            if (!permService.IsRoleAdmin(this.Context, new MetaCore.PermData.PermAuth(this.Context)))
            {
                filterString = " and not exists (select 1 from (select min(m1.fid) fid from t_sec_user m1 where m1.fmainorgid=@currentCompanyId) u1 where u1.fid=u.fid) and u.fmainorgid<>u.fnumber";
            }

            var sqlText = $@"
            select u.fid fuserid,u.fnumber fusername,u.fname,u.fmainorgid fusercompanyid,ur.froleid from t_sec_user u 
            left join t_sec_roleuser ur on ur.fuserid=u.fid and ur.froleid=@froleid 
            where u.fforbidstatus!='1' {mainOrgWhere} {filterString} 
            order by u.fmainorgid asc,u.fid asc";

            List<Dictionary<string, object>> dicUsers = new List<Dictionary<string, object>>();
            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, paramList))
            {
                while (reader.Read())
                {
                    Dictionary<string, object> dicUser = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        var colValue = reader[i];
                        var colName = reader.GetName(i);
                        dicUser[colName] = colValue.IsNullOrEmptyOrWhiteSpace() ? "" : colValue;
                    }

                    //此处取 DataCenter 中的企业名称
                    var companyName = string.Empty;
                    var companyId = dicUser["fusercompanyid"] as string;
                    if (!companyId.IsNullOrEmptyOrWhiteSpace())
                    {
                        var company = companyId.GetCompany();
                        if (company != null)
                        {
                            companyName = company?.CompanyName ?? "";
                        }
                    }
                    dicUser["fusercompanyname"] = companyName;

                    dicUsers.Add(dicUser);
                }
            }

            this.Result.IsSuccess = true;
            this.Result.SrvData = dicUsers;
        }
    }
}