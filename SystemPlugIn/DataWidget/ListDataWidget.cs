using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.DataWidget
{
    /// <summary>
    /// 列出系统所有可用组件：注意按用户权限进行过滤
    /// </summary>
    [InjectService]
    [FormId("sys_dashboard")]
    [OperationNo("listdatawidget")]
    public class ListDataWidget : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 列出系统所有组件
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            //if (!this.Option.HasInteractionFlag("interaction1"))
            //{
            //    throw new InteractionException("interaction1", "检测到系统存在同名资料，是否继续？");
            //}

            //获取数据组件类型的树
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "sys_datawidgettype");
            var result = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, null, "QueryListTree", new Dictionary<string, object>());
            result?.ThrowIfHasError(true, $"获取{htmlForm.Caption}树结构数据失败!");

            var dataWidgetTypeTree = result.SrvData as ListTreeNode;
            if (dataWidgetTypeTree == null)
            {
                dataWidgetTypeTree = new ListTreeNode()
                {
                    Name = "所有",
                    Filter = "",
                    Children = new List<ListTreeNode>(),
                    Extend = new Dictionary<string, string>
                    {
                        { "nodeType","0"},
                        { "formId",string.Empty},
                        { "icon",string.Empty}
                    }
                };
            }

            this.Result.SrvData = dataWidgetTypeTree;

            var leafNodes = getAllLeafTreeNode(dataWidgetTypeTree);

            if (leafNodes == null || leafNodes.Count <= 0)
            {
                return;
            }

            //根据叶子结点获取数据组件
            var dataWidgetTypeIds = leafNodes.Select(x => x.Id).Where(x => false == string.IsNullOrWhiteSpace(x)).Distinct().ToList();
            if (dataWidgetTypeIds == null || dataWidgetTypeIds.Count <= 0)
            {
                return;
            }

            var dataWidgetListForm = this.MetaModelService.LoadFormModel(this.Context, "sys_datawidgetlist");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, dataWidgetListForm.GetDynamicObjectType(this.Context));
            var where = new StringBuilder(" (fmainorgid=@fmainorgid or fmainorgid='0') and fwidgettypeid ");
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company)
            };

            if (dataWidgetTypeIds.Count == 1)
            {
                where.Append(" = @fwidgettypeid");
                sqlParams.Add(new SqlParam("@fwidgettypeid", System.Data.DbType.String, dataWidgetTypeIds[0]));
            }
            else
            {
                where.Append(" in (");
                where.Append(string.Join(",", dataWidgetTypeIds.Select((x, i) => $"@fwidgettypeid{i}")));
                where.Append(" ) ");
                sqlParams.AddRange(dataWidgetTypeIds.Select((x, i) => new SqlParam($"@fwidgettypeid{i}", System.Data.DbType.String, x)));
            }

            var dataReader = this.Context.GetPkIdDataReader(dataWidgetListForm, where.ToString(), sqlParams);
            var dataWidgetList = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();

            if (dataWidgetList == null || dataWidgetList.Count <= 0)
            {
                return;
            }

            //如果当前企业自己有保存过小组件，则排除系统预置的小组件
            this.ExcludeSysPresetWidget(dataWidgetList);

            var permId = (this.OperationContext.UserContext.ClientType & (long)Enu_ClientType.Mobile) == (long)Enu_ClientType.Mobile ?
                                "mobile_view" : "fw_view";
            var permissionService = this.Container.GetService<IPermissionService>();
            var widgetFormIds = dataWidgetList.Select(x => Convert.ToString(x["fwidgetformid"]))
                                              .Where(x => false == string.IsNullOrWhiteSpace(x))
                                              .Distinct()
                                              .Where(x => permissionService.HasPermission(this.Context, new MetaCore.PermData.PermAuth(this.Context) { FormId = x, PermId = permId }))
                                              .ToList();
            if (widgetFormIds == null || widgetFormIds.Count <= 0)
            {
                return;
            }


            dataWidgetList = dataWidgetList.Where(x => widgetFormIds.Contains(Convert.ToString(x["fwidgetformid"]))).ToList();

            foreach(var dataWidget in dataWidgetList)
            {
                var widgetTypeId = Convert.ToString(dataWidget["fwidgettypeid"]);
                var leafNode = leafNodes.FirstOrDefault(x => x.Id == widgetTypeId);
                if (leafNode != null)
                {
                    var icon = Convert.ToString(dataWidget["ficon"]);
                    icon = string.IsNullOrWhiteSpace(icon) ? string.Empty : string.Concat(this.Context.AppServer.ToString(), icon.Substring(1));
                    leafNode.Children.Add(new ListTreeNode
                    {
                        Id = Convert.ToString(dataWidget["id"]),
                        Name = Convert.ToString(dataWidget["fname"]),
                        Children = new List<ListTreeNode>(),
                        Extend = new Dictionary<string, string>
                        {
                            { "nodeType","2"},
                            { "formId",dataWidgetListForm.Id},
                            { "icon",icon}
                        }
                    });
                }
            }

        }

        /// <summary>
        /// 如果当前企业自己有保存过小组件，则排除系统预置的小组件
        /// </summary>
        /// <param name="widgetList"></param>
        private void ExcludeSysPresetWidget(List<DynamicObject> widgetList)
        {
            //系统预置的
            var sysPresets = new List<DynamicObject>();

            //非系统预置的并且是基于系统预置重新保存的数据
            var nonSysPresets = widgetList.Where(o => !o["fprimitiveid"].IsNullOrEmptyOrWhiteSpace());
            foreach (var item in nonSysPresets)
            {
                var sysPreset = widgetList.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(item["fprimitiveid"])));
                if (sysPreset != null)
                {
                    sysPresets.Add(sysPreset);
                }
            }

            //移除系统预置的
            foreach (var item in sysPresets)
            {
                widgetList.Remove(item);
            }
        }

        /// <summary>
        /// 获取所有叶子结点
        /// </summary>
        /// <param name="rootNode"></param>
        /// <param name="currentNode"></param>
        /// <returns></returns>
        private List<ListTreeNode> getAllLeafTreeNode(ListTreeNode rootNode, ListTreeNode currentNode = null)
        {
            List<ListTreeNode> leafNodes = new List<ListTreeNode>();

            if (currentNode == null)
            {
                currentNode = rootNode;
            }

            if (currentNode.Children == null || currentNode.Children.Count <= 0)
            {
                if (rootNode != currentNode)
                {
                    leafNodes.Add(currentNode);
                }
                return leafNodes;
            }

            foreach (var nextNode in currentNode.Children)
            {
                leafNodes.AddRange(getAllLeafTreeNode(rootNode, nextNode));
            }

            return leafNodes;
        }
    }
}
