using System;
using System.Linq;
using System.Text.RegularExpressions;

using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System.Collections.Generic;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.DataTransferObject;

namespace JieNor.Framework.AppService.SystemPlugIn.OpSerConfig
{
    /// <summary>
    /// 实现短信服务配置页面字段列表的过滤逻辑
    /// </summary>
    [InjectService]
    [FormId("sys_smssereditor")]
    [OperationNo("queryfield")]
    public class QueryField : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName.ToLower())
            {
                case "onfilterqueryfield":
                    var eventData = e.EventData as Tuple<string, string>;
                    if (eventData == null) return;
                    var formModel = this.MetaModelService.LoadFormModel(this.Context, eventData.Item2);
                    if (formModel == null) return;
                    var allFieldList = formModel.GetFieldList();
                    switch (eventData.Item1.ToLower())
                    {
                        case "ffield":
                            e.Cancel = true;
                            var textFieldList = allFieldList.Where(o => o is HtmlTextField && (o as HtmlTextField).IsBillHeadField);
                            e.Result = textFieldList.GroupBy(o => o.Group)
                                .Select(o => new
                                {
                                    group = o.Key.IsNullOrEmptyOrWhiteSpace() ? "基本" : o.Key,
                                    fields = o.OrderBy(f => f.TabIndex).Select(f => new
                                    {
                                        id = f.Id,
                                        name = f.Caption
                                    }),
                                })
                                .ToArray();
                            break;
                        case "fbdfield":
                            e.Cancel = true;
                            var baseFieldList = allFieldList.Where(o => o is HtmlBaseDataField && (o as HtmlBaseDataField).IsBillHeadField);
                            e.Result = baseFieldList.GroupBy(o => o.Group)
                                .Select(o => new
                                {
                                    group = o.Key.IsNullOrEmptyOrWhiteSpace() ? "基本" : o.Key,
                                    fields = o.OrderBy(f => f.TabIndex).Select(f => new
                                    {
                                        id = f.Id,
                                        name = f.Caption
                                    }),
                                })
                                .ToArray();
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
        }
    }
}