using System;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.FileServer;

namespace JieNor.Framework.AppService.SystemPlugIn.FileSynergy
{
    /// <summary>
    /// 接收协同文件
    /// </summary>
    [InjectService("AcceptSynergy")]
    public class AcceptSynergy : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return "";
            }
        }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return "";
            }
        }

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            string cooFilesJson = this.GetQueryOrSimpleParam<string>("cooFiles");
            if (cooFilesJson.IsNullOrEmptyOrWhiteSpace()) return;

            JArray cooFiles = cooFilesJson.FromJson<JArray>();
            if (cooFiles != null)
            {
                //当前站点的文件服务器地址
                var fileServerUrl = this.GetFileServerUrl();

                //向当前站点文件服务器发送文件协同接收
                var fileServerResponse = this.Gateway.Invoke<FileSynAppplyDTOResponse>(this.OperationContext.UserContext,
                    new TargetServer()
                    {
                        Host = fileServerUrl,
                        TokenId = "",
                        Headers = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
                        {
                            { "reqType", "FileCoordinate" },
                            { "appCode", "66666" },
                            { "timeStamp", DateTime.Now.ToString("yyyyMMddHHmmssfff") }
                        }
                    },
                    new FileSynAppplyDTO()
                    {
                        fisholder = false,
                        coofiles = cooFiles
                    });
            }
        }
    }
}