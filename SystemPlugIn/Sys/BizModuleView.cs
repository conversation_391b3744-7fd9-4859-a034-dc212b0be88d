//using JieNor.Framework;
//using JieNor.Framework.AppService.SystemPlugIn.Auth;
//using JieNor.Framework.DataEntity.MainFw;
//using JieNor.Framework.Interface;
//using JieNor.Framework.Interface.Cache;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.PermData;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.SuperOrm.DataManager;
//using Newtonsoft.Json.Linq;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace JieNor.Framework.AppService.SystemPlugIn.Sys
//{
//    /// <summary>
//    /// 菜单视图实现类
//    /// </summary>
//    [InjectService]
//    public class BizModuleView : IBizModuleView
//    {

//        static readonly string __CachePrefix = "sysmenu:viewtype:{0}:{1}";


//        /// <summary>
//        /// 返回可见的菜单视图
//        /// </summary>
//        /// <param name="ctx"></param>
//        /// <param name="bizMdlItem"></param>
//        /// <returns></returns>
//        public IEnumerable<BizModuleItem> FilterBizModule(UserContext ctx,IEnumerable<BizModuleItem> bizMdlItem)
//        {
//            //if(ctx.IsAdminRole (ctx.Company) || PermHelper.IsRoleAdmin(ctx, new PermAuth(ctx)))
//            //{
//            //    return bizMdlItem;
//            //}

//            JArray viewMenu=new JArray();
//            if( !HaveUserViewType(ctx,ref viewMenu))
//            {
//                return bizMdlItem;
//            }
            
//            List<BizModuleItem> ret = new List<BizModuleItem>();
//            foreach (var mdl in bizMdlItem)
//            {
//                var m = mdl.ToJson().FromJson<BizModuleItem>();
//                m.MenuGroups = new List<MenuGroupItem>();                
//                foreach (var grp in mdl.MenuGroups)
//                {
//                    var g = grp.ToJson().FromJson<MenuGroupItem>();
//                    g.MenuItems = new List<MenuEntryItem>();                    
//                    foreach (var menu in grp.MenuItems)
//                    {
//                        if(viewMenu.Any (f=>f.ToString().EqualsIgnoreCase (menu.Id) ))
//                        {
//                            g.MenuItems.Add(menu);
//                        }
//                    }
//                    if (g.MenuItems.Count > 0)
//                    {
//                        m.MenuGroups.Add(g);
//                    }
//                }
//                if (m.MenuGroups.Count > 0)
//                {
//                    ret.Add(m);
//                }
//            }

//            return ret;
//        }


//        /// <summary>
//        /// 获取用户视图类型里面设置的菜单列表
//        /// </summary>
//        /// <param name="ctx"></param>
//        /// <param name="viewMenu"></param>
//        /// <returns></returns>
//        private bool HaveUserViewType(UserContext ctx,ref JArray viewMenu)
//        {
//            var key = __CachePrefix.Fmt(ctx.Company, ctx.UserId);
//            var cache = ctx.Container.GetService<IRedisCache>();
//            viewMenu = cache.Get<JArray>(ctx, key);
//            if (viewMenu == null || viewMenu.Count == 0)
//            {
//                var viewTypeMeta = ctx.Container.GetService<IMetaModelService>().LoadFormModel(ctx, "sys_viewtype");
//                var dm = ctx.Container.GetService<IDataManager>();
//                dm.InitDbContext(ctx, viewTypeMeta.GetDynamicObjectType(ctx));

//                var userMeta = ctx.Container.GetService<IMetaModelService>().LoadFormModel(ctx, "sec_user");
//                dm.InitDbContext(ctx, userMeta.GetDynamicObjectType(ctx));
//                var userInfo = dm.Select(ctx.UserId) as DynamicObject;
//                if (userInfo == null || userInfo["fviewtype"].IsNullOrEmptyOrWhiteSpace())
//                {
//                    return false;
//                }

//                var userView = userInfo["fviewtype"].ToString().SplitKey();
//                var viewTypeId = string.Join("','", userView);

//                var sql = @"select distinct t0.fmenuid
//                            from t_sys_menuitem t0
//                            inner join t_sys_viewtypeentry t1 on t0.fmenuid=t1.fmenuid 
//                            where t1.fvisible='1' and t1.fid in ('{0}')".Fmt(viewTypeId);
//                var dbSvc = ctx.Container.GetService<IDBService>();
//                var datas = dbSvc.ExecuteDynamicObject(ctx, sql);

//                viewMenu = new JArray();
//                foreach (var item in datas)
//                {
//                    viewMenu.Add(item["fmenuid"].ToString ());
//                }

//                cache.Set(ctx, key, viewMenu);
//            }

//            return true;
//        }

//        /// <summary>
//        /// 根据传入的参数判断菜单是否可显示
//        /// </summary>
//        /// <param name="userCtx"></param>
//        /// <param name="menu"></param>
//        /// <param name="parameters"></param>
//        /// <returns>false 可见（不过滤掉该菜单），true 不可见（过滤掉该菜单）</returns>
//        public bool FilterMenu(UserContext userCtx, JObject menu,Dictionary<string,object> parameters)
//        { 
//            return false;
//        }

//        public void ClearMenuCache(UserContext ctx)
//        {
//            throw new NotImplementedException();
//        }

//        public bool FilterMenu(JObject menu, Dictionary<string, object> parameters)
//        {
//            throw new NotImplementedException();
//        }

//        public void FilterOuterParamsMenu(UserContext ctx, IEnumerable<JObject> menu, string formId)
//        {
            
//        }
//    }
//}
