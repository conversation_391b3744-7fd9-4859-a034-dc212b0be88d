using System;
using System.Linq;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Security.Cryptography;

using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.Validator;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.Framework.AppService.SystemPlugIn.Sys
{
    /// <summary>
    /// 审计日志 --查看归档日志
    /// </summary>
    [InjectService]
    [FormId("bas_auditinglog_list")]
    [OperationNo("lookarchive")]
    public class AuditingLogLookArchive : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            //审计日志归档记录
            var metaLog = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "bas_auditinglog");
            var dmLog = this.Container.GetService<IDataManager>();
            dmLog.InitDbContext(this.Context, metaLog.GetDynamicObjectType(this.Context));
            this.Context.UpdateMdlSchema(metaLog.Id);

            var logs = dmLog.SelectBy("1=1").OfType<DynamicObject>().OrderBy(f => f["fworkobject"]).OrderBy(f => f["forderid"]).OrderBy(f => f["foptime"]);
            var pageId = this.CurrentPageId.IsNullOrEmptyOrWhiteSpace() ? Guid.NewGuid().ToString() : this.CurrentPageId;
            var action = this.Context.ShowListForm(metaLog.Id, "", pageId, Enu_OpenStyle.Modal);
            this.Result.HtmlActions.Add(action);
        }
    }

}