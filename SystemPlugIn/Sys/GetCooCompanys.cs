using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.Sys
{
    /// <summary>
    /// 获取所有协同企业列表
    /// </summary>
    [InjectService("getallcoocompany")]
    public class GetCooCompanys : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return "";
            }
        }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return "";
            }
        }

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var userCtx = this.OperationContext.UserContext;
            if (userCtx == null || userCtx.Company.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("当前用户操作上下文丢失，请刷新页面重试！");
            }

            var meta = this.MetaModelService.LoadFormModel(userCtx, "coo_company");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, meta.GetDynamicObjectType(userCtx));

            var allSql = @"select fcompanyId,fname from T_COO_COMPANY where fmycompanyId='{0}' and fmainorgid='{0}' and fcoostatus=N'已协同'".Fmt(userCtx.Company);
            List<Dictionary<string, object>> cooCompanys = new List<Dictionary<string, object>>();
            System.Collections.ArrayList arrComp = new System.Collections.ArrayList();
            using (var reader = this.DBService.ExecuteReader(userCtx, allSql))
            {
                while (reader.Read())
                {
                    var companyId = reader.GetValue<string>("fcompanyId");
                    if (!arrComp.Contains(companyId))
                    {
                        cooCompanys.Add(new Dictionary<string, object>() { { "id", companyId }, { "name", reader.GetValue<string>("fname") } });
                        arrComp.Add(companyId);
                    }
                }
            }
            this.OperationContext.Result.SrvData = cooCompanys;
        }
    }
}
