using JieNor.Framework.DataEntity.MainFw;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.Framework.AppService.SystemPlugIn.Auth
{
    /// <summary>
    /// 从认证站点获取业务对象启用权限：用于业务对象权限启用设置界面
    /// </summary>
    [InjectService]
    [FormId("sec_role")]
    [OperationNo("GetBizObjPermission")]
    public class GetBizObjPermission : AbstractOperationServicePlugIn
    {

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {

            //业务对象启用的权限项
            var authBizObjPermission = GetAuthBizObjPermission(this.Context.Product);
            //权限项
            var authPermissionItem = GetAuthPermissionItem(this.Context.Product);
            List<MdlPermitInfo> mdlPermitList = BuildRolePermission(authBizObjPermission, authPermissionItem);

            this.Result.SrvData = mdlPermitList.ToJson();
            this.Result.IsSuccess = true;
            this.Result.IsShowMessage = false;
        }


        private List<MdlPermitInfo> BuildRolePermission(Dictionary<string, string> authBizObjPermit, Dictionary<string, string> authPermitItems)
        { 
            //业务模块
            var group = GetModuleGroup();
            List<MdlPermitInfo> mdlPermList = new List<MdlPermitInfo>();

            foreach (var item in group)
            {
                var module = new MdlPermitInfo();
                module.ModuleId = item.Key;
                module.ModuleName = item.Key;

                BuildModuleBizObj(module, item.Value, authBizObjPermit, authPermitItems);

                mdlPermList.Add(module);
            }

            return mdlPermList;
        }


        private void BuildModuleBizObj(MdlPermitInfo module,
                                        Dictionary<string, string> bizObjs,
                                        Dictionary<string, string> authBizObjPermit,
                                        Dictionary<string, string> authPermitItems)
        {
            //权限对象
            foreach (var bizObj in bizObjs)
            {
                HtmlForm meta = null;
                try
                {
                    meta = HtmlParser.LoadFormMetaFromCache(bizObj.Key, this.Context);
                }
                catch (Exception ex)
                {
                    // TODO 记录日志
                }
                if (meta == null)
                {
                    continue;
                }

                var pItems = authBizObjPermit.Where(f => f.Key.StartsWithIgnoreCase(bizObj.Key + ":")).ToList();
                if (pItems == null || pItems.Count == 0)
                {
                    continue;
                }

                BizObjPermitInfo bizObjAuth = new BizObjPermitInfo();
                bizObjAuth.BizObjId = bizObj.Key;
                bizObjAuth.BizObjName = bizObj.Value;
                foreach (var item in authPermitItems)
                {
                    var facl = new PermitItemAuthInfo()
                    {
                        ItemId = item.Key,
                        ItemName = item.Value,
                    };
                     
                    if (authBizObjPermit.ContainsKey(bizObj.Key + ":" + item.Key))
                    {
                        facl.IsAllow = true;
                    }

                    bizObjAuth.FucntionACL.Add(facl);
                }
                  
                module.BizObjPermission.Add(bizObjAuth);
            }
        }


        /// <summary>
        /// 将业务对象信息格式化
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, Dictionary<string, string>> GetModuleGroup()
        {
            Dictionary<string, string> bizObj = GetBizObjectInfo();
            Dictionary<string, Dictionary<string, string>> ret = new Dictionary<string, Dictionary<string, string>>(StringComparer.OrdinalIgnoreCase);
            foreach (var item in bizObj)
            {
                var mdl = item.Key.Split('|');
                if (!ret.ContainsKey(mdl[0]))
                {
                    ret.Add(mdl[0], new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase));
                }

                ret[mdl[0]].Add(mdl[1], item.Value);
            }

            return ret;
        }


        /// <summary>
        /// 获取业务对象信息
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, string> GetBizObjectInfo()
        {
            //TODO 按模块分组
            Dictionary<string, string> bizObj = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            string strSql = $@" select distinct fmodule,fnumber,fname
                                from t_sys_bizobject t0 order by fmodule";
            var svc = this.Container.GetService<IDBService>();
            using (var reader = svc.ExecuteReader(this.Context, strSql))
            {
                while (reader.Read())
                {
                    var module = reader.GetValue<string>("fmodule");
                    if (module.IsNullOrEmptyOrWhiteSpace())
                    {
                        module = "未知模块";
                    }
                    var no = reader.GetValue<string>("fnumber");
                    var name = reader.GetValue<string>("fname");

                    bizObj.Add(module + "|" + no, name);
                }
            }

            return bizObj;
        }


        /// <summary>
        /// 获取验证站点上业务对象启用的权限项
        /// </summary> 
        /// <param name="productId"></param>
        /// <returns></returns>
        private Dictionary<string, string> GetAuthBizObjPermission(string productId)
        {
            var param = new CommonFormDTO()
            {
                FormId = "auth_role",
                OperationNo = "GetBizObjPermission",
            };
            param.SimpleData.Add("productid", productId);
            var gateway = this.Container.GetService<IHttpServiceInvoker>();
            var result = gateway.Invoke(this.Context, TargetSEP.AuthService, param) as DynamicDTOResponse;
            var authBizObjPermission = result?.OperationResult?.SrvData?.ToString().FromJson<Dictionary<string, string>>(); ;
            return authBizObjPermission;
        }


        /// <summary>
        /// 获取验证站点上产品启用的权限项
        /// </summary> 
        /// <param name="productId"></param>
        /// <returns></returns>
        private Dictionary<string, string> GetAuthPermissionItem(string productId)
        {
            var param = new CommonFormDTO()
            {
                FormId = "auth_role",
                OperationNo = "GetPermissionItem",
            };
            param.SimpleData.Add("productid", productId);
            var gateway = this.Container.GetService<IHttpServiceInvoker>();
            var result = gateway.Invoke(this.Context, TargetSEP.AuthService, param) as DynamicDTOResponse;
            var authPermissionItem = result?.OperationResult?.SrvData?.ToString().FromJson<Dictionary<string, string>>(); ;
            return authPermissionItem;
        }
    }
}
