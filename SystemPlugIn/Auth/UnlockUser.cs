using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.Framework.AppService.SystemPlugIn.Auth
{
    /// <summary>
    /// 解锁用户账号
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    [OperationNo("unlock")]
    public class UnlockUser : AbstractOperationServicePlugIn
    {
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            base.OnCheckPermssion(e);
            e.PermItem = "fw_unlock";

        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null) return;

            var userService = this.Container.GetService<IUserRegisterService>();
            var lstUsers = e.DataEntitys.Select(o => o["fnumber"] as string)
                .Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                .ToArray();

            var result = userService.UnLockUser(this.Context, lstUsers.Distinct(), this.Option);

            this.Result.MergeResult(result);
        }
    }
}
