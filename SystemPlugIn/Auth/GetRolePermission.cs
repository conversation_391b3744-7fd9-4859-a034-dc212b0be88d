using JieNor.Framework.DataEntity.MainFw;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.Framework.AppService.SystemPlugIn.Auth
{
    /// <summary>
    /// 从认证站点获取角色权限：用于角色授权界面
    /// </summary>
    [InjectService]
    [FormId("sec_role")]
    [OperationNo("GetRolePermission")]
    public class GetRolePermission : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 操作结束处理逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var roleId = this.GetQueryOrSimpleParam<string>("roleid", "");
            if (roleId.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.IsSuccess = false;
                return;
            }
            var billForm = this.GetQueryOrSimpleParam<string>("billForm", "");

            PermHelper.AutoUpdateSchema(Context);

            //是否不从缓存抓取数据
            var isForceNoCache = this.GetQueryOrSimpleParam<string>("forceNoCache", "false").EqualsIgnoreCase("true");
            if (isForceNoCache) PermHelper.ClearCache(this.Context);

            var permService = this.Container.GetService<IPermissionService>();
            var auth = permService.GetRolePermission(this.Context, roleId, true, billForm);

            //加载用户的其他信息
            this.LoadUserInfo(auth);

            this.IgnoreSysMenuEditor(auth);

            this.FilterCurrRoleACL(auth);

            this.IgnoreBIbyOrg(this.Context, auth);

            this.Result.SrvData = auth.ToJson();
            this.Result.IsSuccess = true;
            this.Result.IsShowMessage = false;
        }

        /// <summary>
        /// 过滤当前用户拥有的权限
        /// </summary>
        /// <param name="auth"></param>
        private void FilterCurrRoleACL(RolePermitInfo auth)
        {
            if (this.Context.IsTopOrg)
            {
                return;
            }

            // 获取当前用户的权限信息
            var currUserAuth = PermHelper.GetCurrUserPermDataInfo(this.Context);

            List<MdlPermitInfo> removeMdls = new List<MdlPermitInfo>();
            foreach (var mdl in auth.MdlPermission)
            {
                List<BizObjPermitInfo> removeBizObjs = new List<BizObjPermitInfo>();
                foreach (var bizObj in mdl.BizObjPermission)
                {
                    List<PermitItemAuthInfo> removeFuncs = new List<PermitItemAuthInfo>();
                    foreach (var func in bizObj.FucntionACL)
                    {
                        var key = PermHelper.GetBizPermitKey(bizObj.BizObjId, func.ItemId);

                        // 没有权限：包括拒绝
                        if (!currUserAuth.TryGetValue(key, out var value) || value != 1)
                        {
                            removeFuncs.Add(func);
                        }
                    }

                    foreach (var func in removeFuncs)
                    {
                        bizObj.FucntionACL.Remove(func);
                    }

                    if (!bizObj.FucntionACL.Any())
                    {
                        removeBizObjs.Add(bizObj);
                    }
                }

                foreach (var bizObj in removeBizObjs)
                {
                    mdl.BizObjPermission.Remove(bizObj);
                }

                if (!mdl.BizObjPermission.Any())
                {
                    removeMdls.Add(mdl);
                }
            }

            foreach (var mdl in removeMdls)
            {
                auth.MdlPermission.Remove(mdl);
            }

        }

        /// <summary>
        /// 排除主控菜单
        /// </summary>
        /// <param name="auth"></param>
        private void IgnoreSysMenuEditor(RolePermitInfo auth)
        {
            foreach (var module in auth.MdlPermission)
            {
                foreach (var bizObjAuth in module.BizObjPermission)
                {
                    if (bizObjAuth.BizObjId.EqualsIgnoreCase("sys_menueditor"))
                    {
                        module.BizObjPermission.Remove(bizObjAuth);
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 二级分销排除BI报表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="auth"></param>
        private void IgnoreBIbyOrg(UserContext userCtx, RolePermitInfo auth)
        {
            var removeData = new MdlPermitInfo();
            foreach (var module in auth.MdlPermission)
            {
                if (userCtx.IsSecondOrg && module.ModuleName.EqualsIgnoreCase("BI报表"))
                {
                    removeData = module;
                }
            }
            auth.MdlPermission.Remove(removeData);
        }

        /// <summary>
        /// 加载用户的其他信息
        /// </summary>
        /// <param name="roleInfo"></param>
        private void LoadUserInfo(RolePermitInfo roleInfo)
        {
            if (roleInfo == null) return;
            var userIds = roleInfo?.RoleUsers?.Keys?.Where(o => !o.IsNullOrEmptyOrWhiteSpace())?.Distinct()?.ToList();
            if (userIds == null || userIds.Count() <= 0) return;

            using (var tran = Context.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var paramList = new List<SqlParam>();
                var sqlText = "";
                var tempTblUser = "";
                if (userIds.Count < 20)
                {
                    var mainOrgWhere = " a.fid in ({0})".Fmt (userIds.JoinEx(",", true)); 
                    sqlText = $@"
                            select a.fid userId,a.fnumber userNumber,a.fname as userName,x.fnumber as orgNumber,x.fname as orgName
                            from t_sec_user a with(nolock)
                            inner join T_BAS_ORGANIZATION x with(nolock) on a.fmainorgid=x.fid
                            where {mainOrgWhere} and a.fforbidstatus='0'";
                }
                else
                {
                    tempTblUser = DBService.CreateTempTableWithDataList(Context, userIds,false);
                    sqlText = $@"
                            select a.fid userId,a.fnumber userNumber,a.fname as userName,x.fnumber as orgNumber,x.fname as orgName
                            from t_sec_user a with(nolock)
                            inner join T_BAS_ORGANIZATION x with(nolock) on a.fmainorgid=x.fid
                            inner join {tempTblUser} y on a.fid=y.fid 
                            where a.fforbidstatus='0'";
                }

                List<Dictionary<string, object>> dicUsers = new List<Dictionary<string, object>>();
                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, paramList))
                {
                    while (reader.Read())
                    {
                        Dictionary<string, object> dicUser = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            var colValue = reader[i];
                            var colName = reader.GetName(i);
                            dicUser[colName] = colValue.IsNullOrEmptyOrWhiteSpace() ? "" : colValue;
                        }
                        dicUsers.Add(dicUser);
                    }
                }

                if (tempTblUser.IsNullOrEmptyOrWhiteSpace() == false)
                {
                    DBService.DeleteTempTableByName(Context, tempTblUser, true);
                }

                roleInfo.UserInfo = dicUsers;

                tran.Complete();
            }
        }
    }
}