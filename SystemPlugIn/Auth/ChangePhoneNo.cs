using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Session;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.UserEdit
{
    /// <summary>
    /// 更换手机号操作
    /// </summary>
    [InjectService]
    [FormId("bd_personal")]
    [OperationNo("changephone")]
    public class ChangePhoneNo : AbstractOperationServicePlugIn
    {
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            base.OnCheckPermssion(e);
            e.FormId = "sec_user";
            e.PermItem = "sec_user_changephone";
        }

        /// <summary>
        /// 处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //修改用户手机号时的校验器
            var phoneUniqueRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_UserPhoneChangeValidation);
            if (phoneUniqueRule != null)
            {
                //将当前要修改的新手机号传给校验器
                var phoneNo = this.GetQueryOrSimpleParam<string>("phoneNo");
                var validExpr = new Dictionary<string, string>
                {
                    ["phoneNo"] = phoneNo,
                    ["userIdPropName"] = "fuserid"
                };
                phoneUniqueRule.Initialize(this.Context, validExpr.ToJson());
                e.Rules.Add(phoneUniqueRule);
            }
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length < 1)
            {
                return;
            }
            if (e.DataEntitys.Length > 1)
            {
                throw new BusinessException("操作失败：不支持批量解绑手机号！");
            }
            var phoneNo = this.GetQueryOrSimpleParam<string>("phoneNo");
            if (phoneNo.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("操作失败：重新绑定的新手机号不可以为空！");
            }
            var userObj = e.DataEntitys.FirstOrDefault();
            var userId = userObj?["fuserid"] as string;
            if (userId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("操作失败：用户不存在！");
            }

            var userMeta = this.MetaModelService.LoadFormModel(this.Context, "sec_user");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, userMeta.GetDynamicObjectType(this.Context));
            var localUserObj = dm.Select(userId) as DynamicObject;
            if (localUserObj == null)
            {
                throw new BusinessException("操作失败：用户不存在！");
            }

            var querySql = $@"/*dialect*/select fnumber,fname,fispreset from t_sec_role where fid='{Convert.ToString(localUserObj["froleids"])}'";
            var queryRole = this.DBService.ExecuteDynamicObject(this.Context, querySql)?.FirstOrDefault();
            if (queryRole != null && Convert.ToString(queryRole["fnumber"]) == "Admin_Agent")
            {
                var agentSql = $@"/*dialect*/select fid,fnumber,actualownernumber,fcontacterphone from t_bas_agent where actualownernumber in (
                                        select actualownernumber from t_bas_agent where fid = '{this.Context.Company}')";
                var agents = this.DBService.ExecuteDynamicObject(this.Context, agentSql);
                if (agents != null && agents.Count == 1)
                {
                    var agent = agents.FirstOrDefault();
                    if (phoneNo != Convert.ToString(agent["fcontacterphone"]))
                    {
                        throw new BusinessException("操作失败！实控人的手机号解绑后将与招商系统档案不一致，请在招商系统提交主数据变更流程修改手机号，再重新操作。");
                    }
                }
                else
                {
                    var isExsit = agents.Where(x => Convert.ToString(x["fcontacterphone"]) == phoneNo).Any();
                    if (!isExsit)
                    {
                        throw new BusinessException("操作失败！实控人的手机号解绑后将与招商系统档案不一致，请在招商系统提交主数据变更流程修改手机号，再重新操作。");
                    }
                    foreach (var item in agents)
                    {
                        if (Convert.ToString(item["fcontacterphone"]) != phoneNo)
                        {
                            this.Result.SrvData = $@"操作成功！该手机号对应组织[{Convert.ToString(item["fnumber"])}]的账号未更新，请在招商系统提交主数据变更流程修改手机号。";
                            break;
                        }
                    }
                }
            }

            var localUserNumber = localUserObj["fnumber"] as string;

            List<DynamicObject> otherUsers = dm.SelectBy("fnumber='{0}' and fforbidstatus='0' and fid<>'{1}'".Fmt(localUserNumber, localUserObj["Id"]?.ToString())
                                            ).OfType<DynamicObject>().ToList();
            if (otherUsers != null || otherUsers.Count > 0)
            {
                //其他相同账号同步修改
                foreach (var item in otherUsers)
                {
                    item["fnumber"] = phoneNo;
                    item["fphone"] = phoneNo;
                    item["fmusisyncstatus"] = "01";
                    item["fmodifydate"] = DateTime.Now;
                }
                dm.Save(otherUsers);
            }

            //保存本地用户信息
            var remoteUserId = localUserObj["frefuserid"] as string;
            if (remoteUserId.IsNullOrEmptyOrWhiteSpace())
            {
                remoteUserId = this.Context.UserSession?.RemoteUserId;
                localUserObj["frefuserid"] = remoteUserId;
            }

            //修改手机号，下次登录强制修改密码
            if (!localUserObj["fphone"].Equals(phoneNo))
            {
                localUserObj["fforcepasswordchange"] = true;
            }

            localUserObj["fnumber"] = phoneNo;
            localUserObj["fphone"] = phoneNo;
            localUserObj["fmusisyncstatus"] = "01";
            localUserObj["fmodifydate"] = DateTime.Now;
            dm.Save(localUserObj);

            ////员工手机号同步修改
            //var userIds = otherUsers.Select(x => Convert.ToString(x["Id"])).ToList();
            //userIds.Add(localUserObj["Id"]?.ToString());
            //var staffs = this.Context.LoadBizDataByFilter("ydj_staff", $@"flinkuserid in ({userIds.JoinEx(", ", true)})");
            //if (staffs != null || staffs.Count > 0)
            //{
            //    foreach (var item in staffs)
            //    {
            //        item["fphone"] = phoneNo;
            //        item["fmusisyncstatus"] = "01";
            //        item["fmodifydate"] = DateTime.Now;
            //    }
            //    var staffMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_staff");
            //    dm.InitDbContext(this.Context, staffMeta.GetDynamicObjectType(this.Context));
            //    dm.Save(staffs);
            //}

            var changePhoneDto = new CommonFormDTO()
                .SetFormId("auth_user")
                .SetOperationNo("changephone")
                .SetSimpleData("phoneNo", phoneNo)
                .SetSimpleData("userNumber", localUserNumber)
                .SetSelectedRows(new SelectedRow[]
                {
                    new SelectedRow()
                    {
                        PkValue = remoteUserId
                    }
                });
            var result = this.Gateway.Invoke(this.Context,
                TargetSEP.AuthService,
                changePhoneDto);
            var respResult = (result as DynamicDTOResponse)?.OperationResult;
            respResult.ThrowIfHasError(true, "操作失败：出现未知错误，请查看系统日志！");
            this.Result.MergeResult(respResult);
            if (this.Result.IsSuccess)
            {
                this.AddRefreshPageAction();
            }

            //清除用户会话
            var sessionCache = this.Context.Container.GetService<ISessionCache>();
            var tokenId = $"{localUserObj["fmainorgid"]}.{localUserObj["id"]}".HashString();            
            sessionCache?.RemoveTicket(tokenId);
            if (otherUsers != null || otherUsers.Count > 0)
            { 
                foreach (var item in otherUsers)
                {
                    tokenId = $"{item["fmainorgid"]}.{item["id"]}".HashString();
                    sessionCache?.RemoveTicket(tokenId);
                } 
            }
            //RemoveInvalidUserSessionCache(sessionCache, this.Context.Company, this.Context.UserSession.RemoteUserId);
        }

        ///// <summary>
        ///// 将不在线的用户会话从（OnlineUser、LoginOrg）中移除，避免缓存无用的会话标识
        ///// </summary>
        //public void RemoveInvalidUserSessionCache(ISessionCache sessionCache, string company, string remoteUserId)
        //{
        //    var task = new Task(() =>
        //    {
        //        int index = 0;
        //        var ok = false;
        //        while (index < 10 && !ok)
        //        {
        //            ok = DoRemoveInvalidSession(sessionCache, company, remoteUserId);
        //            index++;
        //        } 
        //    });

        //    task.Start();
        //}

        //private static bool DoRemoveInvalidSession(ISessionCache sessionCache, string company, string remoteUserId)
        //{
        //    try
        //    {
        //        var onlineKey = "OnlineUser:{0}".Fmt(company);
        //        var loginOrgKey = "LoginOrg:{0}".Fmt(remoteUserId);

        //        var onlines = sessionCache.GetAllItemsFromSet<string>(onlineKey);
        //        if (onlines != null && onlines.Count > 0)
        //        {
        //            foreach (var item in onlines)
        //            {
        //                // 这里只是为了检测指定的缓存键是否存在，无需将缓存内容序列化为对象（太耗CPU资源了）
        //                var ticket = sessionCache.RemoveTicket(item);
        //                if (ticket.IsNullOrEmptyOrWhiteSpace())
        //                {
        //                    sessionCache.RemoveItemFromSet<string>(onlineKey, item);
        //                }
        //            }
        //        }

        //        var loginOrgs = sessionCache.GetAllItemsFromSet<string>(loginOrgKey);
        //        if (loginOrgs != null && loginOrgs.Count > 0)
        //        {
        //            foreach (var item in loginOrgs)
        //            {
        //                var ticket = sessionCache.GetTicket(item);
        //                if (ticket.IsNullOrEmptyOrWhiteSpace())
        //                {
        //                    sessionCache.RemoveItemFromSet<string>(loginOrgKey, item);
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        return false;
        //    }

        //    return true;
        //}
    }

    /// <summary>
    /// 用户列表上手机解绑（通常是管理员操作）
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    [OperationNo("changephone")]
    public class ChangePhoneNo2 : AbstractOperationServicePlugIn
    {
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            base.OnCheckPermssion(e);
            e.PermItem = "sec_user_changephone";
        }

        /// <summary>
        /// 处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //修改用户手机号时的校验器
            var phoneUniqueRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_UserPhoneChangeValidation);
            if (phoneUniqueRule != null)
            {
                //将当前要修改的新手机号传给校验器
                var phoneNo = this.GetQueryOrSimpleParam<string>("phoneNo");
                var validExpr = new Dictionary<string, string>
                {
                    ["phoneNo"] = phoneNo,
                    ["userIdPropName"] = "id"
                };
                phoneUniqueRule.Initialize(this.Context, validExpr.ToJson());
                e.Rules.Add(phoneUniqueRule);
            }
        }

        /// <summary>
        /// 操作前处理逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length < 1 || e.DataEntitys[0] == null)
            {
                return;
            }
            if (e.DataEntitys.Length > 1)
            {
                throw new BusinessException("操作失败：不支持批量解绑手机号！");
            }
            var phoneNo = this.GetQueryOrSimpleParam<string>("phoneNo");
            if (phoneNo.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("操作失败：重新绑定的新手机号不可以为空！");
            }

            var localUserObj = e.DataEntitys.FirstOrDefault();
            var localUserNumber = localUserObj?["fnumber"] as string;
            if (localUserNumber.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("操作失败：用户不存在！");
            }
            if (!this.Context.UserSession.UserId.EqualsIgnoreCase(localUserObj["id"] as string))
            {
                throw new BusinessException("操作失败：不支持修改他人手机号账号！");
            }

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            List<DynamicObject> otherUsers = dm.SelectBy("fnumber='{0}' and fforbidstatus='0' and fid<>'{1}'".Fmt(localUserNumber, localUserObj["Id"]?.ToString())
                                            ).OfType<DynamicObject>().ToList();
            if (otherUsers != null || otherUsers.Count > 0)
            {
                //其他相同账号同步修改
                foreach (var item in otherUsers)
                {
                    item["fnumber"] = phoneNo;
                    item["fphone"] = phoneNo;
                    item["fmusisyncstatus"] = "01";
                    item["fmodifydate"] = DateTime.Now;
                }
                dm.Save(otherUsers);
            }

            //本地用户对应的远程用户id信息
            var remoteUserId = localUserObj["frefuserid"] as string;
            if (remoteUserId.IsNullOrEmptyOrWhiteSpace())
            {
                //如果修改的当前登录用户的手机号，则获取当前登录用户的远程用户Id
                if (this.Context.UserSession.UserId.EqualsIgnoreCase(localUserObj["id"] as string))
                {
                    remoteUserId = this.Context.UserSession?.RemoteUserId;
                }
            }
            if (!remoteUserId.IsNullOrEmptyOrWhiteSpace())
            {
                remoteUserId = ExistsAuthUser(this.Context, "", remoteUserId);
            }
            else
            {
                //如果本地用户没有远程用户Id字段值，则根据本地用户名查询远程用户
                remoteUserId = ExistsAuthUser(this.Context, localUserNumber);
            }
            if (remoteUserId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("操作失败：用户不存在！");
            }

            //保存本地用户信息
            localUserObj["frefuserid"] = remoteUserId;
            localUserObj["fnumber"] = phoneNo;
            localUserObj["fphone"] = phoneNo;
            localUserObj["fmusisyncstatus"] = "01";
            localUserObj["fmodifydate"] = DateTime.Now;
            dm.Save(localUserObj);

            //更新远程用户信息
            var changePhoneDto = new CommonFormDTO()
                .SetFormId("auth_user")
                .SetOperationNo("changephone")
                .SetSimpleData("phoneNo", phoneNo)
                .SetSimpleData("userNumber", localUserNumber)
                .SetSelectedRows(new SelectedRow[]
                {
                    new SelectedRow()
                    {
                        PkValue = remoteUserId
                    }
                });
            var result = this.Gateway.Invoke(this.Context,
                TargetSEP.AuthService,
                changePhoneDto);
            var respResult = (result as DynamicDTOResponse)?.OperationResult;
            respResult.ThrowIfHasError(true, "操作失败：出现未知错误，请查看系统日志！");
            this.Result.MergeResult(respResult);
            if (this.Result.IsSuccess)
            {
                this.AddRefreshPageAction();
            }

            //清除用户会话
            var sessionCache = this.Context.Container.GetService<ISessionCache>(); 
            var tokenId = $"{localUserObj["fmainorgid"]}.{localUserObj["id"]}".HashString();
            sessionCache?.RemoveTicket(tokenId);
            if (otherUsers != null || otherUsers.Count > 0)
            { 
                foreach (var item in otherUsers)
                {
                    tokenId = $"{item["fmainorgid"]}.{item["id"]}".HashString();
                    sessionCache?.RemoveTicket(tokenId);
                } 
            }

            //RemoveInvalidUserSessionCache(sessionCache, this.Context.Company, remoteUserId);
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        private string ExistsAuthUser(UserContext ctx, string userName, string userId = "")
        {
            var para = new CommonBillDTO()
            {
                FormId = "auth_user",
                OperationNo = "fetch"
            };
            para.SimpleData.Add("userName", userName);
            para.SimpleData.Add("userId", userId);
            var gateway = ctx.Container.GetService<IHttpServiceInvoker>();

            var targetSEP = TargetSEP.AuthService;
            var appId = ctx.AppId;
            if (!targetSEP.Headers.ContainsKey("X-AppId")) targetSEP.Headers.Add("X-AppId", appId);

            var result = gateway.Invoke(null, targetSEP, para) as DynamicDTOResponse;
            var exists = result?.OperationResult?.SrvData?.ToString().FromJson<CustomUserAuth>();

            return exists?.RefIdStr;
        }

        ///// <summary>
        ///// 将不在线的用户会话从（OnlineUser、LoginOrg）中移除，避免缓存无用的会话标识
        ///// </summary>
        //public void RemoveInvalidUserSessionCache(ISessionCache sessionCache, string company, string remoteUserId)
        //{
        //    var task = new Task(() =>
        //    {
        //        int index = 0;
        //        var ok = false;
        //        while (index < 10 && !ok)
        //        {
        //            ok = DoRemoveInvalidSession(sessionCache, company, remoteUserId);
        //            index++;
        //        }  
        //    });

        //    task.Start();
        //}

        //private bool  DoRemoveInvalidSession(ISessionCache sessionCache, string company, string remoteUserId)
        //{
        //    try
        //    {
        //        var onlineKey = "OnlineUser:{0}".Fmt(company);
        //        var loginOrgKey = "LoginOrg:{0}".Fmt(remoteUserId);

        //        var onlines = sessionCache.GetAllItemsFromSet<string>(onlineKey);
        //        if (onlines != null && onlines.Count > 0)
        //        {
        //            foreach (var item in onlines)
        //            {
        //                // 这里只是为了检测指定的缓存键是否存在，无需将缓存内容序列化为对象（太耗CPU资源了）
        //                var ticket = sessionCache.GetTicket(item);
        //                if (ticket.IsNullOrEmptyOrWhiteSpace())
        //                {
        //                    sessionCache.RemoveItemFromSet<string>(onlineKey, item);
        //                }
        //            }
        //        }

        //        var loginOrgs = sessionCache.GetAllItemsFromSet<string>(loginOrgKey);
        //        if (loginOrgs != null && loginOrgs.Count > 0)
        //        {
        //            foreach (var item in loginOrgs)
        //            {
        //                var ticket = sessionCache.GetTicket(item);
        //                if (ticket.IsNullOrEmptyOrWhiteSpace())
        //                {
        //                    sessionCache.RemoveItemFromSet<string>(loginOrgKey, item);
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        return false;
        //    }

        //    return true;
        //}


    }
}
