using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;

namespace TailDifferenceTest
{
    /// <summary>
    /// 尾差计算测试用例
    /// 模拟提交总部时的尾差处理逻辑
    /// </summary>
    public class TailDifferenceTestCase
    {
        /// <summary>
        /// 测试用例：零售价33888，产生尾差的场景
        /// </summary>
        public static void TestTailDifferenceScenario()
        {
            Console.WriteLine("=== 尾差计算测试用例 ===");
            Console.WriteLine();

            // 测试场景1：单行商品，产生尾差
            TestSingleProductTailDifference();
            
            Console.WriteLine();
            Console.WriteLine("=================================");
            Console.WriteLine();
            
            // 测试场景2：多行商品，尾差分配到最后一行
            TestMultipleProductsTailDifference();
        }

        /// <summary>
        /// 测试场景1：单行商品产生尾差
        /// 零售价：33888，成交单价：33885.678（会产生四舍五入差异）
        /// </summary>
        private static void TestSingleProductTailDifference()
        {
            Console.WriteLine("【测试场景1：单行商品尾差计算】");
            
            // 模拟订单数据
            var order = new OrderData
            {
                Id = "ORDER001",
                BillNo = "SO202412270001",
                BillTypeName = "普通销售合同",
                IsResellerOrder = false, // 非二级分销
                TotalDealAmount = 33885.68m, // 订单总成交金额
                Entries = new List<OrderEntry>
                {
                    new OrderEntry
                    {
                        Id = "ENTRY001",
                        ProductName = "测试商品A",
                        RetailPrice = 33888.00m,        // 零售价
                        DealPrice = 33885.678m,         // 原始成交单价（3位小数）
                        Quantity = 1,                   // 数量
                        DealAmount = 33885.68m,         // 原始成交金额（已四舍五入到2位）
                        IsSuite = false,                // 非套装
                        IsGiveaway = false              // 非赠品
                    }
                }
            };

            Console.WriteLine($"原始数据：");
            Console.WriteLine($"  零售价: {order.Entries[0].RetailPrice:F2}");
            Console.WriteLine($"  成交单价: {order.Entries[0].DealPrice:F3}");
            Console.WriteLine($"  数量: {order.Entries[0].Quantity}");
            Console.WriteLine($"  成交金额: {order.Entries[0].DealAmount:F2}");
            Console.WriteLine($"  订单总金额: {order.TotalDealAmount:F2}");
            Console.WriteLine();

            // 执行尾差计算逻辑
            CalculateTailDifference(order);
        }

        /// <summary>
        /// 测试场景2：多行商品，尾差分配到ID最大的行
        /// </summary>
        private static void TestMultipleProductsTailDifference()
        {
            Console.WriteLine("【测试场景2：多行商品尾差分配】");
            
            var order = new OrderData
            {
                Id = "ORDER002",
                BillNo = "SO202412270002",
                BillTypeName = "普通销售合同",
                IsResellerOrder = false,
                TotalDealAmount = 50000.00m, // 订单总成交金额
                Entries = new List<OrderEntry>
                {
                    new OrderEntry
                    {
                        Id = "ENTRY001", // ID较小
                        ProductName = "测试商品A",
                        RetailPrice = 33888.00m,
                        DealPrice = 33885.678m,     // 会产生尾差
                        Quantity = 1,
                        DealAmount = 33885.68m,
                        IsSuite = false,
                        IsGiveaway = false
                    },
                    new OrderEntry
                    {
                        Id = "ENTRY002", // ID较大，尾差会分配到这里
                        ProductName = "测试商品B",
                        RetailPrice = 16115.00m,
                        DealPrice = 16114.334m,     // 也会产生尾差
                        Quantity = 1,
                        DealAmount = 16114.33m,
                        IsSuite = false,
                        IsGiveaway = false
                    }
                }
            };

            Console.WriteLine($"原始数据：");
            foreach (var entry in order.Entries)
            {
                Console.WriteLine($"  商品: {entry.ProductName} (ID: {entry.Id})");
                Console.WriteLine($"    零售价: {entry.RetailPrice:F2}");
                Console.WriteLine($"    成交单价: {entry.DealPrice:F3}");
                Console.WriteLine($"    成交金额: {entry.DealAmount:F2}");
            }
            Console.WriteLine($"  订单总金额: {order.TotalDealAmount:F2}");
            Console.WriteLine();

            // 执行尾差计算逻辑
            CalculateTailDifference(order);
        }

        /// <summary>
        /// 模拟提交总部的尾差计算逻辑
        /// </summary>
        private static void CalculateTailDifference(OrderData order)
        {
            Console.WriteLine("开始计算尾差...");
            Console.WriteLine();

            // 步骤1：计算保留2位小数的成交单价
            foreach (var entry in order.Entries)
            {
                decimal cvalue = entry.RetailPrice - entry.DealPrice;
                entry.RoundedDealPrice = Math.Round(entry.RetailPrice, 2, MidpointRounding.AwayFromZero) 
                                       - Math.Round(cvalue, 2, MidpointRounding.AwayFromZero);
                
                Console.WriteLine($"商品 {entry.ProductName}:");
                Console.WriteLine($"  cvalue = {entry.RetailPrice:F2} - {entry.DealPrice:F3} = {cvalue:F3}");
                Console.WriteLine($"  保留2位小数成交单价 = {Math.Round(entry.RetailPrice, 2):F2} - {Math.Round(cvalue, 2):F2} = {entry.RoundedDealPrice:F2}");
            }
            Console.WriteLine();

            // 步骤2：检查是否需要处理尾差
            bool needProcess = !order.Entries.All(d => d.RoundedDealPrice == d.DealPrice);
            Console.WriteLine($"是否需要处理尾差: {needProcess}");
            Console.WriteLine();

            if (needProcess)
            {
                // 步骤3：计算每行的尾差值
                foreach (var entry in order.Entries)
                {
                    var roundedOriginalAmount = Math.Round(entry.DealAmount, 2, MidpointRounding.AwayFromZero);
                    var roundedFDealAmount = entry.Quantity * entry.RoundedDealPrice;
                    entry.RowTailDifference = roundedOriginalAmount - roundedFDealAmount;
                    
                    Console.WriteLine($"商品 {entry.ProductName} 行尾差计算:");
                    Console.WriteLine($"  四舍五入成交金额: {roundedOriginalAmount:F2}");
                    Console.WriteLine($"  数量 × 保留2位小数成交单价: {entry.Quantity} × {entry.RoundedDealPrice:F2} = {roundedFDealAmount:F2}");
                    Console.WriteLine($"  行尾差值: {roundedOriginalAmount:F2} - {roundedFDealAmount:F2} = {entry.RowTailDifference:F2}");
                }
                Console.WriteLine();

                // 步骤4：整单尾差处理
                ProcessWholeOrderDifference(order);
            }
        }

        /// <summary>
        /// 整单尾差处理逻辑
        /// </summary>
        private static void ProcessWholeOrderDifference(OrderData order)
        {
            Console.WriteLine("整单尾差处理:");
            
            // 获取有效明细行（非套装、非赠品）
            var validDetails = order.Entries.Where(d => !d.IsSuite && !d.IsGiveaway).ToList();
            
            if (!validDetails.Any())
            {
                Console.WriteLine("  没有有效明细行，跳过处理");
                return;
            }

            // 计算总差异
            decimal totalNewAmount = validDetails.Sum(d => d.Quantity * d.RoundedDealPrice + d.RowTailDifference);
            decimal difference = totalNewAmount - order.TotalDealAmount;
            
            Console.WriteLine($"  有效明细行数量: {validDetails.Count}");
            Console.WriteLine($"  计算总金额: {totalNewAmount:F2}");
            Console.WriteLine($"  订单总金额: {order.TotalDealAmount:F2}");
            Console.WriteLine($"  整单差异: {totalNewAmount:F2} - {order.TotalDealAmount:F2} = {difference:F2}");

            if (difference == 0M)
            {
                Console.WriteLine("  整单差异为0，无需调整");
                return;
            }

            // 找出ID最大的有效明细行
            var lastDetail = validDetails.OrderByDescending(d => d.Id).First();
            
            Console.WriteLine($"  ID最大的明细行: {lastDetail.ProductName} (ID: {lastDetail.Id})");
            Console.WriteLine($"  调整前该行尾差: {lastDetail.RowTailDifference:F2}");
            
            // 更新尾差
            lastDetail.RowTailDifference += difference;
            
            Console.WriteLine($"  调整后该行尾差: {lastDetail.RowTailDifference:F2}");
            Console.WriteLine();

            // 输出最终结果
            Console.WriteLine("最终结果:");
            foreach (var entry in order.Entries)
            {
                Console.WriteLine($"  {entry.ProductName} (ID: {entry.Id}):");
                Console.WriteLine($"    原始成交单价: {entry.DealPrice:F3}");
                Console.WriteLine($"    保留2位小数成交单价: {entry.RoundedDealPrice:F2}");
                Console.WriteLine($"    行尾差值: {entry.RowTailDifference:F2}");
                Console.WriteLine($"    最终金额: {entry.Quantity * entry.RoundedDealPrice + entry.RowTailDifference:F2}");
            }
            
            decimal finalTotal = order.Entries.Sum(e => e.Quantity * e.RoundedDealPrice + e.RowTailDifference);
            Console.WriteLine($"  订单最终总金额: {finalTotal:F2}");
            Console.WriteLine($"  与原始总金额差异: {finalTotal - order.TotalDealAmount:F2}");
        }

        public static void Main(string[] args)
        {
            TestTailDifferenceScenario();
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }

    /// <summary>
    /// 订单数据模型
    /// </summary>
    public class OrderData
    {
        public string Id { get; set; }
        public string BillNo { get; set; }
        public string BillTypeName { get; set; }
        public bool IsResellerOrder { get; set; }
        public decimal TotalDealAmount { get; set; }
        public List<OrderEntry> Entries { get; set; } = new List<OrderEntry>();
    }

    /// <summary>
    /// 订单明细数据模型
    /// </summary>
    public class OrderEntry
    {
        public string Id { get; set; }
        public string ProductName { get; set; }
        public decimal RetailPrice { get; set; }        // 零售价 fprice
        public decimal DealPrice { get; set; }          // 成交单价 fdealprice
        public decimal Quantity { get; set; }           // 数量 fbizqty
        public decimal DealAmount { get; set; }         // 成交金额 fdealamount
        public bool IsSuite { get; set; }               // 是否套装 fsuiteflag
        public bool IsGiveaway { get; set; }            // 是否赠品 fisgiveaway
        
        // 计算字段
        public decimal RoundedDealPrice { get; set; }   // 保留2位小数成交单价 rounded_fdealprice
        public decimal RowTailDifference { get; set; }  // 行尾差值 frounded_fdirectdealamount
    }
}
