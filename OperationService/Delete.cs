using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.FormService;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 删除操作
    /// </summary>
    [InjectService("Delete")]
    public class Delete : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "删除";
            }
        }
        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_Delete;
            }
        }

        /// <summary>
        /// 删除后即刻反写
        /// </summary>
        protected override bool CanDoWriteback
        {
            get
            {
                return true;
            }
        }

        /// <summary>
        /// 业务表单
        /// </summary>
        protected HtmlForm HtmlForm => this.OperationContext.HtmlForm;

        /// <summary>
        /// 删除时自动添加数据集成同步服务
        /// </summary>
        /// <param name="lstOpServices"></param>
        protected override void PrepareBusinessService(List<FormServiceDesc> lstOpServices)
        {
            base.PrepareBusinessService(lstOpServices);

            var formServiceLoaders = this.Container.GetService<IEnumerable<IFormServiceLoader>>();
            if (formServiceLoaders != null && formServiceLoaders.Any())
            {
                foreach (var formServiceLoader in formServiceLoaders)
                {
                    var syncServiceInst = formServiceLoader.CreateSyncService(this.UserCtx, this.OperationContext.HtmlForm, Enu_SyncTimePoint.SyncWhenDelete, this.OperationContext.Option);
                    if (syncServiceInst != null)
                    {
                        lstOpServices.AddRange(syncServiceInst);
                    }
                }
            }
        }

        /// <summary>
        /// 执行删除逻辑
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Count() == 0)
            {
                return;
            }

            //更新业务进度
            var billNodeDefineService = this.Container.GetService<IBillNodeDefineService>();
            billNodeDefineService.Init(this.UserCtx, this.HtmlForm, this.PlugInProxy);
            billNodeDefineService.UpdateBillNodeDefine(dataEntities, true);

            //取消发布
            UnDistribute(dataEntities);

            //取消下载
            CancelDownload(dataEntities);

            //删除流程实例
            DeleteFlowInstance(dataEntities);

            var dm = this.OperationContext.Container.GetService<IDataManager>();
            dm.InitDbContext(UserCtx, this.OperationContext.HtmlForm.DataEntityType(UserCtx));
            var oids = from p in dataEntities
                       select p["Id"].ToString();
            dm.Delete(oids, null, this.OperationContext.Option);



            //DOTO 清除缓存？
            //CacheHelper.DeleteBizDataFromCache(this.OperationContext.UserContext, this.OperationContext.HtmlForm.Id, oids);

            this.OperationContext.Result.SrvData = new
            {
                //成功删除的业务对象id
                oids = oids.ToList()
            };
            this.OperationContext.Result.IsSuccess = true;
        }

        /// <summary>
        /// 取消发布
        /// </summary>
        /// <param name="dataEntities"></param>
        private void UnDistribute(DynamicObject[] dataEntities)
        {
            try
            {
                var fchaindataid = this.OperationContext.HtmlForm.GetField("fchaindataid");
                var fsendstatus = this.OperationContext.HtmlForm.GetField("fsendstatus");

                if (fchaindataid == null || fsendstatus == null)
                {
                    return;
                }

                var publishEntities = dataEntities.Where(x => Convert.ToString(x["fsendstatus"]) == "已发布" &&
                                                        string.IsNullOrWhiteSpace(Convert.ToString(x["fchaindataid"])) == false).ToList();

                if (publishEntities.Count <= 0)
                {
                    return;
                }

                // 模拟正常表单保存操作流程
                var result = this.Gateway.InvokeBillOperation(this.OperationContext.UserContext,
                    this.OperationContext.HtmlForm.Id,
                    publishEntities,
                    "UnDistribute",
                    new Dictionary<string, object>()
                    );
            }
            catch
            {
            }
        }

        /// <summary>
        /// 取消下载
        /// </summary>
        private void CancelDownload(DynamicObject[] dataEntities)
        {
            try
            {
                var fchaindataid = this.OperationContext.HtmlForm.GetField("ffromchaindataid");

                if (fchaindataid == null)
                {
                    return;
                }

                var cancelEntities = dataEntities.Where(x => string.IsNullOrWhiteSpace(Convert.ToString(x["ffromchaindataid"])) == false).ToList();

                if (cancelEntities.Count <= 0)
                {
                    return;
                }

                // 模拟正常表单保存操作流程
                var result = this.Gateway.InvokeBillOperation(this.OperationContext.UserContext,
                    this.OperationContext.HtmlForm.Id,
                    cancelEntities,
                    "CancelDownload",
                    new Dictionary<string, object>()
                    );
            }
            catch
            {
            }
        }

        /// <summary>
        /// 删除关联的流程实例
        /// </summary>
        /// <param name="dataEntities"></param>
        private void DeleteFlowInstance(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0) return;

            var flowInstanceField = this.HtmlForm.GetField("fflowinstanceid");
            if (flowInstanceField == null || !this.HtmlForm.ApprovalFlow) return;

            var bizPkids = dataEntities.Select(o => o["id"]).Distinct().ToList();
            if (bizPkids == null || bizPkids.Count < 1) return;

            var htmlForm = this.MetaModelService.LoadFormModel(this.UserCtx, "bpm_flowinstance");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.UserCtx, htmlForm.GetDynamicObjectType(this.UserCtx));

            var paramList = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.UserCtx.Company)
            };
            var sqlText = $"select distinct {htmlForm.BillPKFldName} from {htmlForm.BillHeadTableName} where fmainorgid=@fmainorgid and fbizbillpkid in('{string.Join("','", bizPkids)}')";
            var flowInstanceIds = this.DBService.ExecuteDynamicObject(this.UserCtx, sqlText, paramList)
                ?.Select(o => o[htmlForm.BillPKFldName])
                ?.ToList();
            if (flowInstanceIds != null && flowInstanceIds.Count > 0)
            {
                dm.Delete(flowInstanceIds);
            }
        }

        /// <summary>
        /// 处理删除操作通用校验规则
        /// </summary>
        /// <returns></returns>
        protected override List<IDataValidRule> PrepareValidationRules()
        {
            var rules = base.PrepareValidationRules();
            //添加通用的删除操作校验器
            var delRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_BillDeleteValidation);
            if (delRule != null)
            {
                delRule.Initialize(this.OperationContext.UserContext, "");
                rules.Add(delRule);
            }
            //是否预设数据的校验
            delRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_BDPrepareValidation);
            if (delRule != null)
            {
                delRule.Initialize(this.OperationContext.UserContext, "");
                rules.Add(delRule);
            }
            //添加基础资料的删除操作校验器
            delRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_BDDeleteValidation);
            if (delRule != null)
            {
                delRule.Initialize(this.OperationContext.UserContext, "");
                rules.Add(delRule);
            }
            return rules;
        }

    }
}
