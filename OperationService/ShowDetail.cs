using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 显示详情
    /// </summary>
    [InjectService("ShowDetail")]
    public class ShowDetail : AbstractOperationService
    {
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var showFormId = this.GetQueryOrSimpleParam<string>("formId");
            if (string.IsNullOrWhiteSpace(showFormId))
            {
                showFormId = this.OperationContext.HtmlForm.Id;
            }
            var pkIds = this.GetQueryOrSimpleParam<string>("pkIds")?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            var billNos = this.GetQueryOrSimpleParam<string>("billNos")?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            var userContext = this.OperationContext.UserContext;
            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(userContext, showFormId);
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));
            List<DynamicObject> showEntities = new List<DynamicObject>();

            if (pkIds != null && pkIds.Length > 0)
            {
                var pkEntities = dm.Select(pkIds).OfType<DynamicObject>().ToList();
                if (pkEntities != null && pkEntities.Count > 0)
                {
                    showEntities.AddRange(pkEntities);
                }
            }

            if (billNos != null && billNos.Length > 0)
            {
                var dataReader = userContext.GetPkIdDataReaderWithNumber(htmlForm, billNos);
                var noEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
                if (noEntities != null && noEntities.Count > 0)
                {
                    showEntities.AddRange(noEntities);
                }
            }

            if (showEntities == null || showEntities.Count <= 0)
            {
                return;
            }

            showEntities = showEntities.Distinct(x => Convert.ToString(x["id"])).ToList();
            var loadReferenceObjectManager = this.Container.GetService<LoadReferenceObjectManager>();
            loadReferenceObjectManager.Load(userContext, htmlForm.GetDynamicObjectType(userContext), showEntities.ToArray(), false);

            if (string.IsNullOrWhiteSpace(this.OperationContext.PageId) == false)
            {
                foreach (var showEntity in showEntities)
                {
                    var action = userContext.ShowSpecialForm(htmlForm,
                        showEntity,
                        false,
                        this.OperationContext.PageId,
                        this.OpenStyle,
                        Enu_DomainType.Bill,
                        new Dictionary<string, object>(),
                        formPara =>
                        {
                            formPara.Status = Enu_BillStatus.Modify;
                        });
                    this.OperationContext.Result.HtmlActions.Add(action);
                }
            }
            else
            {
                IUiDataConverter dataConverter = userContext.Container.GetService<IUiDataConverter>();
                var datas = new List<object>();
                var option = OperateOption.Create();
                foreach (var showEntity in showEntities)
                {

                    var uiData = dataConverter.CreateUIDataObject(userContext,
                                                                   htmlForm,
                                                                   showEntity,
                                                                   option)["uidata"];
                    datas.Add(uiData);
                }

                this.OperationContext.Result.SrvData = new Dictionary<string, object> { { "datas", datas } };
            }
        }
    }
}
