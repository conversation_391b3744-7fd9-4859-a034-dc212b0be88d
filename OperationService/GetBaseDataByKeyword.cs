using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Drivers;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace JieNor.Framework.AppService.OperationService
{
    [InjectService("getbasedata")]
    public class GetBaseDataByKeyword : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "查看";
            }
        }

        protected override string PermItem
        {
            get
            {
                return "";
            }
        }

        protected override bool IgnoreOpMessage
        {
            get
            {
                return true;
            }
        }

        /// <summary>
        /// 校验权限的表单标识
        /// </summary>
        protected override string PermFormId
        {
            get
            {
                var fieldKey = this.GetQueryOrSimpleParam<string>("fieldKey");
                var baseField = this.OperationContext.HtmlForm?.GetField(fieldKey ?? "") as HtmlBaseDataField;
                return baseField?.RefFormId ?? base.PermFormId;
            }
        }


        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            this.OperationContext.Result.IsSuccess = false;
             
            var fieldValue = this.GetQueryOrSimpleParam<string>("value");             
            if (fieldValue.IsNullOrEmptyOrWhiteSpace())
            {
                this.OperationContext.Result.IsSuccess = true;
                this.OperationContext.Result.SrvData = new JObject();
                return;
            }

            var fieldKey = this.GetQueryOrSimpleParam<string>("fieldKey");
            var baseField = this.OperationContext.HtmlForm?.GetField(fieldKey ?? "") as HtmlBaseDataField;
            if (baseField == null)
            {
                this.OperationContext.Result.IsSuccess = false;
                this.OperationContext.Result.SimpleMessage = "不明确的基础资料字段标识，无法查找明细信息！";
                return;
            }
             
            string strFilter = "";
            HtmlForm refFieldForm = baseField.RefHtmlForm(this.OperationContext.UserContext);
            var numberField = refFieldForm.GetNumberField();
            var nameField = refFieldForm.GetNameField();
            var mainorgidFld= refFieldForm.GetField ("fmainorgid");

            List<SqlParam> lstParas = new List<SqlParam>();
            if(refFieldForm.BillPKFldName != null)
            {
                strFilter += " {0} {1}=@{1}".Fmt(strFilter.IsNullOrEmptyOrWhiteSpace() ? "" : "or", refFieldForm.BillPKFldName);
                lstParas.Add(new SqlParam(refFieldForm.BillPKFldName, System.Data.DbType.String, fieldValue));
            }
            if (numberField != null)
            {
                strFilter += " {0} {1}=@{1}".Fmt(strFilter.IsNullOrEmptyOrWhiteSpace() ? "" : "or", numberField.Id);
                lstParas.Add(new SqlParam(numberField.Id, System.Data.DbType.String, fieldValue));                 
            }

            if (nameField != null)
            {
                strFilter += " {0} {1}=@{1}".Fmt(strFilter.IsNullOrEmptyOrWhiteSpace() ? "" : "or", nameField.Id);
                lstParas.Add(new SqlParam(nameField.Id, System.Data.DbType.String, fieldValue));
            }

            if (false == string.IsNullOrWhiteSpace(baseField.Filter))
            {
                strFilter = " {0} {1} ".Fmt(strFilter.IsNullOrEmptyOrWhiteSpace() ? string.Empty : $" ({strFilter}) and ", baseField.Filter);
            }
            if (!strFilter.IsNullOrEmptyOrWhiteSpace())
            {
                strFilter = $"({strFilter})";
            }
            var field = this.OperationContext.HtmlForm?.GetField(fieldKey ?? "");
            switch (field.ElementType)
            {
                case HtmlElementType.HtmlField_MulClassTypeDataField:
                    this.LoadMulClassTypeData(field, strFilter, lstParas);
                    return;
                case HtmlElementType.HtmlField_BaseDataEntryField:
                    this.LoadBaseDataEntry(field, strFilter, lstParas);
                    return;
                default:
                    break;
            }

            var strPkid = "";             
            var lookupMode = this.GetQueryOrSimpleParam<string>("lookupmode");
            if (lookupMode.EqualsIgnoreCase("byid"))
            {
                strPkid = fieldValue;
            }
            else
            {
                SqlBuilderParameter para = new SqlBuilderParameter(this.UserCtx, refFieldForm.Id);
                para.PageCount = 100;
                para.PageIndex = 1;
                para.NoIsolation = this.UserCtx.Company.EqualsIgnoreCase("0");
                para.SrcFormId = this.OperationContext.HtmlForm.Id;
                para.SrcFldId = fieldKey;
                var srcPara = this.GetQueryOrSimpleParam<string>("srcPara");
                if (!srcPara.IsNullOrEmptyOrWhiteSpace())
                {
                    para.SrcPara = srcPara.FromJson<Dictionary<string, string>>();
                }

                if (strFilter.IsNullOrEmptyOrWhiteSpace() == false)
                {
                    para.FilterString = " ( {0} ) ".Fmt(strFilter);
                }

                para.AddParameter(lstParas);

                para.AddSelectField("fid");
                para.AddSelectField(numberField?.Id );
                para.AddSelectField(nameField?.Id);
                para.AddSelectField(mainorgidFld == null ? " '' as fmainorgid" : mainorgidFld.Id);

                //列表数据
                var listQueryBuilder = this.OperationContext.Container.GetService<IListSqlBuilder>();
                var datas = listQueryBuilder.GetQueryDataEntity(this.UserCtx, para);                
                
                strPkid = datas.FirstOrDefault(f => this.UserCtx.Company.EqualsIgnoreCase(f["fmainorgid"]?.ToString()))?["fbillhead_id"].ToString();//优先取本组织的
                if(strPkid.IsNullOrEmptyOrWhiteSpace ())
                {
                    //本组织取不到，取总部组织的
                    strPkid = datas.FirstOrDefault(f => this.UserCtx.TopCompanyId.EqualsIgnoreCase(f["fmainorgid"]?.ToString()))?["fbillhead_id"].ToString();
                }

                //本组织还是取不到，则取公用的
                if (strPkid.IsNullOrEmptyOrWhiteSpace())
                { 
                    strPkid = datas.FirstOrDefault()?["fbillhead_id"].ToString();
                } 
            }

            DynamicObject baseDataObj = null;
            if (!strPkid.IsNullOrEmptyOrWhiteSpace())
            { 
                var dm = this.GetDataManager();
                dm.InitDbContext(this.UserCtx, refFieldForm.GetDynamicObjectType(this.OperationContext.UserContext));
                baseDataObj = dm.Select(strPkid) as DynamicObject;
            }

            PackData(baseField, refFieldForm, baseDataObj);
        }


        private void PackData(HtmlBaseDataField baseField, HtmlForm refFieldForm,   DynamicObject baseDataObj )
        {
            if(baseDataObj ==null )
            {
                return ;
            }

            this.Container.GetService<LoadReferenceObjectManager>()?.Load(this.UserCtx, baseDataObj.DynamicObjectType, baseDataObj, true);

            JObject jBaseObj = new JObject();
            var uiConvertService = this.Container.GetService<IUiDataConverter>();
            var refFieldKeys = this.GetQueryOrSimpleParam<string>("refFieldKeys")?.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);            
            if (refFieldKeys != null && refFieldKeys.Any() == true)
            {   
                foreach (var refFldKey in refFieldKeys)
                {
                    var refField = refFieldForm.GetField(refFldKey);
                    if (refField == null)
                    {
                        continue;
                    }

                    var jFldObj = uiConvertService.PackageFieldData(this.UserCtx, refFieldForm, refField, baseDataObj, "", false);
                    jBaseObj[refField.Id] = jFldObj;
                }
            }
            else
            {
                var numberField = refFieldForm.GetNumberField();
                var nameField = refFieldForm.GetNameField();

                jBaseObj["id"] = JToken.FromObject(baseDataObj["id"] ?? "");
                jBaseObj["fnumber"] = JToken.FromObject(numberField?.DynamicProperty.GetValue(baseDataObj) as string ?? "");
                jBaseObj["fname"] = JToken.FromObject(nameField?.DynamicProperty.GetValue(baseDataObj) as string
                    ?? numberField?.DynamicProperty.GetValue(baseDataObj) as string
                    ?? "");

                //打包单位特有的数据，比如：基本单位，舍入类型，换算率，精度
                uiConvertService.PackageUnitInfo(this.UserCtx, baseField, jBaseObj, baseDataObj);
                //打包物料单位明细信息
                uiConvertService.PackageMaterialUnitEntry(this.UserCtx, baseField, jBaseObj, baseDataObj);

                refFieldKeys = baseField.DisplayFldKeys.ToArray();
                foreach (var refFldKey in refFieldKeys)
                {
                    var refField = refFieldForm.GetField(refFldKey);
                    if (refField == null)
                    {
                        continue;
                    }

                    var jFldObj = uiConvertService.PackageFieldData(this.UserCtx, refFieldForm, refField, baseDataObj, "", false);
                    jBaseObj[refField.Id] = jFldObj;
                }
            }

            //打包基础资料颜色字段值
            var refColorField = baseField.GetRefColorField(this.UserCtx);
            if (refColorField != null)
            {
                var jColor = JToken.FromObject(refColorField?.DynamicProperty?.GetValue<string>(baseDataObj)?.Trim() ?? "");
                if (!jColor.IsNullOrEmptyOrWhiteSpace())
                {
                    jBaseObj["fcolor"] = jColor;
                }
            }

            this.OperationContext.Result.SrvData = jBaseObj;
            this.OperationContext.Result.IsSuccess = true;          
        }

        /// <summary>
        /// 加载多类别基础资料
        /// </summary>
        /// <param name="field"></param>
        /// <param name="strFilter"></param>
        /// <param name="lstParas"></param>
        private void LoadMulClassTypeData(HtmlField field, string strFilter, List<SqlParam> lstParas)
        {
            var mulClassTypeDataField = field as HtmlMulClassTypeDataField;
            if (mulClassTypeDataField.ControlFieldKey.IsNullOrEmptyOrWhiteSpace())
            {
                var controlField = this.OperationContext.HtmlForm?.GetField(mulClassTypeDataField.ControlFieldKey ?? "");
                if (controlField != null && controlField is HtmlMulClassTypeField)
                {
                    var mulClassTypeField = controlField as HtmlMulClassTypeField;
                    if (mulClassTypeField.DataViewName.IsNullOrEmptyOrWhiteSpace())
                    {
                        var ctlFieldVal = this.GetQueryOrSimpleParam<string>("ctlFieldVal", "");

                        var sqlText = $@"select top 1 fid,fnumber,fname from {mulClassTypeField.DataViewName} 
                                where (fmainorgid is null or fmainorgid=' ' or fmainorgid ='' or fmainorgid='0' or fmainorgid='{this.UserCtx.Company}' ) 
                                and ftypeformid=@ftypeformid and {strFilter}";

                        lstParas.Add(new SqlParam("ftypeformid", System.Data.DbType.String, ctlFieldVal));

                        using (var reader = this.DBService.ExecuteReader(this.OperationContext.UserContext, sqlText, lstParas))
                        {
                            if (reader.Read())
                            {
                                JObject jBaseObj = new JObject();
                                jBaseObj["id"] = reader.GetString("fid");
                                jBaseObj["fnumber"] = reader.GetString("fnumber");
                                jBaseObj["fname"] = reader.GetString("fname");
                                this.OperationContext.Result.SrvData = jBaseObj;
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 加载基础资料分录
        /// </summary>
        /// <param name="field"></param>
        /// <param name="strFilter"></param>
        /// <param name="lstParas"></param>
        private void LoadBaseDataEntry(HtmlField field, string strFilter, List<SqlParam> lstParas)
        {
            var baseDataEntryField = field as HtmlBaseDataEntryField;
            if (baseDataEntryField.ControlFieldKey.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            var controlField = this.OperationContext.HtmlForm?.GetField(baseDataEntryField.ControlFieldKey ?? "");
            if (controlField == null || !(controlField is HtmlBaseDataField))
            {
                return;
            }

            var baseDataField = controlField as HtmlBaseDataField;
            if (baseDataField.RefFormId.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            var controlFieldForm = this.MetaModelService?.LoadFormModel(this.OperationContext.UserContext, baseDataField.RefFormId);
            var ctlFieldVal = this.GetQueryOrSimpleParam<string>("ctlFieldVal", "");
            if (!ctlFieldVal.IsNullOrEmptyOrWhiteSpace())
            {
                strFilter += " and fpid=@fpid";
                lstParas.Add(new SqlParam("fpid", System.Data.DbType.String, ctlFieldVal));
            }
            var sqlText = $@"select top 1 fid,fnumber,fname from v_{controlFieldForm.BillHeadTableName.Substring(2)} 
                                where (fmainorgid is null or fmainorgid=' ' or fmainorgid ='' or fmainorgid='0' or fmainorgid='{this.UserCtx.Company}' ) 
                                and {strFilter}";

            using (var reader = this.DBService.ExecuteReader(this.OperationContext.UserContext, sqlText, lstParas))
            {
                if (reader.Read())
                {
                    JObject jBaseObj = new JObject();
                    jBaseObj["id"] = reader.GetString("fid");
                    jBaseObj["fnumber"] = reader.GetString("fnumber");
                    jBaseObj["fname"] = reader.GetString("fname");
                    this.OperationContext.Result.SrvData = jBaseObj;
                }
            } 
        }
    }
}
