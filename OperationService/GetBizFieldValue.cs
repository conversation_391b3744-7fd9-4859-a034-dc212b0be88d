using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 获取业务字段值：主要用于动态列类型业务字段
    /// </summary>
    [InjectService("GetBizFieldValue")]
    public class GetBizFieldValue : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName { get { return "查看"; } }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem { get { return PermConst.PermssionItem_View; } }

        /// <summary>
        /// 忽略基类操作消息
        /// </summary>
        protected override bool IgnoreOpMessage { get { return true; } }

        /// <summary>
        /// 实际操作服务执行过程
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var fieldSrcId = this.GetQueryOrSimpleParam<string>("fieldSrcId"); //基础资料 或者 辅助资料
            var fieldTypeId = this.GetQueryOrSimpleParam<string>("fieldTypeId"); //基础资料表单标识 或者 辅助资料类型主键
            var fieldValue = this.GetQueryOrSimpleParam<string>("fieldValue"); //基础资料主键 或者 辅助资料主键
            if (fieldSrcId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentNullException("参数 fieldSrcId 为空，请检查！");
            }
            if (fieldTypeId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentNullException("参数 fieldTypeId 为空，请检查！");
            }
            if (fieldValue.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentNullException("参数 fieldValue 为空，请检查！");
            }

            var dm = this.GetDataManager();
            var data = new JObject();

            switch (fieldSrcId.ToLower())
            {
                case "basedata":
                    var baseDataForm = this.MetaModelService.LoadFormModel(this.UserCtx, fieldTypeId);
                    dm.InitDbContext(this.UserCtx, baseDataForm.GetDynamicObjectType(this.UserCtx));
                    var baseDataObj = dm.Select(fieldValue) as DynamicObject;
                    data["id"] = JToken.FromObject(baseDataObj?["id"] ?? "");
                    data["fnumber"] = JToken.FromObject(baseDataObj?["fnumber"] ?? "");
                    data["fname"] = JToken.FromObject(baseDataObj?["fname"] ?? "");
                    break;

                case "enumdata":
                    var enumDataForm = this.MetaModelService.LoadFormModel(this.UserCtx, "bd_enum");
                    dm.InitDbContext(this.UserCtx, enumDataForm.GetDynamicObjectType(this.UserCtx));
                    var enumDataObj = dm.Select(fieldValue) as DynamicObject;
                    data["id"] = JToken.FromObject(enumDataObj?["id"] ?? "");
                    data["fnumber"] = JToken.FromObject(enumDataObj?["id"] ?? "");
                    data["fname"] = JToken.FromObject(enumDataObj?["fenumitem"] ?? "");
                    break;

                default:
                    break;
            }

            this.OperationContext.Result.SrvData = data;
            this.OperationContext.Result.IsSuccess = true;
        }
    }
}