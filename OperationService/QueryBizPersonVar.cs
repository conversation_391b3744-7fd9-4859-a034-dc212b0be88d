using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 获取某个业务对象上面指向人的字段（比如：业务员，设计师，部分负责人，部门内所有员工）
    /// </summary>
    [InjectService("QueryBizPersonVar")]
    public class QueryBizPersonVar : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName { get { return ""; } }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem { get { return ""; } }

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            string bizFormId = this.GetQueryOrSimpleParam("bizFormId", "");
            if (bizFormId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentException("参数 bizFormId 为空，请检查！");
            }

            OnCustomServiceEventArgs e = new OnCustomServiceEventArgs()
            {
                EventName = "onFilterQueryBizPersonVar",
                EventData = Tuple.Create(bizFormId),
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", e);
            if (e.Cancel && e.Result != null)
            {
                this.OperationContext.Result.SrvData = e.Result;
                return;
            }

            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();

            //部门对象标识
            var deptFormId = baseFormProvider.GetDeptFormObject(this.OperationContext.UserContext);

            //员工对象标识
            var staffFormId = baseFormProvider.GetStaffFormObject(this.OperationContext.UserContext);

            Dictionary<string, object> vars = new Dictionary<string, object>();

            // 当前业务对象
            var bizForm = this.MetaModelService.LoadFormModel(this.OperationContext.UserContext, bizFormId);
            vars.Add("currBizForm", new BizFormBizPersonVarModel
            {
                Id = bizForm.Id,
                Name = bizForm.Caption,
                Vars = LoadBizFormPersonVars(bizForm, deptFormId, staffFormId)
            });

            // 上游单据
            List<BizFormBizPersonVarModel> upStreamBizForms = new List<BizFormBizPersonVarModel>();
            List<string> upStreamFormIds = new List<string>();
            LoadUpStreamBizForms(bizForm, upStreamFormIds);
            foreach (var formId in upStreamFormIds)
            {
                var htmlForm = this.MetaModelService.LoadFormModel(this.OperationContext.UserContext, formId);
                string suffix = "$up_" + htmlForm.Id;

                var model = new BizFormBizPersonVarModel
                {
                    Id = "up_" + htmlForm.Id,
                    Name = htmlForm.Caption,
                    Vars = LoadBizFormPersonVars(htmlForm, deptFormId, staffFormId, suffix)
                };

                upStreamBizForms.Add(model);
            }
            vars.Add("upStreamBizForms", upStreamBizForms);

            // 下游单据 
            List<BizFormBizPersonVarModel> downStreamBizForms = new List<BizFormBizPersonVarModel>();
            List<string> downStreamFormIds = new List<string>();
            LoadDownStreamBizForms(bizForm, downStreamFormIds);
            foreach (var formId in downStreamFormIds)
            {
                var htmlForm = this.MetaModelService.LoadFormModel(this.OperationContext.UserContext, formId);
                string suffix = "$down_" + htmlForm.Id;

                var model = new BizFormBizPersonVarModel
                {
                    Id = "down_" + htmlForm.Id,
                    Name = htmlForm.Caption,
                    Vars = LoadBizFormPersonVars(htmlForm, deptFormId, staffFormId, suffix)
                };

                downStreamBizForms.Add(model);
            }
            vars.Add("downStreamBizForms", downStreamBizForms);

            // 原有逻辑
            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.SrvData = vars;
        }

        /// <summary>
        /// 加载上游单据
        /// </summary>
        /// <param name="bizForm"></param>
        /// <returns></returns>
        private void LoadUpStreamBizForms(HtmlForm bizForm, List<string> formIds)
        {
            foreach (var rule in this.MetaModelService.LoadConvertRuleByTargetFormId(this.OperationContext.UserContext, bizForm.Id))
            {
                if (formIds.Contains(rule.SourceFormId))
                {
                    continue;
                }

                var form = this.MetaModelService.LoadFormModel(this.OperationContext.UserContext, rule.SourceFormId);
                // 非单据
                if (form.ElementType != 1) continue;
                // 没有fsourcetype字段
                if (form.GetField("fsourcetype") == null) continue;

                formIds.Add(rule.SourceFormId);

                LoadUpStreamBizForms(form, formIds);
            }
        }

        /// <summary>
        /// 加载下游单据
        /// </summary>
        /// <param name="bizForm"></param>
        /// <returns></returns>
        private void LoadDownStreamBizForms(HtmlForm bizForm, List<string> formIds)
        {
            foreach (var rule in this.MetaModelService.LoadConvertRuleByFormId(this.OperationContext.UserContext, bizForm.Id))
            {
                if (formIds.Contains(rule.TargetFormId))
                {
                    continue;
                }

                var form = this.MetaModelService.LoadFormModel(this.OperationContext.UserContext, rule.TargetFormId);
                // 非单据
                if (form.ElementType != 1) continue;
                // 没有fsourcetype字段
                if (form.GetField("fsourcetype") == null) continue;

                formIds.Add(rule.TargetFormId);

                LoadDownStreamBizForms(form, formIds);
            }
        }

        /// <summary>
        /// 加载业务单据人员变量对象
        /// </summary>
        /// <param name="bizForm">业务单据</param>
        /// <param name="deptFormId">部门对象标识</param>
        /// <param name="staffFormId">员工对象标识</param>
        /// <param name="suffix">后缀</param>
        /// <returns></returns>
        private List<BizPersonVarModel> LoadBizFormPersonVars(HtmlForm bizForm, string deptFormId, string staffFormId, string suffix = "")
        {
            //返回给前端的字段信息列表
            List<BizPersonVarModel> bizPersonVars = new List<BizPersonVarModel>();

            var fields = bizForm.GetFieldList().Where(f => f is HtmlBaseDataField).OrderBy(f => f.ListTabIndex);
            foreach (var field in fields)
            {
                var _field = field as HtmlBaseDataField;

                if (_field.RefFormId.EqualsIgnoreCase(staffFormId) || _field.RefFormId.EqualsIgnoreCase("sec_user"))
                {
                    bizPersonVars.Add(new BizPersonVarModel
                    {
                        Id = _field.Id + suffix,
                        Name = $"{_field.Group}.{_field.Caption}",
                        AllowSelect = true,
                        Children = new List<BizPersonVarModel>
                        {
                            new BizPersonVarModel{ Id = $"{_field.Id}$deptmain{suffix}", Name = $"所属部门负责人", AllowSelect = true },
                            new BizPersonVarModel{ Id = $"{_field.Id}$deptall{suffix}", Name = $"所属部门所有员工", AllowSelect = true }
                        }
                    });
                }
                else if (_field.RefFormId.EqualsIgnoreCase(deptFormId))
                {
                    bizPersonVars.Add(new BizPersonVarModel
                    {
                        Id = _field.Id + suffix,
                        Name = $"{_field.Group}.{_field.Caption}",
                        AllowSelect = false,
                        Children = new List<BizPersonVarModel>
                        {
                            new BizPersonVarModel{ Id = $"{_field.Id}$main{suffix}", Name = $"负责人", AllowSelect = true },
                            new BizPersonVarModel{ Id = $"{_field.Id}$all{suffix}", Name = $"所有员工", AllowSelect = true }
                        }
                    });
                }
            }

            return bizPersonVars;
        }
    }
}