using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 加载选单时的来源表单
    /// </summary>
    [InjectService("QueryPullSource")]
    public class QueryPullSource : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName { get { return ""; } }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem { get { return ""; } }

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var convertRules = this.MetaModelService.LoadConvertRuleByTargetFormId(this.UserCtx, this.OperationContext.HtmlForm.Id);
            if (convertRules == null || convertRules.Count() <= 0)
            {
                this.OperationContext.Result.SimpleMessage = $"{this.OperationContext.HtmlForm.Caption}没有可选的源头单据，无法选单！";
                this.OperationContext.Result.IsSuccess = false;
                return;
            }

            //接收前端指定的源单
            var sourceFormIds = new List<string>();
            var sourceFormIdStr = this.GetQueryOrSimpleParam<string>("sourceFormId", "");
            if(!sourceFormIdStr.IsNullOrEmptyOrWhiteSpace())
            {
                sourceFormIds = sourceFormIdStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
            }
            //当前所在表单的单据类型Id
            var currentBillTypeId = this.GetQueryOrSimpleParam<string>("billTypeId", "");

            List<Dictionary<string, object>> cvtRules = new List<Dictionary<string, object>>();
            foreach (var convertRule in convertRules)
            {
                if (convertRule == null 
                    || convertRule.SourceFormId.IsNullOrEmptyOrWhiteSpace()
                    || convertRule.Visible == false) continue;

                //如果前端有指定的源单，则以指定的为准，但是前提是前端传递的这个源单必须在单据转换规则中是有效的
                var sourceFormId = convertRule.SourceFormId;
                if (sourceFormIds != null && sourceFormIds.Count > 0 && !sourceFormIds.Contains(sourceFormId)) continue;

                HtmlForm sourceForm = null;
                try
                {
                    sourceForm = this.MetaModelService.LoadFormModel(this.UserCtx, sourceFormId);
                }
                catch { }
                if (sourceForm == null) continue;

                //来源单分录内码，用于在选单时设置前置过滤条件（也就是已经被选过的源单明细，不能再次被选到）
                var sourceEntryId = "";
                if (!convertRule.ActiveEntityKey.EqualsIgnoreCase("fbillhead"))
                {
                    sourceEntryId = convertRule
                        ?.FieldMappings
                        ?.Where(o => o.SrcFieldId.EqualsIgnoreCase($"{convertRule.ActiveEntityKey}.id"))
                        ?.FirstOrDefault()?.Id ?? "";
                }

                //选单时参与过滤的字段
                var filterFields = convertRule
                    ?.FieldMappings
                    ?.Where(o =>
                    {
                        return o.AllowFilter
                            && o.MapType == (int)Enu_FieldMapType.Default
                            && this.OperationContext.HtmlForm.GetField(o.Id) != null
                            && sourceForm.GetField(o.SrcFieldId) != null;
                    })
                    ?.Select(o =>
                    {
                        return new { id = o.Id, srcFieldId = o.SrcFieldId };
                    });

                //源单单据类型字段
                var sourceBillTypeFldKey = "";
                var sourceBillTypeId = "";
                if (!currentBillTypeId.IsNullOrEmptyOrWhiteSpace())
                {
                    var sourceBillTypeFld = sourceForm.GetFieldList().FirstOrDefault(o => o is HtmlBillTypeField);
                    sourceBillTypeFldKey = sourceBillTypeFld?.Id ?? "";
                    var firstBillTypeMap = convertRule?.BillTypeMappings?.FirstOrDefault(o =>
                    {
                        return o.TargetBillTypeId.EqualsIgnoreCase(Convert.ToString(currentBillTypeId))
                            && !o.SourceBillTypeId.IsNullOrEmptyOrWhiteSpace();
                    });
                    sourceBillTypeId = firstBillTypeMap?.SourceBillTypeId ?? "";
                }

                cvtRules.Add(new Dictionary<string, object>
                {
                    { "ruleId", convertRule.Id },
                    { "sourceFormId", sourceForm.Id },
                    { "sourceFormCaption", sourceForm.Caption },
                    { "sourceNumberFldKey", sourceForm.NumberFldKey },
                    { "sourceBillTypeFldKey", sourceBillTypeFldKey },
                    { "sourceBillTypeId", sourceBillTypeId },
                    { "sourceEntryId", sourceEntryId },
                    { "activeEntityKey", convertRule.ActiveEntityKey },
                    { "filterString", convertRule.FilterString },
                    { "filterFields", filterFields }
                });
            }

            if (cvtRules.Count <= 0)
            {
                this.OperationContext.Result.SimpleMessage = $"{this.OperationContext.HtmlForm.Caption}没有可选的源头单据，无法选单！";
                this.OperationContext.Result.IsSuccess = false;
                return;
            }

            this.OperationContext.Result.SrvData = new { cvtRules = cvtRules };
            this.OperationContext.Result.IsSuccess = true;
        }
    }
}