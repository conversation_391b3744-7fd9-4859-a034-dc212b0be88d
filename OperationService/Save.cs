using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Linq;
using JieNor.Framework.MetaCore.Validator;
using System.Collections.Generic;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Interface.FormService;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.FileServer;
using System.Security.Cryptography;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using JieNor.Framework.MetaCore.FormOp.OpData;
using JieNor.Framework.AppService.Validation;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 保存操作
    /// </summary>
    [InjectService("Save")]
    public class Save : Draft
    {
        /// <summary>
        /// 保存即反写
        /// </summary>
        protected override bool CanDoWriteback
        {
            get
            {
                return true;
            }
        }

        protected override string OperationName
        {
            get
            {
                return "保存";
            }
        }

        /// <summary>
        /// 操作后状态值
        /// </summary>
        protected override string StatusValue
        {
            get
            {
                return "B";
            }
        }

        protected override bool AutoSaveData
        {
            get
            {
                if (this.OperationContext is ParameterOperationContext) return false;
                return base.AutoSaveData;
            }
        }

        /// <summary>
        /// 如果是暂存只需要将状态设置为创建
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null) return;

            var statusField = this.OperationContext.HtmlForm.GetField(this.OperationContext.HtmlForm.BizStatusFldKey) as HtmlBillStatusField;
            if (statusField == null) return;
            if (!statusField.CategoryFilter.EqualsIgnoreCase("数据状态")) return;
            foreach (var dataEntity in dataEntities)
            {
                var statusValue = statusField.DynamicProperty.GetValue<string>(dataEntity);
                //暂存时，将数据变化创建中状态
                if (statusValue.EqualsIgnoreCase("A"))
                {
                    statusField.DynamicProperty.SetValue(dataEntity, this.StatusValue);
                }
            }

            //填充和更新业务进度
            var billNodeDefineService = this.Container.GetService<IBillNodeDefineService>();
            billNodeDefineService.Init(this.UserCtx, this.HtmlForm, this.PlugInProxy);
            billNodeDefineService.FillBillNodeDefineInfos(dataEntities);
            billNodeDefineService.UpdateBillNodeDefine(dataEntities, false);
            updateBillType(dataEntities);

        }

        /// <summary>
        /// 更新单据类型：如果存在系统自建单据类型，则保存时更新为自建单据类型，因为存在一种情况，先打开业务单据新建页面，然后又去改预置单据类型保存生成了经销商自建的单据类型，
        /// 此时前面业务单据去保存会拿前端控件存的预置的单据类型id保存，界面刷新后单据类型会被渲染为空，此时做逻辑改造，此种情况去保存时判断有无自建单据类型，有的话则更新为自建单据类型。
        /// 历史BUG:28686,之前只处理销售合同的，现在全模块涉及单据类型的统一处理
        /// </summary>
        /// <param name="dataEntities"></param>
        private void updateBillType(DynamicObject[] dataEntities)
        {
            var billTypeService = this.Container.GetService<IBillTypeService>();
            var BillTypeField = this.HtmlForm.GetFieldList().FirstOrDefault(o => o is HtmlBillTypeField)?.FieldName;
            if (BillTypeField.IsNullOrEmptyOrWhiteSpace()) return;

            List<string> BillTypeIds = dataEntities.Select(o => Convert.ToString(o[BillTypeField])).ToList();
            
            if (BillTypeIds.IsNullOrEmpty() || BillTypeIds.Count() == 0) return;

            var change = false;
            var dyObjs = billTypeService.GetBillTypeIdMapInfo(this.UserCtx,   BillTypeIds);
            foreach (var dataEntitie in dataEntities) 
            {
                //如果在过程中产生了自建的单据类型，要取到其自建的单据类型
                var currBillTypeId = Convert.ToString(dataEntitie[BillTypeField]);
                if(dyObjs.ContainsKey (currBillTypeId) && !dyObjs[currBillTypeId].EqualsIgnoreCase (currBillTypeId))
                {
                    //更新单据类型。
                    dataEntitie[BillTypeField] = dyObjs[currBillTypeId];
                    change = true;
                }                 
            }
            if (change)
            {
                var ComboDatas = LoadBillTypeComboDatas(UserCtx, this.HtmlForm, BillTypeField);
                if (ComboDatas != null)
                {
                    this.OperationContext.Result.SimpleData["ComboDatas"] = ComboDatas.ToJson();
                }
            }
        }

        /// <summary>
        /// 加载下拉框数据源
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="activeForm"></param>
        /// <param name="formShowPara"></param>
        private static Dictionary<string, object> LoadBillTypeComboDatas(UserContext userCtx, HtmlForm activeForm,string BillTypeField)
        {
            //加载下拉框数据源
            var comboDataService = userCtx.Container.GetService<IComboDataService>();
            var comboDatas = comboDataService.GetBillTypeComboDatas(userCtx, activeForm.Id);
            if (comboDatas != null)
            {
                //此处转成动态对象，避免序列化多余的类型字符串到前端
                Dictionary<string, object> uiComboData = new Dictionary<string, object>();
                foreach (var key in comboDatas.Keys)
                {
                    if (!BillTypeField.EqualsIgnoreCase(key)) continue;
                    uiComboData[key] = comboDatas[key].Select(o => new
                    {
                        Id = o.Id,
                        Number = o.Number,
                        Name = o.Name,
                        Disable = o.Disable,
                        IsPrepare = o.IsPreset,
                    }).ToList();
                }
                return uiComboData;
            }
            return null;
        }
        /// <summary>
        /// 自动审核，自动提交，不需要和保存在一个事务里
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void AfterExecute(ref DynamicObject[] dataEntities)
        {
            base.AfterExecute(ref dataEntities);

            //保存参数设置信息（该逻辑针对 parameter 领域模型的表单保存）
            if (this.OperationContext is ParameterOperationContext)
                this.SaveParameter(dataEntities);
             
            //做参数判断
            bool isAutoSubmitAfterSave = this.GetQueryOrSimpleParam<bool>("autosubmit");
            bool isAutoAuditAfterSave = this.GetQueryOrSimpleParam<bool>("autoaudit");
             
            if (isAutoSubmitAfterSave)
            {
                this.AutoSubmitBill(dataEntities);
            }

            if (isAutoAuditAfterSave)
            {
                this.AutoAuditBill(dataEntities);
            }

        }

        /// <summary>
        /// 保存参数设置信息（该逻辑针对 parameter 领域模型的表单保存）
        /// </summary>
        /// <param name="dataEntities"></param>
        private void SaveParameter(DynamicObject[] dataEntities)
        {
            if (dataEntities == null) return;
            var paraDataObj = dataEntities.FirstOrDefault();
            if (paraDataObj == null) return;

            //加载引用数据
            this.Container.GetService<LoadReferenceObjectManager>()?.Load(
                this.UserCtx,
                this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx),
                paraDataObj,
                false);
            //如果是系统参数保存设置，则发请求到文件服务器更新图片压缩参数
            UpdateFileServerImagePars(dataEntities, this.UserCtx);

            //打包成前端的数据结构
            var uiDataConvert = this.Container.GetService<IUiDataConverter>();
            var paraData = uiDataConvert.CreateUIDataObject(this.UserCtx, this.OperationContext.HtmlForm, paraDataObj, this.OperationContext.Option);

            //获得详细审计日志
            var auditDetail = this.CreateBillDetailLog(paraDataObj);
            if (auditDetail != null && (auditDetail.Items == null || auditDetail.Items.Count == 0))
            {
                auditDetail = null;
            }

            //保存参数设置信息
            var spService = this.Container.GetService<ISystemProfile>();
            spService.CreateOrUpdateProfile(this.UserCtx, "fw", $"{this.OperationContext.HtmlForm.Id}_parameter", paraData["uidata"]?.ToJson());

            //创建日志对象
            var logService = this.Container.GetService<ILogService>();
            if (logService == null) return;

            var detailValue = auditDetail?.ToJson() ?? "执行了【保存设置】操作！";
            if (detailValue.Length > 1000)
            {
                detailValue = detailValue.Substring(0, 1000);
            }
            detailValue = RepalceValueDisPlay(detailValue);

            //处理审计日志字段展示效果。
            var lstLogEntryObjs = new LogEntry()
            {
                BillFormId = this.OperationContext.HtmlForm.Id,
                OpCode = this.OperationContext.OperationNo,
                Level = Enu_LogLevel.Info.ToString(),
                Category = 5,
                Content = detailValue,
                BillIds = this.UserCtx.UserName,
                BillNos = this.UserCtx.UserName,
                Detail = "",
                LogType = Enu_LogType.RecordType_03,
                DebugData = this.OperationContext.Result?.ToJson() ?? "",
                OpName = "保存设置"
            };

            logService.WriteLog(this.UserCtx, lstLogEntryObjs);
        }

        private string RepalceValueDisPlay(string detailValue)
        {
            return detailValue.Replace("oldValue", "更新前").Replace("oldDisplayValue", "更新前展示").Replace("newValue", "更新后").Replace("newDisplayValue", "更新后展示");
        }

        /// <summary>
        /// 发请求到文件服务器更新图片压缩参数
        /// </summary>
        /// <param name="dataEntities"></param>
        private void UpdateFileServerImagePars(DynamicObject[] dataEntities, UserContext ctx)
        {

            var dataentry = dataEntities[0];

            //判断是否系统参数表单
            if (!this.OperationContext.HtmlForm.Id.EqualsIgnoreCase("sys_systemparam"))
            {
                return;
            }
            //判断系统参数表单是否存在fimagepercentage字段
            var properties = dataentry?.DynamicObjectType?.Properties?.OfType<DynamicProperty>();
            var refFieldProperty = properties?.FirstOrDefault(p => p.Name.EqualsIgnoreCase("fimagepercentage"));
            if (refFieldProperty == null) return;

            var fimagepercentage = Convert.ToString(dataentry["fimagepercentage"]);
            if (fimagepercentage.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            var fmainorgid = ctx.Company;

            //当前站点的文件服务器地址
            var fileServerUrl = this.GetFileServerUrl();
            if (fileServerUrl.IsNullOrEmptyOrWhiteSpace()) return;
            var dtticks = DateTime.Now.Ticks;
            var key = Md5Encrypt("jienor_ydj" + dtticks);
            //向当前站点文件服务器发送文件协同申请
            var currFileServerResponse = this.Gateway.Invoke<FileSetImgParDTOResponse>(ctx,
                new TargetServer()
                {
                    // Host = fileServerUrl+ "FileInfo/SetImagePar?value="+ fimagepercentage+ "&&mainorgid="+ fmainorgid+"&&key="+key+"&&dtticks="+dtticks.ToString(),
                    Host = fileServerUrl,
                    TokenId = "",
                    Headers = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
                    {
                        { "reqType", "FileCoordinate" },
                        { "appCode", "66666" },
                        { "timeStamp", DateTime.Now.ToString("yyyyMMddHHmmssfff") }
                    }
                },
                new FileSetImgParDTO()
                {
                    value = fimagepercentage,
                    mainorgid = fmainorgid,
                    key = key,
                    dtticks = dtticks.ToString()
                }, Enu_HttpMethod.Get);
        }

        private void AutoSubmitBill(DynamicObject[] dataEntities)
        {
            var statusField = this.OperationContext.HtmlForm.GetField(this.OperationContext.HtmlForm.BizStatusFldKey);
            DynamicObject[] validDataObjs = dataEntities;
            if (statusField != null)
            {
                var statusName1 = Enum.GetName(typeof(BillStatus), BillStatus.B);
                var statusName2 = Enum.GetName(typeof(BillStatus), BillStatus.C);
                validDataObjs = dataEntities.Where(o => statusField.DynamicProperty.GetValue<string>(o).EqualsIgnoreCase(statusName1)
                    || statusField.DynamicProperty.GetValue<string>(o).EqualsIgnoreCase(statusName2))
                .ToArray();
            }
            if (validDataObjs.Any() == false) return;

            var option = new Dictionary<string, object> { { "saveAutoSubmit", true } };
            var result = this.Gateway.InvokeBillOperation(this.UserCtx, this.OperationContext.HtmlForm.Id, validDataObjs, "submitflow", option);
            if (result != null)
                this.OperationContext.Result.MergeResult(result);
        }

        private void AutoAuditBill(DynamicObject[] dataEntities)
        {
            this.AutoSubmitBill(dataEntities);

            var statusField = this.OperationContext.HtmlForm.GetField(this.OperationContext.HtmlForm.BizStatusFldKey);
            DynamicObject[] validDataObjs = dataEntities;
            if (statusField != null)
            {
                var statusName1 = Enum.GetName(typeof(BillStatus), BillStatus.D); 
                validDataObjs = dataEntities.Where(o => statusField.DynamicProperty.GetValue<string>(o).EqualsIgnoreCase(statusName1) )
                .ToArray();
            }
            if (validDataObjs.Any() == false) return;

            var option = new Dictionary<string, object> { { "saveAutoAudit", true } };
            var result = this.Gateway.InvokeBillOperation(this.UserCtx, this.OperationContext.HtmlForm.Id, validDataObjs, "audit", option);
            if (result != null)
                this.OperationContext.Result.MergeResult(result);
        }

        /// <summary>
        /// 获取系统参数详细修改日志
        /// </summary>
        /// <param name="newData"></param>
        private BillAuditDetailObject CreateBillDetailLog(DynamicObject newData)
        {
            //保存参数设置信息
            var spService = this.Container.GetService<ISystemProfile>();

            BillAuditDetailObject auditObj = new BillAuditDetailObject();
            auditObj.Summary = $"本次保存引起的详细数据变化如下：";

            var oldData = spService.GetSystemParameter(this.UserCtx, this.OperationContext.HtmlForm.Id);
            if (oldData.IsNullOrEmpty()) return auditObj;

            var flds = this.OperationContext.HtmlForm.GetFieldList().ToList();
            var headFields = this.OperationContext.HtmlForm.HeadEntity.GetFieldList(flds).ToList();
            if (headFields != null && headFields.Count >= 0)
            {
                foreach (var fld in headFields)
                {
                    if (!newData.DynamicObjectType.Properties.ContainsKey(fld.PropertyName)
                        || !oldData.DynamicObjectType.Properties.ContainsKey(fld.PropertyName)) continue;

                    if (!Convert.ToString(oldData[fld.PropertyName]).Equals(Convert.ToString(newData[fld.PropertyName])))
                    {
                        string oldDisplayValue = null, newDisplayValue = null;
                        auditObj.Items.Add(new BillAuditDetailRow()
                        {
                            Id = fld.Id,
                            Name = fld.Caption,
                            OldValue = Convert.ToString(oldData[fld.PropertyName]),
                            OldDisplayValue = oldDisplayValue,
                            NewValue = Convert.ToString(newData[fld.PropertyName]),
                            NewDisplayValue = newDisplayValue,
                        });
                    }
                }
            }

            foreach (var entry in this.OperationContext.HtmlForm.EntryList)
            {
                var oldEntities = oldData[entry.Id] as DynamicObjectCollection;
                var newEntities = newData[entry.Id] as DynamicObjectCollection;
                var entryFields = entry.GetFieldList(flds).ToList();
                foreach (var newEntity in newEntities)
                {
                    var oldEntry = oldEntities.FirstOrDefault(x => Convert.ToString(x["id"]) == Convert.ToString(newEntity["id"]));
                    foreach (var fld in entryFields)
                    {
                        if (oldEntry == null || newEntity == null) continue;

                        if (!oldEntry.DynamicObjectType.Properties.ContainsKey(fld.PropertyName)
                        || !newEntity.DynamicObjectType.Properties.ContainsKey(fld.PropertyName)) continue;

                        if (!Convert.ToString(oldEntry[fld.PropertyName]).Equals(Convert.ToString(newEntity[fld.PropertyName])))
                        {
                            var row = newEntity.DynamicObjectType.Properties.Contains("fseq") ? Convert.ToInt32(newEntity?["fseq"]) : 0;
                            string oldDisplayValue = null, newDisplayValue = null;

                            auditObj.Items.Add(new BillAuditDetailRow()
                            {
                                Id = fld.Id,
                                Row = row,
                                Name = fld.Caption,
                                OldValue = Convert.ToString(oldEntry[fld.PropertyName]),
                                OldDisplayValue = oldDisplayValue,
                                NewValue = Convert.ToString(newEntity[fld.PropertyName]),
                                NewDisplayValue = newDisplayValue,
                            });
                        }
                    }
                }
            }
            return auditObj;
        }

        protected override List<IDataValidRule> PrepareValidationRules()
        {
            var rules = base.PrepareValidationRules();

            //文本字段长度校验
            rules.Add(new TxtFeldLengthValidation());

            foreach (var field in this.OperationContext.HtmlForm.GetFieldList())
            {
                rules.AddRange(field.GetValidationRules());
            }

            //增加单据类型中配置为必须关联生成的校验逻辑处理
            var billTypeField = this.OperationContext.HtmlForm.GetBillTypeField();
            if (billTypeField != null)
            {
                var billTypeValidateRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_CreateBillByPush);
                if (billTypeValidateRule != null)
                {
                    rules.Add(billTypeValidateRule);
                }
            }

            var mustRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_MustInput);
            if (mustRule != null)
            {
                mustRule.Initialize(this.OperationContext.UserContext, "Save");
                rules.Add(mustRule);
            }

            return rules;
        }

        /// <summary>
        /// 处理集成服务调用
        /// </summary>
        /// <param name="lstOpServices"></param>
        protected override void PrepareBusinessService(List<FormServiceDesc> lstOpServices)
        {
            base.PrepareBusinessService(lstOpServices);

            var formServiceLoaders = this.Container.GetService<IEnumerable<IFormServiceLoader>>();
            if (formServiceLoaders != null && formServiceLoaders.Any())
            {
                foreach (var formServiceLoader in formServiceLoaders)
                {
                    var syncServiceInst = formServiceLoader.CreateSyncService(this.UserCtx, this.OperationContext.HtmlForm, Enu_SyncTimePoint.SyncWhenSave, this.OperationContext.Option);
                    if (syncServiceInst != null)
                    {
                        lstOpServices.AddRange(syncServiceInst);
                    }
                }
            }
        }

        #region MD5加密
        /// <summary>     
        /// MD5加密     
        /// </summary>     
        /// <param name="strSource">需要加密的字符串</param>     
        /// <returns>MD5加密后的字符串</returns>     
        public static string Md5Encrypt(string strSource)
        {
            //把字符串放到byte数组中     
            byte[] bytIn = System.Text.Encoding.Default.GetBytes(strSource);
            //建立加密对象的密钥和偏移量             
            byte[] iv = { 102, 16, 93, 156, 78, 4, 218, 32 };//定义偏移量     
            byte[] key = { 55, 103, 246, 79, 36, 99, 167, 3 };//定义密钥     
            //实例DES加密类     
            DESCryptoServiceProvider mobjCryptoService = new DESCryptoServiceProvider();
            mobjCryptoService.Key = iv;
            mobjCryptoService.IV = key;
            ICryptoTransform encrypto = mobjCryptoService.CreateEncryptor();
            //实例MemoryStream流加密密文件     
            System.IO.MemoryStream ms = new System.IO.MemoryStream();
            CryptoStream cs = new CryptoStream(ms, encrypto, CryptoStreamMode.Write);
            cs.Write(bytIn, 0, bytIn.Length);
            cs.FlushFinalBlock();
            return System.Convert.ToBase64String(ms.ToArray());
        }
        #endregion
    }
}
