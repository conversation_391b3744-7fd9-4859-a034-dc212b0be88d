using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Consts;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.Report;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.Framework.AppService.OperationService.Report
{
    /// <summary>
    /// 获取报表模型摘要操作
    /// </summary>
    [InjectService("viewrpt")]
    public class ReportInit : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "报表摘要";
            }
        }

        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_View;
            }
        }

        protected ReportOperationContext ReportOperationContext
        {
            get
            {
                return this.OperationContext as ReportOperationContext;
            }
        }

        /// <summary>
        /// 报表表单
        /// </summary>
        protected HtmlForm HtmlForm { get { return this.OperationContext.HtmlForm; } }

        /// <summary>
        /// 初始化时对报表概要信息进行回传
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void InitializeOperationDataEntities(ref DynamicObject[] dataEntities)
        {

        }

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (this.ReportOperationContext.IsNullOrEmpty())
            {
                throw new BusinessException("本操作只支持报表的领域模型调用！");
            }
            if (this.ReportOperationContext.ReportTemplate.IsNullOrEmpty())
            {
                throw new BusinessException($"报表配置文件没有配置，请检查:{this.HtmlForm.Id}.rpt.json！");
            }
            if (this.ReportOperationContext.ReportTemplate.service.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"报表配置文件({this.HtmlForm.Id}.rpt.json)中没有配置数据源（service属性）信息！");
            }

            this.ReportOperationContext.Option.SetVariableValue("_plugInProxy_", this.PlugInProxy);
            var rptService = this.Container.GetAllServiceByMeta<IReportDataService>("formId", this.ReportOperationContext.ReportTemplate.service)
                .OrderByDescending(o => o.Priority)
                .FirstOrDefault();

            var rptGridModel = rptService?.GetRptGridModel(this.UserCtx, this.ReportOperationContext);

            //加载模型中配置的字段默认值
            var dataEntity = new DynamicObject(this.HtmlForm.GetDynamicObjectType(this.UserCtx));
            var defCalulator = this.Container.GetService<IDefaultValueCalculator>();
            defCalulator.Execute(this.UserCtx, this.HtmlForm, new DynamicObject[] { dataEntity });

            var uiConverter = this.Container.GetService<IUiDataConverter>();
            var billJsonData = uiConverter.CreateUIDataObject(this.UserCtx, this.HtmlForm, dataEntity);

            //设置页面打开方式（如果前端有传递，则用传递的方式打开，否则按默认的方式打开）
            var action = this.OperationContext.UserContext.ShowForm(this.OperationContext.HtmlForm,
                "",
                this.OperationContext.PageId,
                Enu_DomainType.Report,
                this.OpenStyle,
                this.GlobalParameter,
                (formPara) =>
                {
                    formPara.UiData = billJsonData.GetJsonValue<JObject>("uiData", new JObject());

                    (formPara as ReportShowParameter).RptModelDesc = rptGridModel;

                    this.SetFormCaption(formPara);

                    var dctShowPara = this.OperationContext.Option.GetFormShowParameter();
                    if (dctShowPara != null)
                    {
                        formPara.CustomParameter.Merge(dctShowPara);
                    }
                });

            this.OperationContext.Result.HtmlActions.Add(action);

            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.IsShowMessage = true;
        }
    }
}