using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 查询任务进度操作实现
    /// </summary>
    [InjectService("querytask")]
    public class QueryTaskProgress : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return "查询任务进度";
            }
        }

        /// <summary>
        /// 不验权
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return "";
            }
        }

        /// <summary>
        /// 忽略操作消息
        /// </summary>
        protected override bool IgnoreOpMessage
        {
            get
            {
                return true;
            }
        }

        /// <summary>
        /// 提供任务进度查询接口
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var taskId = this.GetQueryOrSimpleParam<string>("taskid");
            var progress = this.TaskProgressService?.GetTaskProgress(this.UserCtx, taskId);
            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.SrvData = progress;
        }
    }
}
