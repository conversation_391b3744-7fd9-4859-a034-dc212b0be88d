using JieNor.Framework.DataTransferObject.IM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.IM
{
    /// <summary>
    /// 消息代理基类接口
    /// </summary>
    public interface IMessageClientProxyBase
    {
        /// <summary>
        /// 发送系统通知消息
        /// </summary>
        /// <param name="code"></param>
        /// <param name="message"></param>
        void onRecvSystemMessage(string code, IEnumerable<IMSimpleMessage> message);

        /// <summary>
        /// 发送通用业务消息
        /// </summary>
        /// <param name="callId"></param>
        /// <param name="msgHeadList"></param>
        void onRecvBusinessMessage(string callId, IEnumerable<IMMessageHead> msgHeadList);

        /// <summary>
        /// 错误消息传递接口
        /// </summary>
        /// <param name="errCode"></param>
        /// <param name="error"></param>
        void onRecvError(string errCode, Exception error);

        /// <summary>
        /// 登录失败专用接口
        /// </summary>
        /// <param name="errCode"></param>
        /// <param name="message"></param>
        void onLoginFailed(int errCode, string message);

        /// <summary>
        /// 发送简单提示消息
        /// </summary>
        /// <param name="type">对话框提示类型：info,warn,error</param>
        /// <param name="message"></param>
        void onRecvSimpleMessage(string type, string message);

        /// <summary>
        /// 回发正常调用的响应数据
        /// </summary>
        /// <param name="callId"></param>
        /// <param name="resp"></param>
        void onRecvCallResponse(string callId, object resp);

        /// <summary>
        /// 心跳客户端接口
        /// </summary>
        /// <param name="code"></param>
        /// <param name="content"></param>
        void onHeartBeat(string code, string content);
    }
}
