using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormModel.List;
using System.ComponentModel;
using JieNor.Framework.Meta.Designer;

namespace JieNor.Framework.Interface.BizTask
{
    /// <summary>
    /// 后台任务示例：员工生日提醒任务
    /// 后台任务开发要点：
    ///     1、TaskSvrIdAttribute 对应后台任务id，执行时根据这个获取对应的后台任务服务插件
    ///     2、所有后台服务插件继承自 AbstractTaskPlugin
    /// </summary>
    [InjectService(AliasName ="Task")]
    [TaskSvrId("fw.common.task")]
    [Caption("未知")]
    [Browsable(false)]
    public   class AbstractCommonTaskPlugin : AbstractScheduleWorker
    {
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        protected override async Task DoExecute()
        {
            await Task.Run(() =>
           {
               List<DynamicObject> listData = GetTaskCondition(this.UserContext, TaskObject.Id);
               this.Result.SrvData = listData;
           });
        }


        /// <summary>
        /// 返回预警执行条件的数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        public List<DynamicObject> GetTaskCondition(UserContext ctx, string taskId)
        {
            var metatask = ctx.Container.GetService<IMetaModelService>().LoadFormModel(ctx, "bas_task");
            var _taskdm = ctx.GetDefaultDataManager(metatask);
            DynamicObject dytask = _taskdm.Select(taskId) as DynamicObject;//查询出预警设置的条件
            if (dytask.IsNullOrEmptyOrWhiteSpace() || dytask["fcondition"].IsNullOrEmptyOrWhiteSpace())
            {
                throw new Exception("未定义预警任务");
            }

            var filter = Convert.ToString(dytask["fcondition"]);

            //根据预警设置的条件，查询对应业务对象的数据
            SqlBuilderParameter para = new SqlBuilderParameter(this.UserContext, dytask["fworkobject"].ToString());
            para.PageCount = 99999;
            if (!filter.IsNullOrEmptyOrWhiteSpace())
            {
                para.FilterString = " ( {0} ) ".Fmt(filter);
            }
            List<string> fieldKey = GetTmplFieldKeys(taskId);
            foreach (var item in fieldKey)
            {
                para.SelectedFieldKeys.Add(item);
            }
            para.SelectedFieldKeys.Add("fcreatorid");
            para.SelectedFieldKeys.Add("fid");
            QueryObject queryObject = QueryService.BuilQueryObject(para);
            var dbSvc = this.UserContext.Container.GetService<IDBService>();
            List<DynamicObject> listData = dbSvc.ExecuteDynamicObject(this.UserContext, queryObject.SqlNoPage, para.DynamicParams).ToList();//, para.DynamicParams

            return listData;
        }


        /// <summary>
        /// 获取模版中配置内容的字段名称
        /// </summary>
        /// <param name="taskId"></param>
        /// <returns></returns>
        private List<string> GetTmplFieldKeys(string taskId)
        {
            List<string> fieldKey = new List<string>();
            var metaModel = this.UserContext.Container.GetService<IMetaModelService>().LoadFormModel(this.UserContext, "sys_setupcontent");
            var _setupcontentdm = this.UserContext.GetDefaultDataManager(metaModel);
            DynamicObject dy = _setupcontentdm.SelectBy("fworkobject='{0}' and fmainorgid='{1}'".Fmt(TaskConst.HtmlForm_TaskInfo, this.UserContext.Company)).OfType<DynamicObject>().FirstOrDefault();
            if (dy.IsNullOrEmptyOrWhiteSpace())
            {
                return fieldKey;
            }
            DynamicObjectCollection entitys = dy["fentity"] as DynamicObjectCollection;
            foreach (DynamicObject entity in entitys)
            {
                if (entity["fcontent"].IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                JObject jObj = JObject.Parse(entity["fcontent"].ToString());
                if (jObj["opName"]["id"].ToString() == taskId)
                {
                    JObject content = JObject.Parse(jObj["content"].ToString());
                    List<string> titleFieldKey = GetRichKeys(content["ftitle"].ToString());
                    List<string> BodyFieldKey = GetRichKeys(content["fcontent"].ToString());
                    foreach (var item in titleFieldKey)
                    {
                        if (!fieldKey.Contains(item))
                        {
                            fieldKey.Add(item);
                        }
                    }
                    foreach (var item in BodyFieldKey)
                    {
                        if (!fieldKey.Contains(item))
                        {
                            fieldKey.Add(item);
                        }
                    }
                    continue;
                }
            }


            return fieldKey;
        }


        /// <summary>
        /// 解析出内容中的字段名称
        /// </summary>
        /// <param name="content"></param>
        /// <returns></returns>
        private List<string> GetRichKeys(string content)
        {
            List<string> fieldKey = new List<string>();
            int beginIndex = 0;
            for (int i = 0; i < content.Length; i++)
            {
                if (content[i] != '{')
                {
                    continue;
                }
                int charIndex = content.IndexOf('}', i) - i + 1;
                string fileid = content.Substring(i, charIndex).Trim().Replace('}',' ').Replace('{',' ');//截取出当前需要替换的字段，并且去掉空格
                string[] fileids = fileid.Split('.');
                string fileidx=string.Empty;
                for (int j = 1; j < fileids.Length; j++)
                {
                    fileidx += fileids[j] + ".";
                }
                //string fileidx = fileid.Split('.')[1].Replace('}', ' ').Trim();
                i = fileid.Length - 1 + i;
                beginIndex++;
                fieldKey.Add(fileidx.Substring(0, fileidx.Length - 1));
            }
            return fieldKey;
        }



    }


}
