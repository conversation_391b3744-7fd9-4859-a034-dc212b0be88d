SET BUILD=Debug
REM SET BUILD=Release
REM COPY ..\lib\%BUILD%\JieNor.Framework.dll ..\src\JieNor.AMS.YDJ.Store.Web\bin\

REM COPY ..\..\..\..\..\fw\trunk\lib\%BUILD%\*.dll ..\lib\%BUILD%\
REM COPY ..\..\..\..\..\fw\trunk\lib\%BUILD%\*.xml ..\lib\%BUILD%\
REM COPY ..\..\..\..\..\fw\trunk\lib\%BUILD%\*.config ..\lib\%BUILD%\
REM COPY ..\..\..\..\..\fw\trunk\lib\%BUILD%\*.txt ..\lib\%BUILD%\

REM copy the platform website
XCOPY ..\..\..\..\fw\trunk\src2\JieNor.Framework.Web ..\src\JieNor.AMS.YDJ.Store.Web /exclude:fwexclude.txt /y /s /e
REM XCOPY ..\lib\x86 ..\src\JieNor.AMS.YDJ.Store.Web\bin\ /y /s /e